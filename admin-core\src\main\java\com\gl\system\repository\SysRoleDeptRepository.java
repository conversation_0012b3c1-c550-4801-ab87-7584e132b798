package com.gl.system.repository;

import com.gl.system.entity.SysRoleDept;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SysRoleDeptRepository extends JpaRepository<SysRoleDept, Long>, JpaSpecificationExecutor<SysRoleDept> {
    @Transactional
    void deleteByRoleId(Long roleId);

    List<SysRoleDept> findByRoleIdIn(List<Long> roleIds);
}
