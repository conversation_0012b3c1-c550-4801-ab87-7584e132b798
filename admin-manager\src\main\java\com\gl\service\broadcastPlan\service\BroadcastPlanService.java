package com.gl.service.broadcastPlan.service;

import cn.hutool.core.util.ObjectUtil;
import com.gl.commons.enums.UserTypeEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.broadcastPlan.entity.BroadcastPlan;
import com.gl.service.broadcastPlan.entity.BroadcastPlanVoiceWorkRef;
import com.gl.service.broadcastPlan.repository.BroadcastPlanRepository;
import com.gl.service.broadcastPlan.repository.BroadcastPlanVoiceWorkRepository;
import com.gl.service.broadcastPlan.vo.BroadcastPlanInfoVo;
import com.gl.service.broadcastPlan.vo.BroadcastPlanVo;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanDto;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import cn.hutool.core.collection.CollUtil;
import com.gl.aspectj.annotation.ShopPermission;
import com.gl.service.opus.entity.Device;

import java.util.*;

@Service
@Slf4j
public class BroadcastPlanService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BroadcastPlanRepository broadcastPlanRepository;

    @Autowired
    private BroadcastPlanVoiceWorkRepository broadcastPlanVoiceWorkRepository;

    @Autowired
    private GetShopRefUtil shopRefUtil;

    public Result list(BroadcastPlanDto dto, Integer exportType) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();

        if (dto != null) {
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and ( d.sn like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }

        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            where.append(" and s.id in ").append(SqlUtils.foreachIn(shopRef.size()));
            args.addAll(shopRef);
        }

        String sql = "SELECT\n" +
                "   b.id,\n" +
                "   b.start_time,\n" +
                "   b.end_time,\n" +
                "   s.shop_name,\n" +
                " b.create_time," +
                "s.id shopId,\n" +
                " GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,\n" +
                " GROUP_CONCAT(DISTINCT(d.`name`)) deviceNames,\n" +
                " GROUP_CONCAT(DISTINCT(v.`title`))  titles , \n" +
                " b.type,b.start_date,b.end_date,b.interval_time  " +
                "FROM \n" +
                "  dub_broadcast_plan b  -- 计划\n" +
                "  left join dub_shop s on s.id=b.shop_id -- 门店\n" +
                "  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备\n" +
                "  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品\n" +
                "  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品\n" +
                "where 1=1 ";

        where.append(" GROUP BY b.id ");

        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class,
                args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }

        where.append("order by b.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<BroadcastPlanVo> deviceVos = jdbcTemplate.query(sql + where,
                new BeanPropertyRowMapper<>(BroadcastPlanVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", deviceVos);
        return result;
    }

    @ShopPermission(entity = Device.class, idParam = "deviceIds")
    public Result addOrUpdate(BroadcastPlanAddDto vo) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        if (vo == null) {
            return Result.fail("数据不能为空");
        }
        if (StringUtils.isBlank(vo.getStartTime())) {
            return Result.fail("播报时段不能为空");
        }
        if (StringUtils.isBlank(vo.getEndTime())) {
            return Result.fail("播报时段不能为空");
        }
        if (vo.getDeviceIds() == null) {
            return Result.fail("关联设备不能为空");
        }
        if (ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType())) {// 小程序用户
            if (vo.getShopId() == null) {// 判断用户是否绑定了门店
                // 已绑定门店
                Long shopId = shopRefUtil.getOneShop();// 查询用户是否绑定了门店
                vo.setShopId(shopId);
            } else {
                List<Long> allowShopList = shopRefUtil.getShopRef();
                if (!allowShopList.contains(vo.getShopId())) {
                    return Result.fail("无权操作其他门店的数据");
                }
            }
        }
        Date date = new Date();
        if (vo.getId() == null) {
            // 新增
            BroadcastPlan device = new BroadcastPlan();
            device.setStartTime(vo.getStartTime());
            device.setEndTime(vo.getEndTime());
            device.setShopId(vo.getShopId());
            device.setDeviceIds(String.valueOf(vo.getDeviceIds()));

            device.setCreateTime(date);
            device.setCreateUserId(user.getSiteId());
            device.setType(vo.getType());
            device.setIntervalTime(vo.getIntervalTime());
            device.setStartDate(vo.getStartDate());
            device.setEndDate(vo.getEndDate());

            BroadcastPlan save = broadcastPlanRepository.save(device);
            vo.setId(save.getId());
        } else {
            Optional<BroadcastPlan> byId = broadcastPlanRepository.findById(vo.getId());
            if (byId.isPresent()) {
                // 修改
                BroadcastPlan device = byId.get();
                device.setStartTime(vo.getStartTime());
                device.setEndTime(vo.getEndTime());
                device.setShopId(vo.getShopId());
                device.setDeviceIds(String.valueOf(vo.getDeviceIds()));
                device.setType(vo.getType());
                device.setIntervalTime(vo.getIntervalTime());
                device.setStartDate(vo.getStartDate());
                device.setEndDate(vo.getEndDate());
                broadcastPlanRepository.save(device);
                broadcastPlanVoiceWorkRepository.deleteByPlanId(vo.getId());
            }
        }

        List<Long> voiceWorkList = vo.getVoiceWorkList();
        if (CollUtil.isNotEmpty(voiceWorkList)) {
            List<BroadcastPlanVoiceWorkRef> refList = new ArrayList<>();
            int sort = 0;
            for (Long voiceWorkId : voiceWorkList) {
                BroadcastPlanVoiceWorkRef ref = new BroadcastPlanVoiceWorkRef();
                ref.setPlanId(vo.getId());
                ref.setSort(++sort);
                ref.setVoiceWorkId(voiceWorkId);
                refList.add(ref);
            }
            broadcastPlanVoiceWorkRepository.saveAll(refList);
        }

        return Result.success();
    }

    @ShopPermission(operate = true, entity = BroadcastPlan.class, idParam = "id")
    public Result delete(BroadcastPlanAddDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (CollUtil.isEmpty(dto.getIds())) {
            return Result.fail("播报计划id不能为空");
        }
        broadcastPlanRepository.deleteAllById(dto.getIds());
        broadcastPlanVoiceWorkRepository.deleteAllByPlanId(dto.getIds());
        return Result.success();
    }

    @ShopPermission(entity = BroadcastPlan.class, idParam = "id")
    public Result getInfo(Long id) {
        Optional<BroadcastPlan> optionalPlan = broadcastPlanRepository.findById(id);
        if (!optionalPlan.isPresent()) {
            return Result.fail("播报计划不存在");
        }
        BroadcastPlan broadcastPlan = optionalPlan.get();

        BroadcastPlanInfoVo broadcastPlanInfoVo = new BroadcastPlanInfoVo();
        broadcastPlanInfoVo.setId(broadcastPlan.getId());
        broadcastPlanInfoVo.setShopId(broadcastPlan.getShopId());
        broadcastPlanInfoVo.setStartDate(broadcastPlan.getStartDate());
        broadcastPlanInfoVo.setEndDate(broadcastPlan.getEndDate());
        broadcastPlanInfoVo.setStartTime(broadcastPlan.getStartTime());
        broadcastPlanInfoVo.setEndTime(broadcastPlan.getEndTime());
        broadcastPlanInfoVo.setType(broadcastPlan.getType());
        broadcastPlanInfoVo.setIntervalTime(broadcastPlan.getIntervalTime());
        broadcastPlanInfoVo.setDeviceIds(broadcastPlan.getDeviceIds());

        List<Map<String, Object>> byPlanId = broadcastPlanVoiceWorkRepository.getByPlanId(broadcastPlan.getId());
        broadcastPlanInfoVo.setVoiceWorkRefList(byPlanId);

        return Result.success(broadcastPlanInfoVo);
    }

}
