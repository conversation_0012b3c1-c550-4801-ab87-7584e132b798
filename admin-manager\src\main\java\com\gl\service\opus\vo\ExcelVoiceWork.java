package com.gl.service.opus.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * @author: duanjinze
 * @date: 2022/11/10 16:38
 * @version: 1.0
 */
@Data
public class ExcelVoiceWork {

    @ExcelProperty(value = "作品标题",index = 0)
    @ColumnWidth(20)
    private String title;

    @ExcelProperty(value = "作品文字",index = 1)
    @ColumnWidth(30)
    private String content;

    @ExcelProperty(value = "语音",index = 2)
    @ColumnWidth(30)
    private String fileUrl;

    @ExcelProperty(value = "主播",index = 3)
    @ColumnWidth(20)
    private String anchorName;

    @ExcelProperty(value = "作品时长",index = 4)
    @ColumnWidth(10)
    private String voiceTime;

    @ExcelProperty(value = "语速",index = 5)
    @ColumnWidth(10)
    private String speed;

    @ExcelProperty(value = "背景音乐",index = 6)
    @ColumnWidth(20)
    private String musicName;

    @ExcelProperty(value = "所属用户",index = 7)
    @ColumnWidth(20)
    private String nickname;

    @ExcelProperty(value = "用户手机",index = 8)
    @ColumnWidth(20)
    private String phone;

    @ExcelProperty(value = "创建时间",index = 9)
    @ColumnWidth(20)
    private String createTime;

}
