package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QVoiceWork is a Querydsl query type for VoiceWork
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QVoiceWork extends EntityPathBase<VoiceWork> {

    private static final long serialVersionUID = 1470109138L;

    public static final QVoiceWork voiceWork = new QVoiceWork("voiceWork");

    public final NumberPath<Long> anchorId = createNumber("anchorId", Long.class);

    public final NumberPath<Long> backgroundMusicId = createNumber("backgroundMusicId", Long.class);

    public final NumberPath<Integer> backgroundMusicVolume = createNumber("backgroundMusicVolume", Integer.class);

    public final StringPath content = createString("content");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> pitch = createNumber("pitch", Integer.class);

    public final NumberPath<Integer> sampleRate = createNumber("sampleRate", Integer.class);

    public final NumberPath<Long> shopId = createNumber("shopId", Long.class);

    public final NumberPath<Integer> speed = createNumber("speed", Integer.class);

    public final StringPath title = createString("title");

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public final NumberPath<Long> voiceId = createNumber("voiceId", Long.class);

    public final NumberPath<Integer> voiceTime = createNumber("voiceTime", Integer.class);

    public final NumberPath<Integer> volume = createNumber("volume", Integer.class);

    public QVoiceWork(String variable) {
        super(VoiceWork.class, forVariable(variable));
    }

    public QVoiceWork(Path<? extends VoiceWork> path) {
        super(path.getType(), path.getMetadata());
    }

    public QVoiceWork(PathMetadata metadata) {
        super(VoiceWork.class, metadata);
    }

}

