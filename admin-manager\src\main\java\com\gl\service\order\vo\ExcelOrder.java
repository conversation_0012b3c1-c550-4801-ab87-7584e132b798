package com.gl.service.order.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

@Data
public class ExcelOrder {

    @ExcelProperty(value = "平台订单号",index = 0)
    @ColumnWidth(40)
    private String outTradeNo;

    @ExcelProperty(value = "套餐",index = 1)
    @ColumnWidth(40)
    private String packagesName;

    @ExcelProperty(value = "订单金额",index = 2)
    @ColumnWidth(40)
    private Integer amount;

    @ExcelProperty(value = "下单用户",index = 3)
    @ColumnWidth(40)
    private String nickname;

    @ExcelProperty(value = "下单用户手机号",index = 4)
    @ColumnWidth(40)
    private String phone;

    @ExcelProperty(value = "关联门店",index = 5)
    @ColumnWidth(40)
    private String shopName;

    @ExcelProperty(value = "关联设备",index = 6)
    @ColumnWidth(40)
    private String deviceName;

    @ExcelProperty(value = "订单状态",index = 7)
    @ColumnWidth(40)
    private String status;

    @ExcelProperty(value = "创建时间",index = 8)
    @ColumnWidth(40)
    private String createTime;

    @ExcelProperty(value = "支付时间",index = 9)
    @ColumnWidth(40)
    private Date timeExpire;

}
