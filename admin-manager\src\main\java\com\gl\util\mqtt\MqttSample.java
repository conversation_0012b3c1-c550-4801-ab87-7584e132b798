package com.gl.util.mqtt;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MqttSample {


    @Value(value = "${mqtt.broker}")
    private String broker;
    @Value(value = "${mqtt.username}")
    private String userNmae;
    @Value(value = "${mqtt.password}")
    private String password;
    @Value(value = "${mqtt.qos}")
    private Integer qos;
    @Value(value = "${mqtt.clientId}")
    private String clientId;

    private static final long TIMEOUT = 5;


    /**
     * 获取 MQTT 客户端实例。
     * @param clientId
     * @return
     * @throws MqttException
     */
    private MqttClient mqttInstance(String clientId) throws MqttException {
        //  持久化
        MemoryPersistence persistence = new MemoryPersistence();
        return new MqttClient(broker, clientId, persistence);
    }

    private MqttConnectOptions getMqttConnectOptions() {
        // MQTT 连接选项
        MqttConnectOptions connOpts = new MqttConnectOptions();
        // 设置认证信息
        connOpts.setUserName(userNmae);
        connOpts.setPassword(password.toCharArray());
        return connOpts;
    }

    /**
     * 发布消息
     * @param topic 设备编号
     * @param content 内容
     */
    public void requestPublisher(String topic, JSONObject content) {
        MqttClient client = null;
        try {
            client = mqttInstance(clientId);
            // 设置内容
            client.connect(getMqttConnectOptions());
            // 发布消息 topic
            MqttMessage message = new MqttMessage(content.toJSONString().getBytes());
            message.setQos(qos);
            String requestTopic = "device/command/request/";
            client.publish(requestTopic.concat(topic), message);
            log.info("发布消息  Message published : {}",  message);

            // 订阅消息 topic
            String respanseTopic = "device/command/response/";
            client.subscribe(respanseTopic.concat(topic), qos);

            CountDownLatch latch = new CountDownLatch(1);
            // 设置回调
            client.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    System.out.println("连接丢失 connection lost：" + cause.getMessage());
                }
                @Override
                public void messageArrived(String topic, MqttMessage message) {
                    String payload = new String(message.getPayload());
                    System.out.println("收到消息 Received message: \n  topic：" + topic + "\n  Qos：" + message.getQos() + "\n  payload：" + new String(message.getPayload()));
                    MqttResultVo mqttResultVo = new MqttResultVo();
                    mqttResultVo.setMessage(payload);
                    mqttResultVo.setTopic(topic);
                    // 将消息发送到WebSocket
                    boolean boo = BlockingQueueVo.addMessage(mqttResultVo);
                    if (boo){
                        System.out.println("消息成功到队列BlockingQueueVo");
                    }
                    latch.countDown();
                }
                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    System.out.println("消息传递成功 deliveryComplete");
                }
            });

            if (!latch.await(TIMEOUT, TimeUnit.SECONDS)) {
                System.out.println("Subscription timed out.");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (client != null && client.isConnected()) {
                try {
                    client.disconnect();
                } catch (MqttException e) {
                    log.error("Error disconnecting client: {}", e.getMessage());
                }
            }
        }
    }
}
