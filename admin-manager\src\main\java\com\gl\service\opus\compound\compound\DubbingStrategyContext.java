package com.gl.service.opus.compound.compound;

import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.redis.RedisService;

public class DubbingStrategyContext {

    private DubbingStrategy dubbingStrategy;

    public DubbingStrategyContext(DubbingStrategy dubbingStrategy){
        this.dubbingStrategy = dubbingStrategy;
    }


    /**
     * 根据策略对象，执行不同的下单接口
     * @return
     */
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {

        return dubbingStrategy.process(dto, redisService);
    }


}
