package com.gl.service.user.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.user.vo.ExcelWechatUser;
import com.gl.service.user.vo.SysAreaTreeVo;
import com.gl.service.user.vo.WechatUserVo;
import com.gl.service.user.vo.dto.WechatUserDto;
import com.gl.system.entity.SysArea;
import com.gl.system.repository.SysAreaRepository;
import com.gl.wechat.entity.WechatUser;
import com.gl.wechat.repository.WechatUserRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信用户列表
 * 
 * @author: duanjinze
 * @date: 2022/11/11 14:43
 * @version: 1.0
 */
@Service
@Slf4j
public class WechatUserService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Resource
    private SysAreaRepository sysAreaRepository;
    @Autowired
    private WechatUserRepository wechatUserRepository;

    public Result list(WechatUserDto dto, Integer exportType) {
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();

        if (dto != null) {
            if (dto.getGender() != null) {
                where.append(" and u.gender = ? ");
                args.add(dto.getGender());
            }
            if (StringUtils.isNotBlank(dto.getAddress())) {
                Optional<SysArea> byId = sysAreaRepository.findById(Long.valueOf(dto.getAddress()));
                byId.ifPresent(sysArea -> dto.setAddress(sysArea.getName()));
                where.append(" and u.area = ? ");
                args.add(dto.getAddress());
            }
            if (StringUtils.isNotBlank(dto.getBeginTime())) {
                where.append(" and u.auth_time >= ? ");
                args.add(dto.getBeginTime());
            }
            if (StringUtils.isNotBlank(dto.getEndTime())) {
                where.append(" and u.auth_time <= ? ");
                args.add(dto.getEndTime());
            }
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and u.nickname like ? or u.phone like ?) ");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
            }

        }
        String sql = "select u.id,u.openid,u.nickname," +
                "u.avatar,u.gender,u.phone,u.area,u.auth_time,s.shop_name " +
                "from dub_wechat_user u " +
                "LEFT JOIN dub_user_shop_ref ref ON ref.user_id = u.id " +
                "LEFT JOIN dub_shop s ON s.id = ref.shop_id " +
                "where 1=1 ";
        Result result = Result.success();
        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class,
                args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }
        where.append(" order by u.auth_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<WechatUserVo> wechatUserVos = jdbcTemplate.query(sql + where,
                new BeanPropertyRowMapper<>(WechatUserVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", wechatUserVos);
        return result;
    }

    public void exportList(WechatUserDto dto, HttpServletResponse response) throws IOException {
        Result list = list(dto, 2);
        HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(list.getData()), HashMap.class);
        Object result = hashMap.get("result");
        List<ExcelWechatUser> excelWechatUsers = null;
        if (result != null) {
            List<WechatUserVo> wechatUserVos = JSON.parseArray(JSON.toJSONString(result), WechatUserVo.class);
            excelWechatUsers = wechatUserVos.stream().map(item -> {
                ExcelWechatUser excelWechatUser = new ExcelWechatUser();
                excelWechatUser.setAvatar(StringUtils.isBlank(item.getAvatar()) ? "" : item.getAvatar());
                excelWechatUser.setNickname(StringUtils.isBlank(item.getNickname()) ? "" : item.getNickname());
                excelWechatUser.setPhone(StringUtils.isBlank(item.getPhone()) ? "" : item.getPhone());
                if (item.getGender() == 0) {
                    excelWechatUser.setGender("未知");
                } else if (item.getGender() == 1) {
                    excelWechatUser.setGender("男");
                } else if (item.getGender() == 2) {
                    excelWechatUser.setGender("女");
                }
                excelWechatUser.setArea(StringUtils.isBlank(item.getArea()) ? "" : item.getArea());
                excelWechatUser.setAuthTime(item.getAuthTime() == null ? ""
                        : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(item.getAuthTime()));
                return excelWechatUser;
            }).collect(Collectors.toList());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = "微信用户列表_" + System.currentTimeMillis();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExcelWechatUser.class).sheet("微信用户列表").doWrite(excelWechatUsers);
        // 导出excel
        log.info("微信用户列表导出end");
    }

    /**
     * 获取区域树形图
     */
    public List<SysAreaTreeVo> areaList(String name) {
        List<SysArea> areaList;
        if (StringUtils.isNotBlank(name)) {
            String sql = "select * from sys_area where name like concat('%',?,'%')";
            areaList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SysArea.class), name);
        } else {
            areaList = sysAreaRepository.findAll();
        }

        List<SysAreaTreeVo> rootList = new ArrayList<>();
        List<SysAreaTreeVo> leafList = new ArrayList<>();

        for (SysArea sysArea : areaList) {
            SysAreaTreeVo sysAreaTreeVo = new SysAreaTreeVo();
            sysAreaTreeVo.setId(sysArea.getId());
            sysAreaTreeVo.setName(sysArea.getName());
            sysAreaTreeVo.setParentId(sysArea.getParentId());
            sysAreaTreeVo.setChildren(new ArrayList<>());
            if (sysAreaTreeVo.getParentId() == 0) {
                rootList.add(sysAreaTreeVo);
            } else {
                leafList.add(sysAreaTreeVo);
            }
        }
        return getTree(rootList, leafList);
    }

    public List<SysAreaTreeVo> getTree(List<SysAreaTreeVo> rootList, List<SysAreaTreeVo> leafList) {
        if (leafList != null && !rootList.isEmpty()) {
            // 声明一个map，用来过滤已操作过的数据
            Map<Long, Long> map = Maps.newHashMapWithExpectedSize(leafList.size());
            rootList.forEach(beanTree -> getChild(beanTree, map, leafList));
            return rootList;
        } else {
            List<Long> longs = new ArrayList<>();
            assert leafList != null;
            leafList.forEach(beanTree -> getChilds(beanTree, longs, leafList));
            return leafList;
        }
    }

    private void getChilds(SysAreaTreeVo treeDto, List<Long> longs, List<SysAreaTreeVo> leafList) {
        List<SysAreaTreeVo> childList = Lists.newArrayList();
        for (SysAreaTreeVo vo : leafList) {
            if (vo.getParentId().equals(treeDto.getId())) {
                childList.add(vo);
                longs.add(vo.getId());
                treeDto.setChildren(childList);// 子数据集
            }
        }
    }

    private void getChild(SysAreaTreeVo treeDto, Map<Long, Long> map, List<SysAreaTreeVo> leafList) {
        List<SysAreaTreeVo> childList = Lists.newArrayList();
        leafList.stream()
                .filter(c -> !map.containsKey(c.getId()))
                .filter(c -> c.getParentId().equals(treeDto.getId()))
                .forEach(c -> {
                    map.put(c.getId(), c.getParentId());
                    getChild(c, map, leafList);
                    childList.add(c);
                });
        treeDto.setChildren(childList);// 子数据集
    }

    public Result checkPhone() {
        boolean boo = false;
        Long wxid = SecurityUtils.getLoginUser().getUser().getSiteId();
        if (wxid != null) {
            if (wechatUserRepository.existsById(wxid)) {
                WechatUser wxUser = wechatUserRepository.getById(wxid);
                boo = StringUtils.isEmpty(wxUser.getPhone());
            }
        }
        return Result.success(boo);
    }
}
