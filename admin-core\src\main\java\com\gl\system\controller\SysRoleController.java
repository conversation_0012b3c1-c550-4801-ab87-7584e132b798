package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.constant.Constants;
import com.gl.framework.interceptor.annotation.RepeatSubmit;
import com.gl.framework.web.domain.PageData;
import com.gl.framework.web.response.Result;
import com.gl.system.service.SysRoleService;
import com.gl.system.service.SysUserAllotRoleService;
import com.gl.system.vo.SysRoleVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/system/role")
public class SysRoleController {

    @Autowired
    private SysRoleService roleService;

    @Autowired
    private SysUserAllotRoleService userAllotRoleService;

    /**
     * 角色分页列表
     *
     * @param filter
     * @return
     */
    @GetMapping("/list")
    @PreAuthorize("@ps.hasPermi('system:role:list')")
    public PageData<SysRoleVo> list(SysRoleVo filter) {
        return roleService.selectRoleList(filter);
    }

    @GetMapping("/")
    //@PreAuthorize("@ps.hasPermi('system:role:list')")
    public Result optionList() {

        return userAllotRoleService.selectRoleList();
    }
    /**
     * 获取角色详细信息
     *
     * @param roleId
     * @return
     */
    @GetMapping(value = "/{roleId}")
    @PreAuthorize("@ps.hasPermi('system:role:query')")
    public SysRoleVo getInfo(@PathVariable Long roleId) {
        return roleService.selectRoleById(roleId);
    }

    /**
     * 新增角色
     *
     * @param role
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @PreAuthorize("@ps.hasPermi('system:role:add')")
    @Log(title = "角色管理", businessType = BusinessType.INSERT, businessTypeName = "新增角色")
    public Result add(@Validated @RequestBody SysRoleVo role) {
        if (Constants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return Result.fail("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }

        roleService.saveRole(role);
        return Result.success();
    }

    /**
     * 修改角色
     *
     * @param role
     * @return
     */
    @PutMapping
    @PreAuthorize("@ps.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE, businessTypeName = "修改角色")
    public Result edit(@Validated @RequestBody SysRoleVo role) {
        roleService.checkRoleAllowed(role);
        if (Constants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return Result.fail("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }

        roleService.updateRole(role);
        return Result.success();
    }

    /**
     * 状态修改
     *
     * @param role
     * @return
     */
    @PutMapping("/status")
    @PreAuthorize("@ps.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE, businessTypeName = "状态修改")
    public Result changeStatus(@RequestBody SysRoleVo role) {
        roleService.checkRoleAllowed(role);
        roleService.updateRoleStatus(role);
        return Result.success();
    }

    /**
     * 修改数据权限
     *
     * @param role
     * @return
     */
    @PutMapping("/datascope")
    @PreAuthorize("@ps.hasPermi('system:role:data')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE, businessTypeName = "修改数据权限")
    public Result dataScope(@RequestBody SysRoleVo role) {
        roleService.checkRoleAllowed(role);
        roleService.authDataScope(role);
        return Result.success();
    }

    /**
     * 删除角色
     *
     * @param roleIds
     * @return
     */
    @DeleteMapping("/{roleIds}")
    @PreAuthorize("@ps.hasPermi('system:role:remove')")
    @Log(title = "角色管理", businessType = BusinessType.DELETE, businessTypeName = "删除角色")
    public Result remove(@PathVariable List<Long> roleIds) {
        roleService.deleteRoleByIds(roleIds);
        return Result.success();
    }

    /**
     * 设置角色菜单权限
     *
     * @return
     */
    @RepeatSubmit
    @PostMapping("/menu/{id}")
    @Log(title = "角色管理", businessType = BusinessType.INSERT, businessTypeName = "设置角色菜单权限")
    public Result addMenuRole(@PathVariable Long id, @RequestBody List<Long> menuIds) {
        roleService.roleMenu(id, menuIds);
        return Result.success();
    }
}
