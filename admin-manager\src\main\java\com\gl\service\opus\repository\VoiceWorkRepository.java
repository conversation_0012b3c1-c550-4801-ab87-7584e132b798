package com.gl.service.opus.repository;

import com.gl.service.opus.entity.VoiceWork;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface VoiceWorkRepository extends JpaRepository<VoiceWork, Long>, JpaSpecificationExecutor<VoiceWork> {

    @Transactional
    @Modifying
    @Query(value = "update dub_voice_work set del_status = 1 where id = ?1 ", nativeQuery = true)
    Integer updateDelStatusById(Long id);

    @Transactional
    @Modifying
    @Query(value = "update dub_voice_work set del_status = 1 where voice_id = ?1 ", nativeQuery = true)
    Integer updateDelStatusByVoiceId(Long voiceId);

    List<VoiceWork> findByTitleAndDelStatus(String title, Integer delete);
}
