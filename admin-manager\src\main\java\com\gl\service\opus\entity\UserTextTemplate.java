package com.gl.service.opus.entity;

import com.gl.framework.entity.IdEntity;
import lombok.Data;

import javax.persistence.*;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-07-16-08:58
 */
@Data
@Entity
@Table(name = "user_text_template")
public class UserTextTemplate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "user_id", nullable = true)
    private Long userId; // 小程序openid

    @Basic
    @Column(name = "text_content", nullable = true)
    private String textContent; // 小程序openid
}
