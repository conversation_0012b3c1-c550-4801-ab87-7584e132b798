package com.gl.service.paidPackages.service;

import cn.hutool.core.collection.CollUtil;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.paidPackages.entity.PaidPackages;
import com.gl.service.paidPackages.repository.PaidPackagesRepository;
import com.gl.service.paidPackages.vo.PaidPackagesVo;
import com.gl.service.paidPackages.vo.dto.PaidPackagesAddDto;
import com.gl.service.paidPackages.vo.dto.PaidPackagesDto;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class PaidPackagesService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private PaidPackagesRepository paidPackagesRepository;

    @Autowired
    private GetShopRefUtil shopRefUtil;

    public Result list(PaidPackagesDto dto, Integer exportType) {
        Result result = Result.success();

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and ( p.name like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }

        String sql = "SELECT\n" + "p.id, \n" + "p.name,\n" + "p.data_num,\n" + "p.effect_day,\n" + "p.selling_price,\n" + "p.create_time \n" + "FROM dub_paid_packages p\n" + "where 1=1 ";

        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }

        where.append("order by p.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<PaidPackagesVo> deviceVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(PaidPackagesVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", deviceVos);
        return result;
    }


    public Result addOrUpdate(PaidPackagesAddDto vo) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        if (vo == null) {
            return Result.fail("数据不能为空");
        }
        if (StringUtils.isBlank(vo.getName())) {
            return Result.fail("套餐名称不能为空");
        }
        if (vo.getDataNum() == null) {
            return Result.fail("套餐流量不能为空");
        }
        if (vo.getEffectDay() == null) {
            return Result.fail("有效天数不能为空");
        }
        if (vo.getSellingPrice() == null) {
            return Result.fail("售价不能为空");
        }
        Date date = new Date();
        if (vo.getId() == null) {
            //新增
            PaidPackages device = new PaidPackages();
            device.setName(vo.getName());
            device.setCreateUserId(user.getId());
            device.setEffectDay(vo.getEffectDay());
            device.setDataNum(vo.getDataNum());
            device.setSellingPrice(vo.getSellingPrice());
            device.setCreateTime(date);
            paidPackagesRepository.save(device);
        } else {
            Optional<PaidPackages> byId = paidPackagesRepository.findById(vo.getId());
            if (byId.isPresent()) {
                //修改
                PaidPackages device = byId.get();
                device.setName(vo.getName());
                device.setEffectDay(vo.getEffectDay());
                device.setDataNum(vo.getDataNum());
                device.setSellingPrice(vo.getSellingPrice());
                paidPackagesRepository.save(device);
            } else {
                //新增
                PaidPackages device = new PaidPackages();
                device.setName(vo.getName());
                device.setCreateUserId(user.getId());
                device.setEffectDay(vo.getEffectDay());
                device.setDataNum(vo.getDataNum());
                device.setSellingPrice(vo.getSellingPrice());
                device.setCreateTime(date);
                paidPackagesRepository.save(device);
            }
        }
        return Result.success();
    }

    public Result delete(PaidPackagesAddDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getIds().isEmpty()) {
            return Result.fail("流量管理id不能为空");
        }
        for (Long id : dto.getIds()) {
            paidPackagesRepository.deleteById(id);
        }
        return Result.success();
    }

}
