package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysDept is a Querydsl query type for SysDept
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysDept extends EntityPathBase<SysDept> {

    private static final long serialVersionUID = -1829691222L;

    public static final QSysDept sysDept = new QSysDept("sysDept");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    public final StringPath ancestors = createString("ancestors");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> createUserId = createNumber("createUserId", Long.class);

    public final NumberPath<Integer> deleted = createNumber("deleted", Integer.class);

    public final StringPath deptName = createString("deptName");

    public final StringPath email = createString("email");

    public final NumberPath<Long> headUserId = createNumber("headUserId", Long.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final StringPath leader = createString("leader");

    public final NumberPath<Long> parentId = createNumber("parentId", Long.class);

    public final StringPath phone = createString("phone");

    public final NumberPath<Integer> sortNum = createNumber("sortNum", Integer.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public final NumberPath<Long> updateUserId = createNumber("updateUserId", Long.class);

    public QSysDept(String variable) {
        super(SysDept.class, forVariable(variable));
    }

    public QSysDept(Path<? extends SysDept> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysDept(PathMetadata metadata) {
        super(SysDept.class, metadata);
    }

}

