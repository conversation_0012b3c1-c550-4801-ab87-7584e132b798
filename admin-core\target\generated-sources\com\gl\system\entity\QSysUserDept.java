package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysUserDept is a Querydsl query type for SysUserDept
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysUserDept extends EntityPathBase<SysUserDept> {

    private static final long serialVersionUID = 914811029L;

    public static final QSysUserDept sysUserDept = new QSysUserDept("sysUserDept");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    public final NumberPath<Long> deptId = createNumber("deptId", Long.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QSysUserDept(String variable) {
        super(SysUserDept.class, forVariable(variable));
    }

    public QSysUserDept(Path<? extends SysUserDept> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysUserDept(PathMetadata metadata) {
        super(SysUserDept.class, metadata);
    }

}

