<component name="libraryTable">
  <library name="Maven: org.springframework:spring-context-support:5.3.8">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-context-support/5.3.8/spring-context-support-5.3.8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-context-support/5.3.8/spring-context-support-5.3.8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-context-support/5.3.8/spring-context-support-5.3.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>