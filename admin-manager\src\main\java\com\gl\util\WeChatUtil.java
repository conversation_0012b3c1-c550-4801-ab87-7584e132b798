package com.gl.util;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@Slf4j
public class WeChatUtil {

    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    public static String getAccessToken(String appId, String appSecret) throws Exception {
        String url = String.format(ACCESS_TOKEN_URL, appId, appSecret);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);

        HttpResponse response = httpClient.execute(httpGet);
        String responseBody = EntityUtils.toString(response.getEntity());
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        if (jsonNode.has("access_token")) {
            return jsonNode.get("access_token").asText();
        } else {
            throw new RuntimeException("Failed to get access token: " + responseBody);
        }
    }

    private static final String QR_CODE_URL = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s";
    public static String createQRCode(String accessToken, String sceneStr) throws Exception {
        String url = String.format(QR_CODE_URL, accessToken);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("expire_seconds", 604800);
        jsonObject.put("action_name", "QR_STR_SCENE");
        JSONObject info = new JSONObject();
        info.put("scene_str", sceneStr);
        jsonObject.put("action_info", info.toJSONString());

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json; utf-8");
        httpPost.setEntity(new StringEntity(jsonObject.toJSONString()));

        HttpResponse response = httpClient.execute(httpPost);
        return EntityUtils.toString(response.getEntity()); // 返回二维码的 URL 或 ticket
    }

    public static String getOpenId(String appid, String appSecret, String code) throws Exception {
        // 获取 access_token 和 openid
        String url = String.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code", appid, appSecret, code);;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        HttpResponse response = httpClient.execute(httpGet);
        return EntityUtils.toString(response.getEntity());
    }

    public static String userInfo(String accessToken, String openid) throws Exception {
        // 获取用户信息
        String userInfoUrl = "https://api.weixin.qq.com/sns/userinfo?access_token=" + accessToken + "&openid=" + openid;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(userInfoUrl);
        HttpResponse response = httpClient.execute(httpGet);
        return EntityUtils.toString(response.getEntity());
    }

    public String generateAuthUrl(String appId, String redirectUri) {
        return "https://open.weixin.qq.com/connect/oauth2/authorize" +
                "?appid=" + appId +
                "&redirect_uri=" + redirectUri +
                "&response_type=code" +
                "&scope=snsapi_login" +
                "&state=UNIQUE_STATE" +
                "#wechat_redirect";
    }

    public static void main(String[] args) throws Exception {
        // 获取 access_token
        String accessToken = getAccessToken("wxa88d0a3a689307c6", "7e7d1a7728c39a305dfb0ad0290a02be");
        log.info("Access token: {}", accessToken);
        // 生成二维码
        String sceneStr = "wechat_login"; // 自定义场景值
        String qrCodeResponse = createQRCode(accessToken, sceneStr);
        log.info("QR code response: {}", qrCodeResponse);
    }
}
