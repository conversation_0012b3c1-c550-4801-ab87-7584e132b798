package com.gl.service.opus.repository;


import com.gl.service.opus.entity.UserBackgroundMusic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface UserBackgroundMusicRepository extends JpaRepository<UserBackgroundMusic,Long>, JpaSpecificationExecutor<UserBackgroundMusic> {

    List<UserBackgroundMusic> findByUserId(Long userId);
    @Transactional
    @Modifying
    @Query(value = "update user_background_music set del_status = 1 where user_id = ?1 and id=?2 ",nativeQuery = true)
    void deleteByUserIdAndId( Long userId,Long id);

    UserBackgroundMusic findByUserIdAndId(Long userId, Long id);

}
