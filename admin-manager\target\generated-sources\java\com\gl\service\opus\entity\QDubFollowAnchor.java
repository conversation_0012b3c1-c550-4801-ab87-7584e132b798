package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QDubFollowAnchor is a Querydsl query type for DubFollowAnchor
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QDubFollowAnchor extends EntityPathBase<DubFollowAnchor> {

    private static final long serialVersionUID = -757060218L;

    public static final QDubFollowAnchor dubFollowAnchor = new QDubFollowAnchor("dubFollowAnchor");

    public final NumberPath<Long> anchorId = createNumber("anchorId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QDubFollowAnchor(String variable) {
        super(DubFollowAnchor.class, forVariable(variable));
    }

    public QDubFollowAnchor(Path<? extends DubFollowAnchor> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDubFollowAnchor(PathMetadata metadata) {
        super(DubFollowAnchor.class, metadata);
    }

}

