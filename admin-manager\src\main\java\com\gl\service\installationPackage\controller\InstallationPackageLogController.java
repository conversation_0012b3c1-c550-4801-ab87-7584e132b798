package com.gl.service.installationPackage.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.installationPackage.service.InstallationPackageLogService;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDelDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogRenewalDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 安装包更新日志
 */
@Controller
@RequestMapping("/installationPackageLog")
public class InstallationPackageLogController {

    @Autowired
    private InstallationPackageLogService installationPackageLogService;

    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:installationPackageLog:list')")
    public Result list(InstallationPackageLogDto dto) {
        return installationPackageLogService.list(dto, 1);
    }


    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:installationPackageLog:delete')")
    public Result delete(@RequestBody InstallationPackageLogDelDto dto){
        return installationPackageLogService.delete(dto);
    }

    @PostMapping(value = "/renewal")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:installationPackageLog:renewal')")
    public Result renewal(@RequestBody InstallationPackageLogRenewalDto vo){
        return installationPackageLogService.renewal(vo);
    }


}
