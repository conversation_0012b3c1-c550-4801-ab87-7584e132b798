# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 数据源
      url: ***********************************************************************************************************************************************************************************
      username: root
      password: "!QAZ@WSX"
      # 初始连接数
      initialSize: 2
      # 最小连接池数量
      minIdle: 2
      # 最大连接池数量
      maxActive: 5
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: admin
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  # Jpa配置
  jpa:
    database: mysql
    show-sql: true
    open-in-view: true
    hibernate:
      # 指定DDL mode (none, validate, update, create, create-drop)
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        # 格式化sql语句
        format_sql: true

  # redis配置
  redis:
    # 地址
    host: ************
    # 端口，默认为6379
    port: 6379
    # 密码
    password: "A&kQE3Zvb"
    # 库索引
    database: 11
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 5
        # 连接池的最大数据库连接数
        max-active: 5
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 阿里云OSS配置
ali:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKeyId: LTAI5tGLtZoTrimJhNrsYRqH
    accessKeySecret: ******************************
    appKey: UDk4g8pGSIvNJwxU
    bucket:
      name: peiyin-prod
      url: https://peiyin-prod.oss-cn-hangzhou.aliyuncs.com/
      path: wifi/
    downFile:
      tempdir: /home/<USER>/py-service/file/MP3/
      packetdir: /home/<USER>/admin-manager/file/

wechat:
  mp:
    appId: wx6921363eb51013c5
    secret: c3cd358056fb139a14e40bc03bbad860
    redirectUri: https://d.darhoo.com
  pay:
    appid: wxfdb8bf236b7e23bc
    mchid: 1675024609
    apikey: 13858985525lanxishihongjingdianz
    mchser: 7FFC26A64E2F65E48FCE52E381B9F79330B74BA0
    privateKeyPath: /home/<USER>/apiclient_key.pem
    privateCertPath: /home/<USER>/apiclient_cert.pem
    notifyPath: https://d.darhoo.com/prod-api

mqtt:
  broker: tcp://d.darhoo.com:1883
  username: admin
  password: peiyin2022
  qos: 1
  clientId: admin-service

ty:
  accessKeyId: LTAI5tGLtZoTrimJhNrsYRqH
  accessKeySecret: ******************************

netty:
  path: /ai
  port: 8050
