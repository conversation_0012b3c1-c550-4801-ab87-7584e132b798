08:32:32.323 [34m<PERSON><PERSON><PERSON> [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
08:32:32.382 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 16500 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
08:32:32.387 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
08:32:52.087 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08:32:52.094 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
08:32:53.635 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1458 ms. Found 54 JPA repository interfaces.
08:32:55.477 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
08:32:55.600 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
08:32:56.820 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@1d6a8386' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08:32:56.869 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08:32:56.951 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08:32:56.997 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08:32:57.002 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08:32:58.239 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
08:32:58.260 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
08:32:58.262 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
08:32:58.263 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
08:32:59.029 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
08:32:59.029 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 26251 ms
08:33:00.795 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
08:33:00.941 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
08:33:04.345 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
08:33:06.539 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
08:33:06.783 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
08:33:07.660 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
08:33:08.601 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08:33:15.086 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
08:33:15.106 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
08:33:21.325 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
08:33:21.325 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
08:33:21.345 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
08:33:37.056 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
08:33:41.411 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
08:33:42.689 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@485996f7, org.springframework.security.web.context.SecurityContextPersistenceFilter@4f7bb581, org.springframework.security.web.header.HeaderWriterFilter@524a96f5, org.springframework.security.web.authentication.logout.LogoutFilter@5c26ab0a, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@5a3cf824, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20bd4fd2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c534814, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35984172, org.springframework.security.web.session.SessionManagementFilter@1db077a4, org.springframework.security.web.access.ExceptionTranslationFilter@6b29771f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@57e0bfd6]
08:33:44.637 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
08:33:44.637 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
08:33:44.638 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
08:33:44.638 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
08:33:45.052 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
08:33:45.981 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 76.341 seconds (JVM running for 80.64)
08:33:45.998 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

08:33:55.588 [34mINFO [0;39m [RMI TCP Connection(34)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:33:55.588 [34mINFO [0;39m [RMI TCP Connection(34)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
08:33:55.593 [34mINFO [0;39m [RMI TCP Connection(34)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
08:47:14.828 [34mINFO [0;39m [http-nio-8770-exec-3] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
08:47:14.828 [34mINFO [0;39m [http-nio-8770-exec-3] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
08:47:15.186 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
08:47:34.390 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:47:34.406 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.s.c.service.CommercialService - args = []
08:47:34.412 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:34.413 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL GROUP BY wu.id ) t]
08:47:36.190 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:36.190 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
08:47:42.488 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:42.488 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`sn`)) deviceNames,
 group_concat(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  GROUP BY b.id ) t]
08:47:44.043 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:44.043 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
 o.id, 
 o.out_trade_no ,
  p.`name` packagesName,
  o.amount,
  u.nickname,
  u.phone,
  s.shop_name,
  d.`name` deviceName,
  d.sn deviceSn,
  o.`status`,
  o.create_time,
  o.time_expire,
  0 invoiceStatus 
FROM
  dub_order o
  LEFT JOIN dub_device d ON d.id = o.device_id
  LEFT JOIN dub_shop s ON s.id = o.shop_id
  LEFT JOIN dub_paid_packages p ON p.id = o.package_id
  LEFT JOIN dub_wechat_user u ON u.id = o.user_id  WHERE 1=1 ) t]
08:47:44.356 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:47:46.129 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:46.129 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 ) t]
08:47:46.142 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:46.142 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 order by p.create_time DESC  LIMIT ? OFFSET ? ]
08:47:48.258 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 
08:47:48.258 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:48.258 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 ) t]
08:47:48.547 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:47:51.953 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:51.954 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
08:47:53.118 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.s.c.service.CommercialService - args = []
08:47:53.118 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:53.118 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL GROUP BY wu.id ) t]
08:47:53.120 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:47:54.400 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 
08:47:54.400 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:54.400 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 ) t]
08:47:54.709 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:47:56.000 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:56.001 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select u.id,u.openid,u.nickname,u.avatar,u.gender,u.phone,u.area,u.auth_time,s.shop_name from dub_wechat_user u LEFT JOIN dub_user_shop_ref ref ON ref.user_id = u.id LEFT JOIN dub_shop s ON s.id = ref.shop_id where u.openid IS NOT NULL ) t]
08:47:56.259 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:56.259 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select u.id,u.openid,u.nickname,u.avatar,u.gender,u.phone,u.area,u.auth_time,s.shop_name from dub_wechat_user u LEFT JOIN dub_user_shop_ref ref ON ref.user_id = u.id LEFT JOIN dub_shop s ON s.id = ref.shop_id where u.openid IS NOT NULL ) t]
08:47:57.381 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:57.381 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1) t]
08:47:57.390 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:47:57.390 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 order by m.create_time DESC  LIMIT ? OFFSET ? ]
08:47:57.678 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:48:02.133 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:02.135 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1) t]
08:48:02.137 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:02.137 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 order by m.create_time DESC  LIMIT ? OFFSET ? ]
08:48:03.208 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:03.208 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1) t]
08:48:03.211 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:03.211 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 order by m.create_time DESC  LIMIT ? OFFSET ? ]
08:48:04.405 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:04.405 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1) t]
08:48:04.408 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:04.408 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 order by m.create_time DESC  LIMIT ? OFFSET ? ]
08:48:06.550 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.s.c.service.CommercialService - args = []
08:48:06.550 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:06.550 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL GROUP BY wu.id ) t]
08:48:06.854 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:48:08.726 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:48:08.726 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
08:48:08.726 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
08:48:08.727 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:48:08.727 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
08:48:08.735 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
08:48:08.738 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
08:48:08.752 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
08:48:10.139 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:50:34.424 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:50:46.003 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:50:46.004 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
 and a.id = ? ]
08:51:20.504 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:51:20.508 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1 ]
08:51:20.510 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:51:20.510 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT dt.id,dt.template_type_id,dtt.name AS templateTypeName,dt.title,dt.content,su.user_name as createUserName,dt.create_time,s.shop_name,s.id shopId FROM dub_template dt 
LEFT JOIN dub_template_type dtt
ON dtt.id = dt.template_type_id
LEFT JOIN sys_user su 
ON su.id = dt.create_id
LEFT JOIN dub_shop s ON s.id = dt.shop_id 
WHERE dt.del_status != 1 and dtt.del_status != 1 
 ) t]
08:51:20.524 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:51:20.524 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT dt.id,dt.template_type_id,dtt.name AS templateTypeName,dt.title,dt.content,su.user_name as createUserName,dt.create_time,s.shop_name,s.id shopId FROM dub_template dt 
LEFT JOIN dub_template_type dtt
ON dtt.id = dt.template_type_id
LEFT JOIN sys_user su 
ON su.id = dt.create_id
LEFT JOIN dub_shop s ON s.id = dt.shop_id 
WHERE dt.del_status != 1 and dtt.del_status != 1 
  order by dt.create_time DESC  LIMIT ? OFFSET ? ]
08:51:37.992 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:51:37.992 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select u.id,u.openid,u.nickname,u.avatar,u.gender,u.phone,u.area,u.auth_time,s.shop_name from dub_wechat_user u LEFT JOIN dub_user_shop_ref ref ON ref.user_id = u.id LEFT JOIN dub_shop s ON s.id = ref.shop_id where u.openid IS NOT NULL ) t]
08:51:40.612 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:51:40.613 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t]
08:51:42.012 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:51:42.012 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
l.id,
s.shop_name ,
d.`name` deviceName,
d.sn,
p.version_name ,
l.create_time ,
l.response_time,
l.status 
from dub_installation_package_log l 
left join dub_installation_package p on l.package_id = p.id 
left join dub_device d on l.device_id = d.id 
left join dub_shop s on s.id=d.shop_id where 1=1 ) t]
08:52:08.435 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:52:08.435 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
l.id,
s.shop_name ,
d.`name` deviceName,
d.sn,
p.version_name ,
l.create_time ,
l.response_time,
l.status 
from dub_installation_package_log l 
left join dub_installation_package p on l.package_id = p.id 
left join dub_device d on l.device_id = d.id 
left join dub_shop s on s.id=d.shop_id where 1=1 ) t]
08:53:23.779 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [SELECT count(1) as total 
FROM sys_user u
left join sys_dept d on d.id = u.dept_id 
LEFT JOIN 
(SELECT u.id,
GROUP_CONCAT(DISTINCT r.role_name ORDER BY r.role_name ASC SEPARATOR ';') as role_name,
GROUP_CONCAT(DISTINCT r.id ORDER BY r.id ASC SEPARATOR ';') as role_id,
GROUP_CONCAT(DISTINCT p.name ORDER BY p.name ASC SEPARATOR ';') as position_name,
GROUP_CONCAT(DISTINCT p.id ORDER BY p.id ASC SEPARATOR ';') as position_id
FROM sys_user u
LEFT JOIN sys_user_role ur ON ur.user_id = u.id 
LEFT JOIN sys_role r ON r.id = ur.role_id 
LEFT JOIN sys_position_user pu ON pu.user_id = u.id 
LEFT JOIN sys_position p ON p.id = pu.position_id 
group by u.id) m ON u.id = m.id 
WHERE (  u.deleted = 0  or  u.deleted is null ) and u.type = 1  and u.dept_id in (128,130,131,132,133,134,135,136,200,137,138,139,141,142,143,145,146,147,148,149,150,151,152,156,157,158,159,160,161,162,163,100,164,101,165,166,103,107,116,117,118,119,120,121,122,100) ]
08:54:32.090 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:54:32.090 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT vp.*,wu.nickname ,wu.phone,vw.create_time,s.shop_name 
 FROM dub_voice_packet vp
LEFT JOIN dub_voice_work vw
ON vp.id = vw.voice_id
LEFT JOIN dub_wechat_user wu
ON wu.id = vw.user_id
LEFT JOIN dub_shop s ON s.id = vp.shop_id 
WHERE vw.del_status != 1
) t]
08:54:32.386 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:54:34.242 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:54:34.242 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1) t]
08:54:34.242 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:54:34.244 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:54:34.244 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 order by m.create_time DESC  LIMIT ? OFFSET ? ]
08:54:35.316 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:54:35.316 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t]
08:54:52.861 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:54:52.861 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
08:57:00.465 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
08:57:00.465 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
08:57:00.478 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
08:57:00.478 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
08:57:00.481 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
08:57:00.481 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
08:57:00.485 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:57:00.490 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
08:57:01.466 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
08:57:13.771 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:14:36.054 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:19:10.207 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:19:16.130 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:19:16.212 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
09:19:16.212 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
09:19:16.212 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
09:19:16.212 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
09:19:16.212 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:19:16.212 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
09:19:16.238 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
09:19:16.644 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:20:53.517 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
09:20:53.518 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:20:53.520 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
09:20:53.521 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
09:20:53.522 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
09:20:53.524 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:20:53.525 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
09:20:53.533 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
09:20:54.433 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:22:43.078 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:22:43.078 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
09:22:43.078 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
09:22:43.079 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
09:22:43.079 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
09:22:43.079 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
09:22:43.086 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:22:43.094 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
09:22:44.208 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:22:50.955 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
09:22:51.093 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:22:51.094 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
09:22:51.094 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
09:22:51.102 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
09:22:51.102 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
09:22:51.108 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
09:22:51.117 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
09:22:52.245 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
10:35:08.436 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:35:08.437 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
10:35:08.437 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
10:35:08.466 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
10:35:08.497 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
10:35:08.509 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
10:35:08.518 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
10:35:23.794 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
10:35:23.826 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 13336 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
10:35:23.826 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
10:35:26.085 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:35:26.086 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:35:26.438 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 344 ms. Found 54 JPA repository interfaces.
10:35:26.816 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
10:35:26.826 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
10:35:27.112 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@ab4aa5e' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:35:27.127 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:35:27.150 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:35:27.169 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:35:27.173 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:35:27.639 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
10:35:27.649 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
10:35:27.650 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
10:35:27.650 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
10:35:27.816 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
10:35:27.816 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3939 ms
10:35:28.245 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
10:35:28.309 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
10:35:28.904 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
10:35:29.106 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:35:29.199 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
10:35:29.461 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
10:35:29.926 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
10:35:31.134 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:35:31.145 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:35:32.464 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
10:35:32.464 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
10:35:32.466 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
10:35:35.097 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
10:35:35.911 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
10:35:36.068 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@49dce561, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d8cfad5, org.springframework.security.web.header.HeaderWriterFilter@2d0ed017, org.springframework.security.web.authentication.logout.LogoutFilter@7469d13a, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@3120495d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4d13b552, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d3d89fb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c534814, org.springframework.security.web.session.SessionManagementFilter@419df81e, org.springframework.security.web.access.ExceptionTranslationFilter@39da0d43, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@20bd4fd2]
10:35:36.312 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:35:36.312 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
10:35:36.312 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
10:35:36.312 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
10:35:36.333 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
10:35:36.452 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 13.624 seconds (JVM running for 15.135)
10:35:36.459 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

10:35:36.789 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:35:36.790 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
10:35:36.794 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
10:35:52.790 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
10:35:52.790 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
10:36:22.175 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:36:22.176 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
10:36:22.177 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
10:36:22.202 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
10:36:22.204 [31mWARN [0;39m [SpringApplicationShutdownHook] o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
10:36:22.250 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
10:36:22.253 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
10:36:22.303 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
10:36:37.294 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
10:36:37.333 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 14472 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
10:36:37.334 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
10:36:39.174 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:36:39.175 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:36:39.495 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 314 ms. Found 54 JPA repository interfaces.
10:36:39.809 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
10:36:39.820 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
10:36:40.054 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@5234b61a' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:36:40.063 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:36:40.077 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:36:40.088 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:36:40.090 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:36:40.409 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
10:36:40.417 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
10:36:40.418 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
10:36:40.418 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
10:36:40.530 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
10:36:40.530 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3124 ms
10:36:40.876 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
10:36:40.909 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
10:36:41.330 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
10:36:41.487 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:36:41.528 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
10:36:41.665 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
10:36:41.932 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
10:36:42.941 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:36:42.950 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:36:44.172 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
10:36:44.173 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
10:36:44.177 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
10:36:48.550 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
10:36:50.296 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
10:36:50.422 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@df80af2, org.springframework.security.web.context.SecurityContextPersistenceFilter@5168f69d, org.springframework.security.web.header.HeaderWriterFilter@72fb922c, org.springframework.security.web.authentication.logout.LogoutFilter@6a636c62, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@2b170932, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@69cd4267, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e38b4ea, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6619692e, org.springframework.security.web.session.SessionManagementFilter@75c90ec5, org.springframework.security.web.access.ExceptionTranslationFilter@4f991f00, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2cf88ae1]
10:36:50.668 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:36:50.668 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
10:36:50.668 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
10:36:50.668 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
10:36:50.687 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
10:36:50.800 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 14.581 seconds (JVM running for 16.552)
10:36:50.807 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

10:36:51.201 [34mINFO [0;39m [RMI TCP Connection(8)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:36:51.201 [34mINFO [0;39m [RMI TCP Connection(8)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
10:36:51.209 [34mINFO [0;39m [RMI TCP Connection(8)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 7 ms
10:36:56.496 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
10:36:56.496 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
10:36:56.783 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
10:37:53.302 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:37:53.302 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
10:37:53.302 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
10:37:53.314 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
10:37:53.327 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
10:37:53.329 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
10:37:53.334 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
10:38:04.688 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
10:38:04.715 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 19268 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
10:38:04.716 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
10:38:06.573 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:38:06.573 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:38:06.915 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 333 ms. Found 54 JPA repository interfaces.
10:38:07.292 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
10:38:07.319 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
10:38:07.606 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@634ca3e7' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:38:07.616 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:38:07.635 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:38:07.647 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:38:07.649 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:38:07.986 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
10:38:07.995 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
10:38:07.996 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
10:38:07.996 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
10:38:08.141 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
10:38:08.141 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3374 ms
10:38:08.492 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
10:38:08.525 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
10:38:08.968 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
10:38:09.131 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:38:09.186 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
10:38:09.310 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
10:38:09.590 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
10:38:10.647 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:38:10.657 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:38:11.873 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
10:38:11.873 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
10:38:11.875 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
10:38:14.448 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
10:38:15.148 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
10:38:15.267 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1342c6e1, org.springframework.security.web.context.SecurityContextPersistenceFilter@42be0eca, org.springframework.security.web.header.HeaderWriterFilter@5757ef9b, org.springframework.security.web.authentication.logout.LogoutFilter@6b29771f, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@5dfc2a4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62267a22, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5b4433dc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4a547f9d, org.springframework.security.web.session.SessionManagementFilter@4a907e5a, org.springframework.security.web.access.ExceptionTranslationFilter@1ec22804, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6c720765]
10:38:15.495 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:38:15.495 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
10:38:15.495 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
10:38:15.495 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
10:38:15.514 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
10:38:15.622 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 11.627 seconds (JVM running for 12.979)
10:38:15.629 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

10:38:16.021 [34mINFO [0;39m [RMI TCP Connection(7)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:38:16.021 [34mINFO [0;39m [RMI TCP Connection(7)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
10:38:16.027 [34mINFO [0;39m [RMI TCP Connection(7)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
10:55:12.271 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:55:12.273 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
10:55:12.273 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
10:55:12.303 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
10:55:12.309 [31mWARN [0;39m [SpringApplicationShutdownHook] o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
10:55:12.443 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
10:55:12.541 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
10:55:12.605 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
10:55:27.429 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
10:55:27.471 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 20140 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
10:55:27.472 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
10:55:30.049 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
10:55:30.049 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:55:30.370 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 314 ms. Found 54 JPA repository interfaces.
10:55:30.704 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
10:55:30.714 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
10:55:30.956 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@634ca3e7' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:55:30.965 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:55:30.983 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:55:30.994 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:55:30.996 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:55:31.317 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
10:55:31.326 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
10:55:31.327 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
10:55:31.327 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
10:55:31.459 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
10:55:31.459 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3872 ms
10:55:31.818 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
10:55:31.856 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
10:55:32.401 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
10:55:32.563 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
10:55:32.640 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
10:55:32.833 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
10:55:33.132 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
10:55:34.320 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:55:34.331 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:55:35.718 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
10:55:35.718 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
10:55:35.722 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
10:55:38.291 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
10:55:39.016 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
10:55:39.153 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@23591a2c, org.springframework.security.web.context.SecurityContextPersistenceFilter@295d8b70, org.springframework.security.web.header.HeaderWriterFilter@42be0eca, org.springframework.security.web.authentication.logout.LogoutFilter@1db077a4, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@5dfc2a4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@f5475d7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f991f00, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@431782fe, org.springframework.security.web.session.SessionManagementFilter@2174fda1, org.springframework.security.web.access.ExceptionTranslationFilter@4a907e5a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@49a6b730]
10:55:39.371 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
10:55:39.372 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
10:55:39.372 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
10:55:39.372 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
10:55:39.402 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
10:55:39.539 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 13.188 seconds (JVM running for 15.518)
10:55:39.546 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

10:55:39.989 [34mINFO [0;39m [RMI TCP Connection(7)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:55:39.989 [34mINFO [0;39m [RMI TCP Connection(7)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
10:55:39.993 [34mINFO [0;39m [RMI TCP Connection(7)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
10:59:51.473 [1;31mERROR[0;39m [nioEventLoopGroup-3-1] c.g.f.common.util.SecurityUtils - null
java.lang.NullPointerException: null
	at com.gl.framework.common.util.SecurityUtils.getLoginUser(SecurityUtils.java:40)
	at com.gl.service.websocket.component.AiSocketComponent.ai(AiSocketComponent.java:71)
	at com.gl.service.websocket.handler.WebSocketNettyHandler.channelRead0(WebSocketNettyHandler.java:53)
	at com.gl.service.websocket.handler.WebSocketNettyHandler.channelRead0(WebSocketNettyHandler.java:1)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:105)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:102)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:86)
	at io.netty.handler.codec.http.websocketx.Utf8FrameValidator.channelRead(Utf8FrameValidator.java:77)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340)
	at io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler$1.channelRead(WebSocketServerProtocolHandler.java:204)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelRead(ChannelInboundHandlerAdapter.java:86)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:310)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:284)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1434)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:965)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:647)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:582)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:499)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:461)
	at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:884)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
10:59:51.473 [34mINFO [0;39m [nioEventLoopGroup-3-1] c.g.s.w.h.WebSocketNettyHandler - ai合成异常获取用户信息异常
11:00:19.534 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
11:00:19.536 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
11:10:40.954 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
11:10:40.955 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
11:16:38.767 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
11:16:38.783 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
11:16:38.786 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
11:16:38.875 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
11:16:38.882 [31mWARN [0;39m [SpringApplicationShutdownHook] o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
11:16:39.002 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
11:16:39.037 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
11:16:39.050 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
11:17:23.051 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
11:17:23.078 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 9412 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
11:17:23.078 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
11:17:28.230 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
11:17:28.231 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
11:17:29.259 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1006 ms. Found 54 JPA repository interfaces.
11:17:29.621 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
11:17:29.637 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
11:17:30.233 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@35cec305' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:17:30.246 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:17:30.274 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:17:30.293 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:17:30.297 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:17:31.266 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
11:17:31.310 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
11:17:31.311 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
11:17:31.311 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
11:17:31.685 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
11:17:31.685 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8552 ms
11:17:32.709 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
11:17:32.760 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
11:17:35.114 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
11:17:35.372 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
11:17:35.914 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
11:17:37.858 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
11:17:38.450 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
11:17:43.387 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
11:17:43.398 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
11:17:48.076 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
11:17:48.076 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
11:17:48.088 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
11:17:58.219 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
11:18:01.184 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
11:18:01.335 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6557d4c6, org.springframework.security.web.context.SecurityContextPersistenceFilter@519f6adb, org.springframework.security.web.header.HeaderWriterFilter@85420, org.springframework.security.web.authentication.logout.LogoutFilter@1f953e51, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@28367da7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6c720765, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4a547f9d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@de4bee9, org.springframework.security.web.session.SessionManagementFilter@5e4d55a6, org.springframework.security.web.access.ExceptionTranslationFilter@20bd4fd2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@de0922c]
11:18:03.244 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
11:18:03.244 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
11:18:03.246 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
11:18:03.247 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
11:18:03.403 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
11:18:04.411 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 42.472 seconds (JVM running for 46.048)
11:18:04.460 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

11:18:05.226 [34mINFO [0;39m [RMI TCP Connection(26)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:18:05.226 [34mINFO [0;39m [RMI TCP Connection(26)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
11:18:05.232 [34mINFO [0;39m [RMI TCP Connection(26)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
11:18:06.421 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
11:18:06.421 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
11:18:07.139 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
11:22:21.201 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId-Sub] c.g.c.mqtt.MqttSubscriptCallBack - admin-service-clientId-Sub与服务器断开连接！！已断开连接
11:22:21.202 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId] c.gl.config.mqtt.MqttClientCallBack - admin-service-clientIdclient 与服务器断开连接！！已断开连接
11:24:21.511 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
11:24:21.514 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
11:24:21.514 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
11:24:21.555 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
11:24:21.587 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
11:24:21.593 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
11:24:21.606 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
11:25:06.429 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
11:25:06.460 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 21380 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
11:25:06.460 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
11:25:11.737 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
11:25:11.738 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
11:25:12.147 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 400 ms. Found 54 JPA repository interfaces.
11:25:13.302 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
11:25:13.351 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
11:25:14.239 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@1c297897' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:25:14.282 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:25:14.346 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:25:14.390 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:25:14.396 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
11:25:17.259 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
11:25:17.279 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
11:25:17.296 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
11:25:17.297 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
11:25:17.528 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
11:25:17.528 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 11009 ms
11:25:17.961 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
11:25:18.256 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
11:25:21.318 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
11:25:21.519 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
11:25:21.685 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
11:25:24.027 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
11:25:24.763 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
11:25:29.063 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
11:25:29.091 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
11:25:32.067 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
11:25:32.067 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
11:25:32.071 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
11:25:38.337 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
11:25:39.715 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
11:25:40.533 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@74eebb25, org.springframework.security.web.context.SecurityContextPersistenceFilter@4cef1ea6, org.springframework.security.web.header.HeaderWriterFilter@85420, org.springframework.security.web.authentication.logout.LogoutFilter@4992e34f, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@1b3a9ef4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@58d85a00, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6258ea84, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c1e7bea, org.springframework.security.web.session.SessionManagementFilter@5b1505c3, org.springframework.security.web.access.ExceptionTranslationFilter@20bd4fd2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@504ed038]
11:25:40.926 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
11:25:40.926 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
11:25:40.926 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
11:25:40.926 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
11:25:40.950 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
11:25:41.065 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 36.102 seconds (JVM running for 38.929)
11:25:41.071 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

11:25:41.240 [34mINFO [0;39m [RMI TCP Connection(16)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:25:41.240 [34mINFO [0;39m [RMI TCP Connection(16)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
11:25:41.245 [34mINFO [0;39m [RMI TCP Connection(16)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
11:25:42.556 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
11:25:42.557 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
11:25:43.152 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
12:36:43.256 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:36:43.256 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT vp.*,wu.nickname ,wu.phone,vw.create_time,s.shop_name 
 FROM dub_voice_packet vp
LEFT JOIN dub_voice_work vw
ON vp.id = vw.voice_id
LEFT JOIN dub_wechat_user wu
ON wu.id = vw.user_id
LEFT JOIN dub_shop s ON s.id = vp.shop_id 
WHERE vw.del_status != 1
) t]
12:36:43.553 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:36:48.138 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:36:48.458 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:36:48.458 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
12:36:48.458 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
12:36:48.458 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
12:36:48.459 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
12:36:48.464 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
12:36:48.476 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
12:36:49.212 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:37:30.742 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:37:41.936 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:37:58.824 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_base_service limit 1 ]
12:37:58.866 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_base_about limit 1 ]
12:37:58.875 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1 ]
12:37:59.210 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_base_service limit 1 ]
12:37:59.213 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_base_about limit 1 ]
12:37:59.215 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1 ]
12:38:00.557 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_base_service limit 1 ]
12:38:00.558 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_base_about limit 1 ]
12:38:00.559 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1 ]
12:38:01.399 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:38:01.399 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t]
12:38:04.117 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:38:04.117 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
l.id,
s.shop_name ,
d.`name` deviceName,
d.sn,
p.version_name ,
l.create_time ,
l.response_time,
l.status 
from dub_installation_package_log l 
left join dub_installation_package p on l.package_id = p.id 
left join dub_device d on l.device_id = d.id 
left join dub_shop s on s.id=d.shop_id where 1=1 ) t]
12:38:04.881 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:38:04.881 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t]
12:38:06.625 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:38:06.625 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
l.id,
s.shop_name ,
d.`name` deviceName,
d.sn,
p.version_name ,
l.create_time ,
l.response_time,
l.status 
from dub_installation_package_log l 
left join dub_installation_package p on l.package_id = p.id 
left join dub_device d on l.device_id = d.id 
left join dub_shop s on s.id=d.shop_id where 1=1 ) t]
12:38:06.892 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:38:06.892 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t]
12:39:36.071 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:39:36.071 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`sn`)) deviceNames,
 group_concat(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  GROUP BY b.id ) t]
12:48:33.592 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 
12:48:33.592 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:48:33.592 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 ) t]
12:48:33.888 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:48:36.226 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:48:36.226 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 ) t]
12:48:36.239 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:48:36.239 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 order by p.create_time DESC  LIMIT ? OFFSET ? ]
12:48:38.548 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:48:38.549 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`sn`)) deviceNames,
 group_concat(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  GROUP BY b.id ) t]
12:51:10.768 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1 ]
12:51:10.774 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:51:10.775 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT dt.id,dt.template_type_id,dtt.name AS templateTypeName,dt.title,dt.content,su.user_name as createUserName,dt.create_time,s.shop_name,s.id shopId FROM dub_template dt 
LEFT JOIN dub_template_type dtt
ON dtt.id = dt.template_type_id
LEFT JOIN sys_user su 
ON su.id = dt.create_id
LEFT JOIN dub_shop s ON s.id = dt.shop_id 
WHERE dt.del_status != 1 and dtt.del_status != 1 
 ) t]
12:51:10.784 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:51:10.785 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT dt.id,dt.template_type_id,dtt.name AS templateTypeName,dt.title,dt.content,su.user_name as createUserName,dt.create_time,s.shop_name,s.id shopId FROM dub_template dt 
LEFT JOIN dub_template_type dtt
ON dtt.id = dt.template_type_id
LEFT JOIN sys_user su 
ON su.id = dt.create_id
LEFT JOIN dub_shop s ON s.id = dt.shop_id 
WHERE dt.del_status != 1 and dtt.del_status != 1 
  order by dt.create_time DESC  LIMIT ? OFFSET ? ]
12:51:11.077 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:51:13.342 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:51:13.342 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 ) t]
12:51:13.344 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:51:13.344 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 order by p.create_time DESC  LIMIT ? OFFSET ? ]
12:51:17.635 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:51:17.635 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t]
12:51:18.934 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
12:51:18.935 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
12:51:18.935 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
12:51:18.935 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
12:51:18.935 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:51:18.935 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
12:51:18.936 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
12:51:18.939 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:51:20.122 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:57:55.531 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
12:57:55.531 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
12:57:55.535 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
12:57:55.536 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
12:57:55.558 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
12:57:55.555 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
12:57:55.562 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
12:57:55.580 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
12:57:56.615 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:01:14.840 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:01:14.841 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:01:14.844 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:01:14.909 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:01:14.911 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:01:14.942 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:01:14.956 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:01:14.960 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:01:16.124 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:01:41.669 [1;31mERROR[0;39m [http-nio-8770-exec-8] c.g.f.e.h.GlobalExceptionHandler - No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call; nested exception is javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
org.springframework.dao.InvalidDataAccessApiUsageException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call; nested exception is javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:403)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:235)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:145)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy218.deleteByIdAndUserId(Unknown Source)
	at com.gl.service.shop.service.WeChatService.delTextTemplate(WeChatService.java:498)
	at com.gl.service.shop.service.WeChatService$$FastClassBySpringCGLIB$$8d341d3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.gl.service.shop.service.WeChatService$$EnhancerBySpringCGLIB$$40bb2026.delTextTemplate(<generated>)
	at com.gl.service.shop.controller.WeChatController.delTextTemplate(WeChatController.java:100)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:931)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:687)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:295)
	at com.sun.proxy.$Proxy154.remove(Unknown Source)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$DeleteExecution.doExecute(JpaQueryExecution.java:275)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:88)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:155)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:143)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:159)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 111 common frames omitted
13:04:26.566 [1;31mERROR[0;39m [http-nio-8770-exec-5] c.g.f.e.h.GlobalExceptionHandler - No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call; nested exception is javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
org.springframework.dao.InvalidDataAccessApiUsageException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call; nested exception is javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:403)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:235)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:145)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy218.deleteByIdAndUserId(Unknown Source)
	at com.gl.service.shop.service.WeChatService.delTextTemplate(WeChatService.java:498)
	at com.gl.service.shop.service.WeChatService$$FastClassBySpringCGLIB$$8d341d3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.gl.service.shop.service.WeChatService$$EnhancerBySpringCGLIB$$40bb2026.delTextTemplate(<generated>)
	at com.gl.service.shop.controller.WeChatController.delTextTemplate(WeChatController.java:100)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:931)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:687)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:295)
	at com.sun.proxy.$Proxy154.remove(Unknown Source)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$DeleteExecution.doExecute(JpaQueryExecution.java:275)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:88)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:155)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:143)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:159)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 111 common frames omitted
13:05:07.399 [1;31mERROR[0;39m [http-nio-8770-exec-8] c.g.f.e.h.GlobalExceptionHandler - No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call; nested exception is javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
org.springframework.dao.InvalidDataAccessApiUsageException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call; nested exception is javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:403)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:235)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:145)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy218.deleteByIdAndUserId(Unknown Source)
	at com.gl.service.shop.service.WeChatService.delTextTemplate(WeChatService.java:498)
	at com.gl.service.shop.service.WeChatService$$FastClassBySpringCGLIB$$8d341d3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.gl.service.shop.service.WeChatService$$EnhancerBySpringCGLIB$$40bb2026.delTextTemplate(<generated>)
	at com.gl.service.shop.controller.WeChatController.delTextTemplate(WeChatController.java:100)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:931)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:687)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.persistence.TransactionRequiredException: No EntityManager with actual transaction available for current thread - cannot reliably process 'remove' call
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:295)
	at com.sun.proxy.$Proxy154.remove(Unknown Source)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$DeleteExecution.doExecute(JpaQueryExecution.java:275)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:88)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:155)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:143)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:159)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 111 common frames omitted
13:05:07.538 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId] c.gl.config.mqtt.MqttClientCallBack - admin-service-clientIdclient 与服务器断开连接！！已断开连接
13:05:07.538 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId-Sub] c.g.c.mqtt.MqttSubscriptCallBack - admin-service-clientId-Sub与服务器断开连接！！已断开连接
13:20:05.852 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:30:47.026 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:30:47.028 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:30:47.028 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:30:47.030 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:30:47.032 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:30:47.032 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:30:47.043 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:30:47.109 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:30:48.972 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:31:48.936 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:31:48.936 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:31:48.937 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:31:48.938 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:31:48.939 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:31:48.941 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:31:48.950 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:31:48.976 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:31:52.729 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:38:41.966 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:38:41.966 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:38:41.990 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:38:42.013 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:38:42.014 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:38:42.331 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:38:42.360 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:38:42.365 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:38:42.750 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:39:13.773 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:39:13.779 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:39:13.779 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:39:13.781 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:39:13.782 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:39:14.222 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:39:14.223 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:39:14.226 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:39:15.086 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:49:29.587 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:49:29.587 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:49:29.591 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:49:29.597 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:49:29.597 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:49:29.602 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:49:29.606 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:49:29.613 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:49:30.572 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:55:06.393 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:55:06.420 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:55:06.421 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:55:06.423 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:55:06.423 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:55:06.423 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:55:06.423 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:55:06.445 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:55:07.596 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:57:25.275 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:59:22.174 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
13:59:22.193 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
13:59:22.195 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
13:59:22.195 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
13:59:22.197 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
13:59:22.204 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
13:59:22.207 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
13:59:22.231 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
13:59:50.204 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:02:25.018 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:02:25.019 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
14:02:25.023 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
14:02:25.024 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
14:02:25.028 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
14:02:25.028 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
14:02:25.029 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
14:02:25.032 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
14:02:27.960 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:06:36.880 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:07:44.047 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:08:37.410 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:08:37.432 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
14:08:37.433 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
14:08:37.434 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
14:08:37.434 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
14:08:37.435 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
14:08:37.444 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
14:08:37.458 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
14:08:42.218 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:14:42.850 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:15:17.306 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
14:15:17.306 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
14:15:17.342 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
14:15:17.342 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
14:15:17.342 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
14:15:17.347 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:15:17.349 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
14:15:17.356 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
14:15:18.619 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:15:32.562 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
14:15:32.569 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
14:15:32.569 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
14:15:32.569 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
14:15:32.569 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
14:15:32.571 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
14:15:32.572 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
14:15:32.573 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
14:16:58.191 [34mINFO [0;39m [schedule-pool-2] sys-user - [127.0.0.1]内网IP[admin][Logout][退出成功]
14:17:01.974 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
14:17:01.974 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
14:17:02.097 [34mINFO [0;39m [schedule-pool-4] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
14:17:08.012 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
14:17:08.012 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
14:17:08.015 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
14:17:08.016 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
14:17:08.016 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
14:17:08.018 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
14:17:08.316 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
14:17:30.101 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
14:17:30.101 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
14:17:30.101 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
14:17:30.102 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
14:17:30.105 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
14:17:30.106 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
14:17:30.113 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
14:19:51.581 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
14:19:51.581 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
14:19:51.581 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
14:19:51.584 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
14:19:51.590 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
14:19:51.593 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
14:19:51.607 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
15:14:31.608 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
15:14:31.726 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 12596 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
15:14:31.728 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
15:14:37.637 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
15:14:37.638 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
15:14:38.704 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1049 ms. Found 54 JPA repository interfaces.
15:14:39.721 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
15:14:39.737 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
15:14:40.032 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@d653e41' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
15:14:40.055 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
15:14:40.097 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
15:14:40.127 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
15:14:40.133 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
15:14:40.901 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
15:14:40.926 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
15:14:40.927 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
15:14:40.928 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
15:14:41.247 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
15:14:41.247 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9329 ms
15:14:42.066 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
15:14:42.155 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
15:14:43.187 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
15:14:43.426 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
15:14:43.570 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
15:14:43.912 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
15:14:44.473 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
15:14:46.082 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
15:14:46.093 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
15:14:47.801 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
15:14:47.801 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
15:14:47.805 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
15:14:51.719 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
15:14:53.310 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
15:14:53.606 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18f90bd8, org.springframework.security.web.context.SecurityContextPersistenceFilter@2d5cb059, org.springframework.security.web.header.HeaderWriterFilter@4374c051, org.springframework.security.web.authentication.logout.LogoutFilter@765d2d4d, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@7ef41ca2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@15beb40b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@660dcd17, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ff9d66b, org.springframework.security.web.session.SessionManagementFilter@6dac64ea, org.springframework.security.web.access.ExceptionTranslationFilter@6a275836, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3a8dd899]
15:14:54.043 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
15:14:54.045 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
15:14:54.046 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
15:14:54.046 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
15:14:54.122 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
15:14:54.455 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 24.351 seconds (JVM running for 26.319)
15:14:54.469 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

15:14:55.191 [34mINFO [0;39m [RMI TCP Connection(13)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:14:55.191 [34mINFO [0;39m [RMI TCP Connection(13)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
15:14:55.197 [34mINFO [0;39m [RMI TCP Connection(13)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
15:27:15.599 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:27:15.629 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:27:15.630 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1) t]
15:27:15.653 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:27:15.654 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 order by m.create_time DESC  LIMIT ? OFFSET ? ]
15:27:16.907 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:27:16.922 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:27:16.922 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
 o.id, 
 o.out_trade_no ,
  p.`name` packagesName,
  o.amount,
  u.nickname,
  u.phone,
  s.shop_name,
  d.`name` deviceName,
  d.sn deviceSn,
  o.`status`,
  o.create_time,
  o.time_expire,
  0 invoiceStatus 
FROM
  dub_order o
  LEFT JOIN dub_device d ON d.id = o.device_id
  LEFT JOIN dub_shop s ON s.id = o.shop_id
  LEFT JOIN dub_paid_packages p ON p.id = o.package_id
  LEFT JOIN dub_wechat_user u ON u.id = o.user_id  WHERE 1=1 ) t]
15:27:18.020 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:27:18.020 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 ) t]
15:27:18.027 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:27:18.028 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 order by p.create_time DESC  LIMIT ? OFFSET ? ]
15:27:33.706 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:27:33.706 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`sn`)) deviceNames,
 group_concat(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  GROUP BY b.id ) t]
15:27:34.953 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:27:34.953 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT vp.*,wu.nickname ,wu.phone,vw.create_time,s.shop_name 
 FROM dub_voice_packet vp
LEFT JOIN dub_voice_work vw
ON vp.id = vw.voice_id
LEFT JOIN dub_wechat_user wu
ON wu.id = vw.user_id
LEFT JOIN dub_shop s ON s.id = vp.shop_id 
WHERE vw.del_status != 1
) t]
15:27:35.250 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:28:12.641 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:28:12.641 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`sn`)) deviceNames,
 group_concat(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  GROUP BY b.id ) t]
15:28:16.161 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:28:16.471 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
15:28:16.471 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
15:28:16.472 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
15:28:16.473 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:28:16.473 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
15:28:16.473 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
15:28:16.486 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
15:28:17.133 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:32:39.465 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:32:51.577 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:32:51.583 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:32:51.583 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
15:32:51.584 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
15:32:51.585 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
15:32:51.592 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
15:32:51.597 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
15:32:51.613 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
15:32:54.969 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:33:00.227 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:33:00.227 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
15:33:00.232 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
15:33:00.233 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
15:33:00.235 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:33:00.236 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
15:33:00.241 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
15:33:00.247 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
15:34:28.780 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[]
15:35:09.257 [1;31mERROR[0;39m [MQTT Ping: admin-service-clientId] o.e.p.c.mqttv3.internal.ClientState - admin-service-clientId: Timed out as no write activity, keepAlive=20,000,000,000 lastOutboundActivity=26,357,386,073,900 lastInboundActivity=26,357,427,343,300 time=26,398,154,103,300 lastPing=26,357,386,080,400
15:35:09.258 [1;31mERROR[0;39m [MQTT Ping: admin-service-clientId-Sub] o.e.p.c.mqttv3.internal.ClientState - admin-service-clientId-Sub: Timed out as no write activity, keepAlive=20,000,000,000 lastOutboundActivity=26,357,386,371,000 lastInboundActivity=26,357,427,591,600 time=26,398,153,685,700 lastPing=26,357,386,639,300
15:35:09.289 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId-Sub] c.g.c.mqtt.MqttSubscriptCallBack - admin-service-clientId-Sub与服务器断开连接！！已断开连接
15:35:09.289 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId] c.gl.config.mqtt.MqttClientCallBack - admin-service-clientIdclient 与服务器断开连接！！已断开连接
15:35:32.522 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Logout][退出成功]
15:35:36.669 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
15:35:36.669 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
15:35:36.799 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Error][Bad credentials]
15:35:44.958 [34mINFO [0;39m [http-nio-8770-exec-9] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
15:35:44.958 [34mINFO [0;39m [http-nio-8770-exec-9] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
15:35:45.057 [34mINFO [0;39m [schedule-pool-2] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
15:35:49.118 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 
15:35:49.119 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:35:49.119 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1 ) t]
15:35:51.924 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
15:35:51.925 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:35:51.925 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
]
15:35:52.242 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
15:35:52.242 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
15:35:52.242 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
15:35:52.244 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
15:58:38.746 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:58:38.746 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
15:58:39.741 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询用户列表，sql:SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL 
15:58:39.757 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:58:39.757 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL ]
15:59:19.153 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:59:19.154 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
15:59:19.992 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询用户列表，sql:SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL 
15:59:19.993 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:59:19.993 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL ]
15:59:32.846 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:59:32.846 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 GROUP BY shop.id, ref2.user_id) t]
15:59:41.941 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询用户列表，sql:SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL 
15:59:41.941 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:59:41.941 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL ]
16:01:14.075 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询用户列表，sql:SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL 
16:01:14.075 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:01:14.076 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL ]
16:02:00.395 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询用户列表，sql:SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL 
16:02:00.396 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:02:00.396 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL ]
16:02:07.343 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Logout][退出成功]
16:02:12.239 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
16:02:12.239 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
16:02:12.385 [34mINFO [0;39m [schedule-pool-3] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
16:02:18.930 [34mINFO [0;39m [http-nio-8770-exec-7] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:02:19.893 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
16:33:44.507 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:50:52.497 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
16:50:52.518 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 24268 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
16:50:52.518 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
16:50:56.383 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
16:50:56.384 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
16:50:57.342 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 945 ms. Found 54 JPA repository interfaces.
16:50:58.133 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
16:50:58.146 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
16:50:58.569 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@4fa86cb8' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:50:58.583 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:50:58.605 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:50:58.622 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:50:58.625 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:50:59.289 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
16:50:59.310 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
16:50:59.310 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
16:50:59.310 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
16:50:59.442 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
16:50:59.442 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6876 ms
16:51:00.385 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
16:51:00.424 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
16:51:01.881 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
16:51:02.076 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
16:51:02.142 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
16:51:02.906 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
16:51:03.296 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
16:51:05.972 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
16:51:05.984 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
16:51:07.893 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
16:51:07.893 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
16:51:07.910 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
16:51:17.244 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
16:51:20.360 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
16:51:20.821 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@13e9a2f2, org.springframework.security.web.context.SecurityContextPersistenceFilter@4e38b4ea, org.springframework.security.web.header.HeaderWriterFilter@26cd77cc, org.springframework.security.web.authentication.logout.LogoutFilter@1a6864f0, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@7a1371, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@21eb33d4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@654be52b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@74eebb25, org.springframework.security.web.session.SessionManagementFilter@4a81582c, org.springframework.security.web.access.ExceptionTranslationFilter@6c6a7553, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@746fda68]
16:51:21.873 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
16:51:21.874 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
16:51:21.874 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
16:51:21.874 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
16:51:22.006 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
16:51:23.034 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 31.531 seconds (JVM running for 34.356)
16:51:23.050 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

16:51:27.519 [34mINFO [0;39m [http-nio-8770-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:51:27.519 [34mINFO [0;39m [http-nio-8770-exec-2] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
16:51:27.521 [34mINFO [0;39m [http-nio-8770-exec-2] o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
16:51:27.794 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
16:51:27.794 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
16:51:28.534 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
16:51:34.263 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:51:35.403 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
16:52:27.354 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
16:52:27.355 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
16:52:27.355 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
16:52:27.367 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
16:52:27.380 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
16:52:27.383 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
16:52:27.389 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
16:52:45.959 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
16:52:45.980 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 3408 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
16:52:45.981 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
16:52:48.732 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
16:52:48.733 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
16:52:49.998 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1258 ms. Found 54 JPA repository interfaces.
16:52:50.334 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
16:52:50.344 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
16:52:51.005 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@cbd40c1' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:52:51.015 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:52:51.030 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:52:51.041 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:52:51.043 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:52:51.360 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
16:52:51.369 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
16:52:51.369 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
16:52:51.369 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
16:52:51.831 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
16:52:51.832 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5810 ms
16:52:52.385 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
16:52:52.419 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
16:52:53.292 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
16:52:53.468 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
16:52:53.514 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
16:52:53.631 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
16:52:54.473 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
16:52:56.387 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
16:52:56.396 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
16:52:58.428 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
16:52:58.428 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
16:52:58.429 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
16:53:03.460 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
16:53:05.385 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
16:53:05.541 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@14eaab92, org.springframework.security.web.context.SecurityContextPersistenceFilter@6d03404e, org.springframework.security.web.header.HeaderWriterFilter@20f1e26, org.springframework.security.web.authentication.logout.LogoutFilter@58d85a00, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@49038f97, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20e1ce62, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@73c71083, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@46a6b8d6, org.springframework.security.web.session.SessionManagementFilter@19324eab, org.springframework.security.web.access.ExceptionTranslationFilter@4f991f00, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@377fca35]
16:53:05.786 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
16:53:05.787 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
16:53:05.787 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
16:53:05.787 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
16:53:05.808 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
16:53:05.933 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 20.864 seconds (JVM running for 23.647)
16:53:05.939 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

16:53:06.455 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:53:06.456 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
16:53:06.461 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
16:53:51.144 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:51.145 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE user_id = ?]
16:53:51.270 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:51.270 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE shop_id = ?]
16:53:51.436 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:51.474 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:51.487 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:51.487 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (21) GROUP BY shop.id, ref2.user_id) t]
16:53:51.534 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:51.534 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (21) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
16:53:54.300 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:54.313 [34mINFO [0;39m [http-nio-8770-exec-3] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:54.320 [34mINFO [0;39m [http-nio-8770-exec-3] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:54.320 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:53:54.320 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:53:54.325 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.s.c.service.CommercialService - args = [21, 21]
16:53:54.326 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:54.326 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL  AND s.id = ?  AND s.id in (?)
GROUP BY wu.id ) t]
16:53:56.509 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:56.527 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:53:56.527 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:53:56.552 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:56.562 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:56.568 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:56.585 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and  ( create_id = ?  or   s.id in (?)
 )
16:53:56.586 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:56.586 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and  ( create_id = ?  or   s.id in (?)
 )) t]
16:53:58.712 [34mINFO [0;39m [http-nio-8770-exec-7] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:58.724 [34mINFO [0;39m [http-nio-8770-exec-7] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:53:58.735 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:58.735 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (21) GROUP BY shop.id, ref2.user_id) t]
16:53:58.783 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:53:58.783 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (21) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
16:54:01.559 [34mINFO [0;39m [http-nio-8770-exec-9] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:01.566 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:01.570 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:01.572 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:54:01.572 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:54:01.574 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:01.869 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and s.id = ?  and s.id = ?  and  ( create_id = ?  or   s.id in (?)
 )
16:54:01.869 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:54:01.869 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and s.id = ?  and s.id = ?  and  ( create_id = ?  or   s.id in (?)
 )) t]
16:54:03.867 [34mINFO [0;39m [http-nio-8770-exec-10] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:03.876 [34mINFO [0;39m [http-nio-8770-exec-10] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:03.892 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.s.c.service.CommercialService - args = [21]
16:54:03.893 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:54:03.893 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL  AND s.id in (?)
GROUP BY wu.id ) t]
16:54:04.126 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:04.159 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:54:04.607 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=21, parentId=0, type=1, shopName=测试, createUserId=0, createTime=2025-07-01 16:53:51.0, status=1)]
16:54:06.056 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:06.059 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:54:06.066 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:54:06.066 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (21) GROUP BY shop.id, ref2.user_id) t]
16:54:06.068 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:54:06.069 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (21) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
16:54:09.035 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [SELECT shop.id, shop.shop_name, COUNT(s2.id) AS shop_count FROM dub_shop shop LEFT JOIN dub_shop s2 ON s2.parent_id = shop.id AND s2.`status` = 1 WHERE shop.parent_id = 0 AND shop.`status` = 1 GROUP BY shop.id HAVING shop_count = 0]
16:54:15.708 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:54:15.708 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE ref.id IS NOT NULL AND wechat.phone <> ''  AND wechat.phone IS NOT NULL AND ref.shop_id = ?]
16:54:17.153 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询用户列表，sql:SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL 
16:54:17.153 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:54:17.153 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL ]
16:54:21.879 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询店铺详情，id:21
16:54:21.890 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:54:21.891 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT shop.id AS id, shop.shop_name AS shopName, u.id AS userId, u.nickname AS userName, u.phone AS userPhone FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id AND ref.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref.user_id WHERE shop.id = ?]
16:54:21.899 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
16:55:25.135 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 删除店铺，shopIds:[21]
16:55:25.146 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询店铺列表，list:[]
16:55:25.696 [34mINFO [0;39m [http-nio-8770-exec-10] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
17:03:17.876 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
17:04:35.752 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
17:04:35.752 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE user_id = ?]
17:05:15.514 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
17:05:15.514 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE shop_id = ?]
17:05:22.429 [1;31mERROR[0;39m [MQTT Ping: admin-service-clientId-Sub] o.e.p.c.mqttv3.internal.ClientState - admin-service-clientId-Sub: Timed out as no write activity, keepAlive=20,000,000,000 lastOutboundActivity=31,748,458,430,200 lastInboundActivity=31,748,493,725,700 time=31,804,407,105,700 lastPing=31,748,458,434,200
17:05:22.429 [1;31mERROR[0;39m [MQTT Ping: admin-service-clientId] o.e.p.c.mqttv3.internal.ClientState - admin-service-clientId: Timed out as no write activity, keepAlive=20,000,000,000 lastOutboundActivity=31,748,458,430,300 lastInboundActivity=31,748,493,725,700 time=31,804,418,378,000 lastPing=31,748,458,434,300
17:05:35.224 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId-Sub] c.g.c.mqtt.MqttSubscriptCallBack - admin-service-clientId-Sub与服务器断开连接！！已断开连接
17:05:35.224 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId] c.gl.config.mqtt.MqttClientCallBack - admin-service-clientIdclient 与服务器断开连接！！已断开连接
17:05:51.301 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
17:05:51.307 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
17:05:51.313 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
17:05:51.313 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id) t]
17:05:51.324 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
17:05:51.324 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
17:05:52.760 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
17:05:52.772 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.shop.service.ShopService - 查询店铺详情，id:22
17:05:52.773 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
17:05:52.773 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT shop.id AS id, shop.shop_name AS shopName, u.id AS userId, u.nickname AS userName, u.phone AS userPhone FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id AND ref.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref.user_id WHERE shop.id = ?]
17:05:55.032 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
17:05:55.033 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE ref.id IS NOT NULL AND wechat.phone <> ''  AND wechat.phone IS NOT NULL AND ref.shop_id = ?]
17:05:59.016 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询店铺详情，id:22
17:05:59.016 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
17:05:59.016 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT shop.id AS id, shop.shop_name AS shopName, u.id AS userId, u.nickname AS userName, u.phone AS userPhone FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id AND ref.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref.user_id WHERE shop.id = ?]
17:05:59.023 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
17:06:22.725 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Logout][退出成功]
