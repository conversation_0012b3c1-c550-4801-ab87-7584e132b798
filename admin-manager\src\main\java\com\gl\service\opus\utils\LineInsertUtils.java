package com.gl.service.opus.utils;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-09-15:47
 */
public class LineInsertUtils {
    public static Double mapRange(double input, double inputMin, double inputMax, double outputMin, double outputMax) {
        if(input<inputMin){
            input=inputMin;
        }
        if(input>inputMax){
            input=inputMax;
        }
        double inputRange = inputMax - inputMin;
        double outputRange = outputMax - outputMin;
        double output = (input - inputMin) * (outputRange / inputRange) + outputMin;
        output = Math.max(outputMin, Math.min(output, outputMax));
        return output;
    }
}
