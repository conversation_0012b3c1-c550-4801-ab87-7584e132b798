package com.gl.framework.web.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * VO基类
 */
public class BaseVo {

    private Long id;
    /**
     * 页指针
     */
    @JsonIgnore
    private Integer pageNumber;

    /**
     * 页大小
     */
    @JsonIgnore
    private Integer pageSize;
    /**
     * 开始时间
     */
    @Parameter(hidden = true)
    @JsonIgnore
    private String beginTime;

    /**
     * 结束时间
     */
    @Parameter(hidden = true)
    @JsonIgnore
    private String endTime;

    private String prop;
    private String order;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getProp() {
        return prop;
    }

    public void setProp(String prop) {
        this.prop = prop;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
