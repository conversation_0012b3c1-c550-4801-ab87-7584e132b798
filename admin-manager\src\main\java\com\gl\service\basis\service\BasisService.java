package com.gl.service.basis.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.basis.entity.BaseAbout;
import com.gl.service.basis.entity.BaseService;
import com.gl.service.basis.repository.BaseAboutRepository;
import com.gl.service.basis.repository.BaseServiceRepository;
import com.gl.service.basis.vo.BasisVo;
import com.gl.service.music.repository.BackgroundMusicTypeRepository;
import com.gl.service.opus.entity.BackgroundMusicType;
import com.gl.service.opus.entity.TemplateType;
import com.gl.service.template.repository.TemplateTypeRepository;
import com.gl.system.vo.SysUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 基础设置
 *
 * @author: duanjinze
 * @date: 2022/11/11 15:14
 * @version: 1.0
 */
@Service
@Slf4j
public class BasisService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private BaseAboutRepository baseAboutRepository;
    @Autowired
    private BaseServiceRepository baseServiceRepository;
    @Autowired
    private BackgroundMusicTypeRepository backgroundMusicTypeRepository;
    @Autowired
    private TemplateTypeRepository templateTypeRepository;

    public Result select() {
        String baseServiceSql = "select * from dub_base_service limit 1 ";
        List<BaseService> baseServices = jdbcTemplate.query(baseServiceSql, new BeanPropertyRowMapper<>(BaseService.class));
        BaseService baseService = null;
        if (!baseServices.isEmpty()) {
            baseService = baseServices.get(0);
        }
        String baseAboutSql = "select * from dub_base_about limit 1 ";
        List<BaseAbout> baseAbouts = jdbcTemplate.query(baseAboutSql, new BeanPropertyRowMapper<>(BaseAbout.class));
        BaseAbout baseAbout = null;
        if (!baseAbouts.isEmpty()) {
            baseAbout = baseAbouts.get(0);
        }
        String templateSql = "select id,name from dub_template_type where del_status != 1 ";
        List<TemplateType> templateTypes = jdbcTemplate.query(templateSql, new BeanPropertyRowMapper<>(TemplateType.class));

        List<BackgroundMusicType> backgroundMusicTypes = backgroundMusicTypeRepository.findAll();

        BasisVo basisVo = new BasisVo();
        basisVo.setBaseService(baseService);
        basisVo.setBaseAbout(baseAbout);
        basisVo.setTemplateTypes(templateTypes);
        basisVo.setBackgroundMusicTypes(backgroundMusicTypes);
        return Result.success(basisVo);
    }

    public Result add(BasisVo vo) {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();

        BaseService baseService = vo.getBaseService();
        BaseAbout baseAbout = vo.getBaseAbout();
        List<TemplateType> templateTypes = vo.getTemplateTypes();
        List<BackgroundMusicType> backgroundMusicTypes = vo.getBackgroundMusicTypes();

        if (baseService != null) {
            baseServiceRepository.save(baseService);//无论是否带了id，如果有id则是修改，没有则新增
        }
        if (baseAbout != null) {
            baseAboutRepository.save(baseAbout);
        }
        if (!templateTypes.isEmpty()) {
            List<TemplateType> templateTypeList = new ArrayList<>();
            for (TemplateType templateType : templateTypes) {
                Long id = templateType.getId();
                if (id == null) {
                    templateType.setDelStatus(0);
                    templateType.setCreateId(user.getId());
                    templateType.setCreateTime(new Date());
                } else {
                    Optional<TemplateType> templateTypeOptional = templateTypeRepository.findById(id);
                    if (templateTypeOptional.isPresent()) {
                        templateTypeOptional.get().setName(templateType.getName());
                        templateType = templateTypeOptional.get();
                    }
                }
                templateTypeList.add(templateType);
            }
            templateTypeRepository.saveAll(templateTypeList);
        }
        if (!backgroundMusicTypes.isEmpty()) {
            backgroundMusicTypeRepository.saveAll(backgroundMusicTypes);
        }
        return Result.success();
    }

    public Result deleteTemplate(Long templateTypeId) {
        if (templateTypeId == null) {
            return Result.fail("模板类型id不能为空");
        }
        templateTypeRepository.updateDelStatusById(templateTypeId);
        return Result.success();
    }

    public Result deleteMusic(Long musicTypeId) {
        if (musicTypeId == null) {
            return Result.fail("背景音乐类型不能为空");
        }
        backgroundMusicTypeRepository.deleteById(musicTypeId);
        return Result.success();
    }
}
