-------------------------------------------------------------------------------
Test set: com.gl.service.commercial.controller.CommercialControllerTest
-------------------------------------------------------------------------------
Tests run: 14, Failures: 7, Errors: 0, Skipped: 0, Time elapsed: 31.715 s <<< FAILURE! - in com.gl.service.commercial.controller.CommercialControllerTest
testList_Unauthenticated_ShouldReturnUnauthorized  Time elapsed: 0.164 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.gl.service.commercial.controller.CommercialControllerTest.testList_Unauthenticated_ShouldReturnUnauthorized(CommercialControllerTest.java:127)

testExportList_MissingCSRFToken_ShouldReturnForbidden  Time elapsed: 0.04 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<403> but was:<200>
	at com.gl.service.commercial.controller.CommercialControllerTest.testExportList_MissingCSRFToken_ShouldReturnForbidden(CommercialControllerTest.java:316)

testExportList_NoPermission_ShouldReturnForbidden  Time elapsed: 0.04 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<403> but was:<200>
	at com.gl.service.commercial.controller.CommercialControllerTest.testExportList_NoPermission_ShouldReturnForbidden(CommercialControllerTest.java:231)

testExportList_ServiceThrowsIOException_ShouldHandleException  Time elapsed: 0.26 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<500> but was:<200>
	at com.gl.service.commercial.controller.CommercialControllerTest.testExportList_ServiceThrowsIOException_ShouldHandleException(CommercialControllerTest.java:301)

testExportList_InvalidJsonFormat_ShouldReturnBadRequest  Time elapsed: 0.264 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<200>
	at com.gl.service.commercial.controller.CommercialControllerTest.testExportList_InvalidJsonFormat_ShouldReturnBadRequest(CommercialControllerTest.java:281)

testExportList_Unauthenticated_ShouldReturnUnauthorized  Time elapsed: 0.029 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.gl.service.commercial.controller.CommercialControllerTest.testExportList_Unauthenticated_ShouldReturnUnauthorized(CommercialControllerTest.java:246)

testList_NoPermission_ShouldReturnForbidden  Time elapsed: 0.029 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<403> but was:<200>
	at com.gl.service.commercial.controller.CommercialControllerTest.testList_NoPermission_ShouldReturnForbidden(CommercialControllerTest.java:114)

