package com.gl.service.device.vo.dto;

import com.gl.framework.web.domain.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: duanjinze
 * @date: 2022/11/10 17:50
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceDto extends BaseVo {
    /**
     * 设备表id
     */
    private Long deviceId;

    /**
     * (设备状态)在线状态 0离线 1在线
     */
    private Integer status;

    /**
     * 绑定状态 0未绑定 1已绑定
     */
    private Integer bindStatus;

    /**
     * 使用状态 0停用 1使用
     */
    private Integer useStatus;

    /**
     * 关键词【设备名称/设备ID/所属用户/用户手机】
     */
    private String searchCondition;

    /**
     * 设备id集合
     */
    private List<Long> ids;

    private Long shopId;
}
