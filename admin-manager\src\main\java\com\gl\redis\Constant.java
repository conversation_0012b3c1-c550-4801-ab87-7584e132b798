package com.gl.redis;

/**
 * Redis Key定义常量类
 */
public class Constant {
	/**
	 * 当前使用的系统参数Key
	 */
	public final static String MZJ_MG_PARAMS = "MZJ_PY_SYS_PARAMS";

	/**
	 * access_token缓存key
	 */
	public final static String ACCESS_TOKEN = "access_token";

	/**
	 * refresh_token缓存key
	 */
	public final static String REFRESH_TOKEN = "refresh_token";

	/**
	 * 系统参数HASH KEY
	 */
	public final static String MZJ_PY_SYS_PARAMS = "MZJ_PY_SYS_PARAMS";

	/**
	 * 系统参数 播音率
	 */
	public final static String SAMPLE_RATE = "sample_rate";

	/**
	 * 语音合成token
	 */
	public final static String VOICE_TOKEN = "voice:token";
}