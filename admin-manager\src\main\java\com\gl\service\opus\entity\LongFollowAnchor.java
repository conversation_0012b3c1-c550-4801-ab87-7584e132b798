package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-05-30-16:11
 */
@Data
@Entity
@Table(name = "long_follow_anchor")
public class LongFollowAnchor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "user_id", nullable = true)
    private Long userId; // 小程序openid

    @Basic
    @Column(name = "anchor_id", nullable = true)
    private Long anchorId; // 小程序openid
}
