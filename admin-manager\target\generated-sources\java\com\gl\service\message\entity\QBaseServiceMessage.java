package com.gl.service.message.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBaseServiceMessage is a Querydsl query type for BaseServiceMessage
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBaseServiceMessage extends EntityPathBase<BaseServiceMessage> {

    private static final long serialVersionUID = 1769345586L;

    public static final QBaseServiceMessage baseServiceMessage = new QBaseServiceMessage("baseServiceMessage");

    public final StringPath content = createString("content");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final DateTimePath<java.util.Date> messageTime = createDateTime("messageTime", java.util.Date.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QBaseServiceMessage(String variable) {
        super(BaseServiceMessage.class, forVariable(variable));
    }

    public QBaseServiceMessage(Path<? extends BaseServiceMessage> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBaseServiceMessage(PathMetadata metadata) {
        super(BaseServiceMessage.class, metadata);
    }

}

