package com.gl.service.opus.repository;

import com.gl.service.opus.dto.AnchorDTO;
import com.gl.service.opus.entity.LongAnchor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface LongAnchorRepository extends JpaRepository<LongAnchor,Long>, JpaSpecificationExecutor<LongAnchor> {

    @Query(value = "select * from long_anchor group by  usage_scenario",nativeQuery = true)
    List<LongAnchor> findAllType();
    List<LongAnchor> findAllByVoiceName(String voiceName);

    @Query(value = "select new com.gl.service.opus.dto.AnchorDTO(v.id,v.name,v.usageScenario,v.typeName,v.url," +
            "v.voiceUrl) " +
            "from LongAnchor v join LongFollowAnchor vf on v.id = vf.anchorId where vf.userId=:userId")
    List<AnchorDTO> taijia(Long userId);
}
