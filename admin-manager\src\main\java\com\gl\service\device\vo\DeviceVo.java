package com.gl.service.device.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备表
 * @author: duanjinze
 * @date: 2022/11/10 13:35
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceVo extends UserVo {
    private Long id;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备SN
     */
    private String sn;

    /**
     * 所属用户
     */
    private Long userId;

    /**
     * 音量
     */
    private Integer volume;

    /**
     * 在线状态 0离线 1在线
     */
    private Integer status;

    /**
     * 绑定状态 0未绑定 1已绑定
     * （当做解绑与绑定操作时 0解绑 1绑定）
     */
    private Integer bindStatus;

    /**
     * 使用状态 0停用 1使用
     */
    private Integer useStatus;

    /**
     * 删除状态 0未删除 1已删除
     */
    private Integer delStatus;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 在线状态更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateStatusTime;

    //===============//
    /**
     * 作品数
     */
    private Integer workCount;

    private String shopName;

    private Long shopId;
}
