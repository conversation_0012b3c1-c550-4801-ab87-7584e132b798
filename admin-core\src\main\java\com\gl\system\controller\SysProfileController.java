package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.sign.Base64;
import com.gl.framework.security.LoginUser;
import com.gl.framework.security.service.TokenService;
import com.gl.system.service.SysUserService;
import com.gl.system.vo.SysUserVo;
import com.gl.framework.web.response.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 个人中心控制器
 */
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysUserService userService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 个人信息
     *
     * @return
     */
    @GetMapping
    public Result profile() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        Result result = Result.success();
        result.addData("user", user);
        // 角色名称组
        result.addData("roleGroup", userService.selectUserRoleGroup(user.getId()));
        return result;
    }

    /**
     * 修改用户
     *
     * @param vo
     * @return
     */
    @PutMapping
    @Log(title = "个人信息", businessType = BusinessType.UPDATE, businessTypeName = "修改用户")
    public Result updateProfile(@RequestBody SysUserVo vo) {
        userService.updateUserProfile(vo);
        // 修改微信用户手机号
        jdbcTemplate.update("update dub_wechat_user set phone = ?, nickname = ? where id = ?", vo.getPhone(), vo.getUserName(), vo.getSiteId());

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        // 更新缓存用户信息
        user.setUserName(vo.getUserName());
        user.setPhone(vo.getPhone());
        user.setEmail(vo.getEmail());
        user.setGender(vo.getGender());

        tokenService.setLoginUser(loginUser);
        return Result.success();
    }

    /**
     * 修改密码
     *
     * @param oldPassword
     * @param newPassword
     * @return
     */
    @PutMapping("/reset_pwd")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE, businessTypeName = "修改密码")
    public Result updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String loginName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return Result.fail("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return Result.fail("新密码不能与旧密码相同");
        }

        String encryptPassword = SecurityUtils.encryptPassword(newPassword);

        // 修改数据库
        userService.resetUserPwd(loginName, encryptPassword);

        // 更新缓存用户密码
        loginUser.getUser().setPassword(encryptPassword);
        tokenService.setLoginUser(loginUser);
        return Result.success();
    }

    /**
     * 头像上传
     *
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/avatar")
    @Log(title = "用户头像", businessType = BusinessType.UPDATE, businessTypeName = "头像上传")
    public Result avatar(@RequestParam("avatarfile") MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            return Result.fail("File is Empty");
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        String avatarBase64 = Base64.encode(file.getBytes());

        // 更新数据库
        userService.updateUserAvatar(user.getId(), avatarBase64);

        // 更新缓存用户头像
        loginUser.getUser().setAvatar(avatarBase64);
        tokenService.setLoginUser(loginUser);

        return Result.success(avatarBase64);
    }
}
