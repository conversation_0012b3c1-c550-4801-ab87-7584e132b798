package com.gl.system.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.gl.system.entity.SysUserDept;

public interface SysUserDeptRepository extends JpaRepository<SysUserDept, Long>, JpaSpecificationExecutor<SysUserDept> {

	List<SysUserDept> findByUserId(Long userId);
}
