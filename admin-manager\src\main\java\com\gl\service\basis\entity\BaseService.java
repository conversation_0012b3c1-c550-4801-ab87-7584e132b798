package com.gl.service.basis.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * 客服联系方式
 * @author: duanjinze
 * @date: 2022/11/11 15:45
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_base_service")
public class BaseService {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 客服微信
     */
    @Basic
    @Column(name = "wechat")
    private String wechat;

    /**
     * 客服电话
     */
    @Basic
    @Column(name = "phone")
    private String phone;
}
