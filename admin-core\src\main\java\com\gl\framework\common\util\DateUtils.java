package com.gl.framework.common.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;

import java.util.Date;

/**
 * 日期时间工具类（继承自hutool中的DateUtil，可做拓展）
 */
public class DateUtils extends cn.hutool.core.date.DateUtil {

	/**
	 * 现在距离明天{hour}点的{unit}数
	 *
	 * @param hour 明天几点（如：明天凌晨3点则传3）
	 * @param unit 相差的单位：相差 小时{@link DateUnit#HOUR}、分钟{@link DateUnit#MINUTE} 等
	 * @return 时间差
	 */
	public static long betweenTomorrow(int hour, DateUnit unit) {
		DateTime tomorrow = tomorrow();
		DateTime tomorrow0Hour = beginOfDay(tomorrow);
		DateTime tomorrowXHour = offsetHour(tomorrow0Hour, hour);
		return between(new Date(), tomorrowXHour, unit, true);
	}

}
