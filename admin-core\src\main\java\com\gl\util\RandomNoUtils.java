package com.gl.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Random;

public class RandomNoUtils {

    /**
     * 输入number 生成指定规格的随机码
     *
     * @param firstNumber  产生的位数
     * @param secondNumber 产生的位数
     * @return
     * @throws Exception
     */
    public synchronized static String getRandomNo(Integer firstNumber, Integer secondNumber) {
        StringBuilder stringBuilder = new StringBuilder();
        String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String seconds = new SimpleDateFormat("HHmmss").format(new Date());
        String milli = new SimpleDateFormat("SSS").format(new Date());
        stringBuilder.append(date);
        stringBuilder.append(getRandomNum(firstNumber));
        stringBuilder.append(seconds);
        stringBuilder.append(getRandomNum(secondNumber));
        stringBuilder.append(milli);
        return stringBuilder.toString();
    }

    /**
     * 获取number个随机数
     *
     * @param number 随机数个数
     * @return
     */
    public static String getRandomNum(Integer number) {
        StringBuffer sb = new StringBuffer();
        String str = "0135792468";
        Random r = new Random();
        for (int i = 0; i < number; i++) {
            int num = r.nextInt(str.length());
            sb.append(str.charAt(num));
            str = str.replace((str.charAt(num) + ""), "");
        }
        return sb.toString();
    }

    /**
     * 获取一定长度的随机字符串
     *
     * @param length 指定字符串长度
     * @return 一定长度的字符串
     */
    public static String getRandomStringByLength(int length) {
        String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 随机指定范围内N个不重复的数
     * 利用HashSet的特征，只能存放不同的值
     *
     * @param min 指定范围最小值
     * @param max 指定范围最大值
     * @param n   随机数个数
     * @param set 随机数结果集
     */
    public static void randomSet(int min, int max, int n, HashSet<Integer> set) {
        if (n > (max - min + 1) || max < min) {
            return;
        }
        for (int i = 0; i < n; i++) {
            int num = (int) (Math.random() * (max - min)) + min;
            set.add(num);// 将不同的数存入HashSet中
        }
        int setSize = set.size();
        // 如果存入的数小于指定生成的个数，则调用递归再生成剩余个数的随机数，如此循环，直到达到指定大小
        if (setSize < n) {
            randomSet(min, max, n - setSize, set);// 递归
        }
    }
}
