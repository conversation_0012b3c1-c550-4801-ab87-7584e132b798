package com.gl.service.opus.repository;

import com.gl.service.opus.entity.Template;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface TemplateRepository extends JpaRepository<Template,Long>, JpaSpecificationExecutor<Template> {
    @Query(value = "select * from dub_template where del_status != 1",nativeQuery = true)
    List<Template> findByDelStatus();

    @Transactional
    @Modifying
    @Query(value = "update dub_template set del_status = 1 where id = ?1 ",nativeQuery = true)
    Integer updateDelStatusById(Long id);
}
