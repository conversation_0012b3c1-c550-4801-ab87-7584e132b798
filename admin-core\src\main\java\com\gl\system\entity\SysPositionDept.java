package com.gl.system.entity;

import com.gl.framework.entity.IdEntity;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 岗位和部门关联表实体
 */
@Entity
@Table(name = "sys_position_dept")
public class SysPositionDept extends IdEntity {
    /**
     * 岗位ID
     */
    private Long positionId;

    /**
     * 部门ID
     */
    private Long deptId;

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    @Override
    public String toString() {
        return "SysPositionDept{" +
                "positionId=" + positionId +
                ", deptId=" + deptId +
                '}';
    }
}
