package com.gl.system.entity;

import com.gl.framework.entity.IdEntity;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 角色部门表实体
 */
@Entity
@Table(name = "sys_role_dept")
public class SysRoleDept extends IdEntity {

    private Long roleId;
    private Long deptId;

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String toJSON() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"id\":").append(getId());
        sb.append(",\"roleId\":")
                .append(roleId);
        sb.append(",\"deptId\":")
                .append(deptId);
        sb.append('}');
        return sb.toString();
    }
}
