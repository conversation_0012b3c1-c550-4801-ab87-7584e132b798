package com.gl.service.shop.repository;

import com.gl.service.shop.entity.ShopUserRef;
import com.gl.service.shop.vo.ShopUserResVo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/2
 * @description:
 */
public interface ShopUserRefRepository extends JpaRepository<ShopUserRef, Long>, JpaSpecificationExecutor<ShopUserRef> {

    @Modifying
    @Query("delete from ShopUserRef where shopId in ?1")
    void deleteAllByShopIdIn(List<Long> shopIds);

    @Query("select shopId from ShopUserRef where userId = ?1")
    List<Long> getShopUserRefByUserId(Long siteId);

    /**
     * 判断用户是否是门店管理员
     *
     * @param userId 用户ID（微信用户ID）
     * @param role   角色标识 1-管理员 2-店员
     * @return 当且仅当存在对应记录时返回 true
     */
    boolean existsByUserIdAndRole(Long userId, Integer role);

    /**
     * 判断用户是否在指定门店集合中，拥有管理员角色
     *
     * @param userId  用户ID（微信用户ID）
     * @param role    角色标识 1-管理员 2-店员
     * @param shopIds 门店ID集合
     * @return 存在返回 true
     */
    boolean existsByUserIdAndRoleAndShopIdIn(Long userId, Integer role, List<Long> shopIds);

    ShopUserRef findByShopIdAndUserIdAndRole(Long shopId, Long wxUserId, Integer code);
}
