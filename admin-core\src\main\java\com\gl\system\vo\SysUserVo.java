package com.gl.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.framework.web.domain.BaseVo;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 用户信息VO
 */
public class SysUserVo extends BaseVo {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 分站id
     */
    private Long siteId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 登录账号
     */
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    private String loginName;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空")
    @Size(min = 0, max = 30, message = "用户姓名长度不能超过30个字符")
    private String userName;

    /**
     * 用户邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /**
     * 手机号码
     */
    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    private String phone;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private Integer gender;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否管理员 0否 1是
     */
    private Integer isAdmin;

    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 分站名称
     */
    private String siteName;
    private Long createUserId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updator;
    private Long updateUserId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 部门信息
     */
    private SysDeptVo dept;

    /**
     * 角色信息
     */
    private List<SysRoleVo> roles;

    /**
     * 角色ID组
     */
    private List<Long> roleIds;

    /**
     * 角色名称组
     */
    private List<String> roleNames;

    /**
     * 可分配角色ID组
     */
    private List<Long> allotRoleIds;

    /**
     * 岗位信息
     */
    private List<SysPositionVo> position;

    /**
     * 岗位ID组
     */
    private List<Long> positionIds;

    /**
     * 岗位名称组
     */
    private List<String> positionNames;
    
    /**
     * 删除状态0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 绑定的门店
     */
    private Long shopId;

    /**
     * 总店下的子门店
     */
    private List<Long> childShopIds;

    /**
     *  '1平台账户 2站点账户',
     */
    private Integer type;


    public SysUserVo() {
    }

    public SysUserVo(Long id, String loginName, String userName, String email, String phone, Integer gender, String avatar, Integer status, Date createTime, SysDeptVo dept) {
        this.id = id;
        this.loginName = loginName;
        this.userName = userName;
        this.email = email;
        this.phone = phone;
        this.gender = gender;
        this.avatar = avatar;
        this.status = status;
        this.createTime = createTime;
        this.dept = dept;
    }

    public SysUserVo(Long userId) {
        this.id = userId;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public SysDeptVo getDept() {
        return dept;
    }

    public void setDept(SysDeptVo dept) {
        this.dept = dept;
    }

    public List<SysRoleVo> getRoles() {
        return roles;
    }

    public void setRoles(List<SysRoleVo> roles) {
        this.roles = roles;
    }

    public List<Long> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }

    public List<String> getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(List<String> roleNames) {
        this.roleNames = roleNames;
    }

    public List<SysPositionVo> getPosition() {
        return position;
    }

    public void setPosition(List<SysPositionVo> position) {
        this.position = position;
    }

    public List<Long> getPositionIds() {
        return positionIds;
    }

    public void setPositionIds(List<Long> positionIds) {
        this.positionIds = positionIds;
    }

    public List<String> getPositionNames() {
        return positionNames;
    }

    public void setPositionNames(List<String> positionNames) {
        this.positionNames = positionNames;
    }

    public Integer getIsAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(Integer isAdmin) {
        this.isAdmin = isAdmin;
    }

    public List<Long> getAllotRoleIds() {
        return allotRoleIds;
    }

    public void setAllotRoleIds(List<Long> allotRoleIds) {
        this.allotRoleIds = allotRoleIds;
    }

    public Integer getDeleted() {
		return deleted;
	}

	public void setDeleted(Integer deleted) {
		this.deleted = deleted;
	}


    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }


    public List<Long> getChildShopIds() {
        return childShopIds;
    }

    public void setChildShopIds(List<Long> childShopIds) {
        this.childShopIds = childShopIds;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }



    @Override
    public String toString() {
        return "SysUserVo{" +
                "id=" + id +
                ", deptId=" + deptId +
                ", loginName='" + loginName + '\'' +
                ", userName='" + userName + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", gender=" + gender +
                ", avatar='" + avatar + '\'' +
                ", password='" + password + '\'' +
                ", isAdmin=" + isAdmin +
                ", status=" + status +
                ", creator='" + creator + '\'' +
                ", createUserId=" + createUserId +
                ", createTime=" + createTime +
                ", updator='" + updator + '\'' +
                ", updateUserId=" + updateUserId +
                ", updateTime=" + updateTime +
                ", dept=" + dept +
                ", roles=" + roles +
                ", roleIds=" + roleIds +
                ", roleNames=" + roleNames +
                ", position=" + position +
                ", positionIds=" + positionIds +
                ", positionNames=" + positionNames +
                '}';
    }

}
