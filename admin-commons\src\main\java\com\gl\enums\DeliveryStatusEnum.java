package com.gl.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物流状态
 */
public enum DeliveryStatusEnum {
	//0待揽收 1已揽收 2运输中 3派送中 4异常件 5退回件 6退回签收 7转寄件 8作废件 9已签收 10已取消 11下单中 12下单失败
	STATUS_0(0, "待揽收"),
	STATUS_1(1, "已揽收"),
	STATUS_2(2, "运输中"),
	STATUS_3(3, "派送中"),
	STATUS_4(4, "异常件"),
	STATUS_5(5, "退回件"),
	STATUS_6(6, "退回签收"),
	STATUS_7(7, "转寄件"),
	STATUS_8(8, "作废件"),
	STATUS_9(9, "已签收"),
	STATUS_10(10, "已取消"),
	STATUS_11(11, "下单中"),
	STATUS_12(12, "下单失败");

	private final Integer status;
	private final String msg;

	DeliveryStatusEnum(Integer status, String msg) {
		this.status = status;
		this.msg = msg;
	}

	public Integer getStatus() {
		return status;
	}

	public String getMsg() {
		return msg;
	}
	
	private static Map<Integer, String> lookupMap = new HashMap<>();

	static {
		lookupMap = EnumSet.allOf(DeliveryStatusEnum.class).stream()
				.collect(Collectors.toMap(DeliveryStatusEnum::getStatus, DeliveryStatusEnum::getMsg));
	}

	/**
	 * 获取状态字符串
	 * 
	 * @param code
	 * @return
	 * <AUTHOR>
	 * @date 2021年11月24日
	 */
	public static String getValue(Integer status) {
		return lookupMap.get(status);
	}
}
