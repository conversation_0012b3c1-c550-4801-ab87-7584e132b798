package com.gl.commons.enums;

public enum GenderTypeEnum {
    UNKNOWN(0, "未知"),
    MAN(1, "男"),
    WOMAN(2, "女");

    public final int value;
    public final String name;

    GenderTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getName(int value) {
        for (GenderTypeEnum genderTypeEnum : GenderTypeEnum.values()) {
            if (genderTypeEnum.getValue() == value) {
                return genderTypeEnum.name;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }


}
