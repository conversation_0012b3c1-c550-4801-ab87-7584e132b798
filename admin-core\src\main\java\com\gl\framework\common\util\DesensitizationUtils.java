package com.gl.framework.common.util;

/**
 * 数据脱敏工具类
 *
 *
 * <AUTHOR>
 */
public class DesensitizationUtils {
	/**
	 * [手机号码] 显示前三后四，<例子:138******1234>
	 *
	 * @param mobile 手机号码
	 * @return
	 */
	public static String mobileEncrypt(String mobile) {
		if (StringUtils.isEmpty(mobile) || (mobile.length() != 11)) {
			return mobile;
		}
		return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
	}

	/**
	 * [身份证号] 显示前三后四，<例子:451002*********647>
	 *
	 * @param idNumber 身份证号
	 * @return
	 */
	public static String idNumberEncrypt(String idNumber) {
		if (!StringUtils.isEmpty(idNumber)) {
			if (idNumber.length() == 15) {
				idNumber = idNumber.replaceAll("(\\w{6})\\w*(\\w{3})", "$1******$2");
			}
			if (idNumber.length() == 18) {
				idNumber = idNumber.replaceAll("(\\w{6})\\w*(\\w{3})", "$1*********$2");
			}
		}
		return idNumber;
	}

	/**
	 * [护照号码] 显示前二后三位，护照一般为8或9位，<例子:12***678>
	 *
	 * @param passportNo
	 * @return
	 */
	public static String passportNoEncrypt(String passportNo) {
		if (StringUtils.isEmpty(passportNo) || (passportNo.length() < 8)) {
			return passportNo;
		}
		return passportNo.substring(0, 2) + new String(new char[passportNo.length() - 5]).replace("\0", "*")
				+ passportNo.substring(passportNo.length() - 3);
	}

	/**
	 * [邮箱] 显示前一@后，<例子:g***@163.com>
	 *
	 * @param email
	 * @return
	 */
	public static String emailEncrypt(String email) {
		if (StringUtils.isEmpty(email)) {
			return email;
		}
		return email.replaceAll("(\\w+)\\w{3}@(\\w+)", "$1***@$2");
	}

	/**
	 * [银行卡号] 显示前六后四，<例子:6222600**********1234>
	 *
	 * @param cardNum
	 * @return
	 */
	public static String bankCardEncrypt(String cardNum) {
		if (StringUtils.isEmpty(cardNum)) {
			return cardNum;
		}
		return StringUtils.left(cardNum, 6).concat(StringUtils.removeStart(
				StringUtils.leftPad(StringUtils.right(cardNum, 4), StringUtils.length(cardNum), "*"), "******"));
	}

	/**
     * [中文姓名] 显示前一，<例子：李**>
     *
     * @param fullName 姓名
     * @return
     */
    public static String chineseNameEncrypt(String fullName) {
        if (StringUtils.isEmpty(fullName)) {
            return fullName;
        }
        String name = StringUtils.left(fullName, 1);
        return StringUtils.rightPad(name, StringUtils.length(fullName), "*");
    }

}
