package com.gl.service.broadcastPlan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Basic;
import javax.persistence.Column;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class BroadcastPlanVo extends UserVo {

    private Long id;
    private String startTime;
    private String endTime;

    private String shopName;
    private String deviceIds;
    private String deviceNames;

    private String titles;

    private String startDate;
    private String endDate;
    private String type;
    private String intervalTime;

    private Long shopId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;


}
