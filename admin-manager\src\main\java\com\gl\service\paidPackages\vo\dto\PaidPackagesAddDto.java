package com.gl.service.paidPackages.vo.dto;

import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaidPackagesAddDto extends UserVo {
    private Long id;
    private String name;
    private Integer dataNum;
    private Integer effectDay;
    private BigDecimal sellingPrice;

    private List<Long> ids;
}
