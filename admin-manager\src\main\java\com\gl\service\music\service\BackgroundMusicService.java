package com.gl.service.music.service;

import cn.hutool.core.collection.CollUtil;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.music.repository.BackgroundMusicTypeRepository;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.music.vo.dto.BackgroundMusicDto;
import com.gl.service.opus.entity.BackgroundMusic;
import com.gl.service.opus.repository.BackgroundMusicRepository;
import com.gl.service.oss.service.OSSService;
import com.gl.util.GetShopRefUtil;
import com.gl.util.VideoUtil;
import com.gl.system.vo.SysUserVo;
import com.gl.util.WavFileParsingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 背景音乐
 *
 * @author: duanjinze
 * @date: 2022/11/11 14:12
 * @version: 1.0
 */
@SuppressWarnings("DuplicatedCode")
@Service
@Slf4j
public class BackgroundMusicService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private OSSService ossService;
    @Autowired
    private BackgroundMusicRepository backgroundMusicRepository;
    @Autowired
    private BackgroundMusicTypeRepository backgroundMusicTypeRepository;
    @Autowired
    private GetShopRefUtil shopRefUtil;


    public Result list(BackgroundMusicDto dto) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {

            if (dto.getShopId() != null) {
                where.append(" and s.id = ? ");
                args.add(dto.getShopId());
            }

            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and (m.name like ?) ");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }
        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            where.append(String.format(" AND s.id in %s", SqlUtils.foreach("(", ")", ",", shopRef)));
        }

        String sql = "SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name " +
                "FROM dub_background_music m\n" +
                "LEFT JOIN dub_background_music_type mt\n" +
                "ON m.type_id = mt.id\n" +
                "LEFT JOIN dub_shop s ON s.id = m.shop_id \n" +
                "WHERE m.del_status != 1";


        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        where.append(" order by m.create_time DESC ");
        where.append(" LIMIT ? OFFSET ? ");
        assert dto != null;
        args.add(dto.getPageSize());
        args.add(dto.getPageNumber() * dto.getPageSize());

        List<BackGroundMusicVo> backGroundMusicVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(BackGroundMusicVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", backGroundMusicVos);
        return result;
    }

    public Result delete(BackgroundMusicDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getBackgroundMusicId() == null) {
            return Result.fail("背景音乐id不能为空");
        }
        backgroundMusicRepository.updateDelStatusById(dto.getBackgroundMusicId());
        return Result.success();
    }

    /**
     * 前端保存之后传完整路径给后端
     *
     * @param vo
     * @return
     */
    public Result add(BackGroundMusicVo vo) {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();

        if (vo.getTypeId() == null) {
            return Result.fail("背景音乐分类id不能为空");
        }
        if (StringUtils.isBlank(vo.getName())) {
            return Result.fail("背景音乐名称不能为空");
        }
        if (StringUtils.isBlank(vo.getMusicUrl())) {
            return Result.fail("背景音乐文件不能为空");
        }

        // 下载文件
        File file = ossService.getObjectFile(null, vo.getMusicUrl());
        try {
            RandomAccessFile rdf = new RandomAccessFile(file, "r");
            // 验证声道数 1 单声道 2 双声道
            int sdao = WavFileParsingUtils.toShort(WavFileParsingUtils.read(rdf, 22, 2));
            if (sdao != 1) {
                return Result.fail("必须单声道WAV格式, 当前" + sdao);
            }
            // 验证采样率、音频采样级别 8000 = 8KHz
            int cyl = WavFileParsingUtils.toInt(WavFileParsingUtils.read(rdf, 24, 4));
            if (cyl < 15000 || cyl > 17000) {
                return Result.fail("采样率必须16 kHz, 当前" + cyl);
            }
            // 验证采样位数
            int unitNum = WavFileParsingUtils.toShort(WavFileParsingUtils.read(rdf, 34, 2));
            if (unitNum != 16) {
                return Result.fail("位深度要求16位, 当前" + unitNum);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.fail("背景音乐文件分析失败");
        }

        long musicTime = 0L;
        try {
            //根据文件路径得到时长
            musicTime = VideoUtil.getDuration(file.getPath());
        } catch (IOException e) {
            e.printStackTrace();
        }

        BackgroundMusic backgroundMusic = new BackgroundMusic();
        backgroundMusic.setTypeId(vo.getTypeId());
        backgroundMusic.setName(vo.getName());
        backgroundMusic.setMusicUrl(vo.getMusicUrl());
        backgroundMusic.setMusicTime(Long.valueOf(musicTime).intValue());
        backgroundMusic.setDelStatus(0);
        backgroundMusic.setCreateId(user.getId());
        backgroundMusic.setCreateTime(new Date());
        backgroundMusic.setShopId(vo.getShopId());
        backgroundMusicRepository.save(backgroundMusic);
        return Result.success();
    }

    public Result findBackgroundMusicType() {
        return Result.success(backgroundMusicTypeRepository.findAll());
    }
}
