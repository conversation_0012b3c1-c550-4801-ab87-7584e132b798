package com.gl.service.message.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.message.service.BaseServiceMessageService;
import com.gl.service.message.vo.dto.BaseServiceMessageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 用户留言
 *
 * @author: duanjinze
 * @date: 2022/11/11 17:19
 * @version: 1.0
 */
@Controller
@RequestMapping("/message")
public class BaseServiceMessageController {

    @Autowired
    private BaseServiceMessageService baseServiceMessageService;

    /**
     * 留言列表
     *
     * @param dto
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('message:message:list')")
    public Result list(BaseServiceMessageDto dto) {
        return baseServiceMessageService.list(dto);
    }

    /**
     * 删除
     *
     * @param dto
     * @return
     */
    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('message:message:delete')")
    public Result delete(@RequestBody BaseServiceMessageDto dto) {
        return baseServiceMessageService.delete(dto);
    }
}
