package com.gl.framework.config.http;

import org.springframework.http.HttpStatus;

import java.io.IOException;

public class RestCustomException extends IOException {

	private static final long serialVersionUID = 1L;

	private final HttpStatus statusCode;

	private final String body;

	public RestCustomException(HttpStatus statusCode, String body, String msg) {
		super(msg);
		this.statusCode = statusCode;
		this.body = body;
	}

	public HttpStatus getStatusCode() {
		return statusCode;
	}

	public String getBody() {
		return body;
	}

}
