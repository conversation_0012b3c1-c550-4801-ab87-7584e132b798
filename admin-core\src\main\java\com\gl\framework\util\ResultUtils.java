package com.gl.framework.util;

import com.gl.framework.web.response.Result;
import com.gl.framework.web.response.ResultCode;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;

import java.util.List;

public enum ResultUtils {

    INSTANCE;


    public static <T> Result getFailResult(BindingResult bindingResult, Result result) {

        result.setCode(ResultCode.FAIL.getCode());
        List<ObjectError> errorList = bindingResult.getAllErrors();
        StringBuilder messages = new StringBuilder();
        errorList.forEach(error -> {
            messages.append(error.getDefaultMessage()).append(";");
        });
        result.setMessage(messages.toString());
        return result;
    }
}
