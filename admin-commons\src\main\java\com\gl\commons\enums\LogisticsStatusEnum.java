package com.gl.commons.enums;

/**
 * 物流状态
 * <AUTHOR>
 * @createDate: 2022年2月15日 下午4:20:49
 * @version: 1.0
 *
 */
public enum LogisticsStatusEnum {
	/**
	 * 待揽收-0
	 */
	STAY_LAN_SHOU(0),
	/**
	 * 已揽收-1
	 */
	HAS_LAN_SHOU(1),
	/**
	 * 运输中-2
	 */
	TRANSIT(2),
	/**
	 * 派送中-3
	 */
	OUT_DLIVERY(3),
	/**
	 * 异常件-4
	 */
	ABNORMAL_THING(4),
	/**
	 * 退回件-5
	 */
	RETURN_SHIPMENT(5),
	/**
	 * 退回签收-6
	 */
	BACK_SIGN(6),
	/**
	 * 转寄件-7
	 */
	FORWARD(7),
	/**
	 * 作废件-8
	 */
	INVALID_PIECES(8),
	/**
	 * 已签收-9
	 */
	HAVE_BEEN_SIGNED(9),
	/**
	 * 已取消-10
	 */
	CANCELED(10),
	/**
	 * 下单中-11
	 */
	IN_ORDER(11),
	/**
	 * 下单失败-12
	 */
	ORDER_FAILED(12);

	private int status;

	LogisticsStatusEnum(int status) {
		this.status = status;
	}

	public int getStatus() {
		return status;
	}

}
