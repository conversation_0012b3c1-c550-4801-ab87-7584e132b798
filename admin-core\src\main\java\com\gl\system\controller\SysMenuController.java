package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.interceptor.annotation.RepeatSubmit;
import com.gl.framework.security.LoginUser;
import com.gl.system.service.SysMenuService;
import com.gl.system.vo.SysMenuVo;
import com.gl.system.vo.SysUserVo;
import com.gl.framework.web.domain.TreeSelect;
import com.gl.framework.web.response.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 菜单管理控制器
 */
@RestController
@RequestMapping("/system/menu")
public class SysMenuController {

    @Autowired
    private SysMenuService menuService;

    /**
     * 获取菜单列表
     *
     * @param vo
     * @return
     */
    @GetMapping("/list")
    //@PreAuthorize("@ps.hasPermi('system:menu:list')")
    public List<SysMenuVo> list(SysMenuVo vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Long userId = user.getId();
        return menuService.selectMenuList(vo, userId);
    }

    /**
     * 获取菜单详细信息
     *
     * @param menuId
     * @return
     */
    @GetMapping("/{menuId}")
    @PreAuthorize("@ps.hasPermi('system:menu:query')")
    public SysMenuVo getInfo(@PathVariable Long menuId) {
        return menuService.selectMenuById(menuId);
    }

    /**
     * 获取菜单下拉树列表
     *
     * @param vo
     * @return
     */
    @GetMapping("/treeselect")
    public List<TreeSelect> treeselect(SysMenuVo vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Long userId = user.getId();
        List<SysMenuVo> menus = menuService.roleSelectMenuList(vo, userId);
        return menuService.buildMenuTreeSelect(menus);
    }

    /**
     * 获取对应角色的菜单列表树
     *
     * @param roleId
     * @return
     */
    @GetMapping(value = "/role_menu_treeselect/{roleId}")
    public Map<String, Object> roleMenuTreeselect(@PathVariable("roleId") Long roleId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Long userId = user.getId();
        List<SysMenuVo> menus = menuService.selectMenuList(userId);

        Map<String, Object> map = new HashMap<String, Object>();
        Result result = Result.success();
        map.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        map.put("menus", menuService.buildMenuTreeSelect(menus));
        return map;
    }

    /**
     * 新增菜单
     *
     * @param vo
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @PreAuthorize("@ps.hasPermi('system:menu:add')")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT, businessTypeName = "新增")
    public Result add(@Validated @RequestBody SysMenuVo vo) {
        if (Constants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(vo))) {
            return Result.fail("新增菜单'" + vo.getName() + "'失败，菜单名称已存在");
        }

        menuService.saveMenu(vo);
        return Result.success();
    }

    /**
     * 修改菜单
     *
     * @param vo
     * @return
     */
    @PutMapping
    @PreAuthorize("@ps.hasPermi('system:menu:edit')")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE, businessTypeName = "修改")
    public Result edit(@Validated @RequestBody SysMenuVo vo) {
        if (Constants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(vo))) {
            return Result.fail("修改菜单'" + vo.getName() + "'失败，菜单名称已存在");
        }

        menuService.updateMenu(vo);
        return Result.success();
    }

    /**
     * 删除菜单
     *
     * @param menuId
     * @return
     */
    @DeleteMapping("/{menuId}")
    @PreAuthorize("@ps.hasPermi('system:menu:remove')")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE, businessTypeName = "删除")
    public Result delete(@PathVariable Long menuId) {
        if (menuService.hasChildByMenuId(menuId)) {
            return Result.fail("存在子菜单,不允许删除");
        }
//		if (menuService.checkMenuExistRole(menuId)) {
//			return ResultEntity.fail(null, "菜单已分配,不允许删除");
//		}

        menuService.deleteMenuById(menuId);
        return Result.success();
    }
}
