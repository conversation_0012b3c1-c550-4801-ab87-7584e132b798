package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysPositionDept is a Querydsl query type for SysPositionDept
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysPositionDept extends EntityPathBase<SysPositionDept> {

    private static final long serialVersionUID = 1216363635L;

    public static final QSysPositionDept sysPositionDept = new QSysPositionDept("sysPositionDept");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    public final NumberPath<Long> deptId = createNumber("deptId", Long.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Long> positionId = createNumber("positionId", Long.class);

    public QSysPositionDept(String variable) {
        super(SysPositionDept.class, forVariable(variable));
    }

    public QSysPositionDept(Path<? extends SysPositionDept> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysPositionDept(PathMetadata metadata) {
        super(SysPositionDept.class, metadata);
    }

}

