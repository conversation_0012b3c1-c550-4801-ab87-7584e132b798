package com.gl.wechat.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QWechatUser is a Querydsl query type for WechatUser
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QWechatUser extends EntityPathBase<WechatUser> {

    private static final long serialVersionUID = 393990224L;

    public static final QWechatUser wechatUser = new QWechatUser("wechatUser");

    public final StringPath area = createString("area");

    public final DateTimePath<java.util.Date> authTime = createDateTime("authTime", java.util.Date.class);

    public final StringPath avatar = createString("avatar");

    public final NumberPath<Integer> gender = createNumber("gender", Integer.class);

    public final DateTimePath<java.util.Date> h5AuthTime = createDateTime("h5AuthTime", java.util.Date.class);

    public final StringPath h5Openid = createString("h5Openid");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath nickname = createString("nickname");

    public final StringPath openid = createString("openid");

    public final StringPath phone = createString("phone");

    public final StringPath unionid = createString("unionid");

    public QWechatUser(String variable) {
        super(WechatUser.class, forVariable(variable));
    }

    public QWechatUser(Path<? extends WechatUser> path) {
        super(path.getType(), path.getMetadata());
    }

    public QWechatUser(PathMetadata metadata) {
        super(WechatUser.class, metadata);
    }

}

