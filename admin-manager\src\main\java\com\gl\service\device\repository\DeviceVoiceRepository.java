package com.gl.service.device.repository;

import com.gl.service.opus.entity.DeviceVoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;


public interface DeviceVoiceRepository extends JpaRepository<DeviceVoice,Long>, JpaSpecificationExecutor<DeviceVoice> {
    @Transactional
    @Modifying
    @Query(value = "update dub_device_voice set del_status = 1 where device_id = ?1 ",nativeQuery = true)
    Integer updateDelStatusByDeviceId(Long deviceId);

    @Transactional
    @Modifying
    @Query(value = "update dub_device_voice set del_status = 1 where id = ?1 ",nativeQuery = true)
    Integer updateDelStatusById(Long id);

    @Transactional
    @Modifying
    @Query(value = "update dub_device_voice set sortby = ?2 where id = ?1 ",nativeQuery = true)
    Integer updateSortBy(Long id,Integer sortby);


/*    @Modifying
    @Query(value = "select *From dub_device_voice where del_status = 0 and device_id = ?1 ",nativeQuery = true)
    List<DeviceVoice> getByDeviceId(Long id);*/




}
