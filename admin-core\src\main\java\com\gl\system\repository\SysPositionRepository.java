package com.gl.system.repository;

import com.gl.system.entity.SysPosition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface SysPositionRepository extends JpaRepository<SysPosition, Long>, JpaSpecificationExecutor<SysPosition> {


    Long countByName(String name);

    Long countByCode(String code);

    @Query(value = "select count(1) from sys_position s where s.id <> ?1 and s.code = ?2", nativeQuery = true)
    Long countByIdAndCode(Long id, String code);
}
