package com.gl.service.opus.repository;

import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.opus.entity.BackgroundMusic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface BackgroundMusicRepository extends JpaRepository<BackgroundMusic,Long>, JpaSpecificationExecutor<BackgroundMusic> {
    @Transactional
    @Modifying
    @Query(value = "update dub_background_music set del_status = 1 where id = ?1 ",nativeQuery = true)
    Integer updateDelStatusById(Long id);

    @Query(value = "select new com.gl.service.music.vo.BackGroundMusicVo(m.id,m.name,m.musicUrl,m.musicTime) " +
            "from BackgroundMusic m right join UserFollowBgm uf on m.id = uf.bgmId where uf.userId=:userId")
    List<BackGroundMusicVo> findFollowBgm(Long userId);
}
