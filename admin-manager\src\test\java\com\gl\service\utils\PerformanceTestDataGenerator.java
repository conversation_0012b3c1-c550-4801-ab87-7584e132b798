package com.gl.service.utils;

import com.gl.service.opus.entity.*;
import com.gl.service.shop.entity.Shop;
import com.gl.wechat.entity.WechatUser;
import com.gl.system.entity.SysUser;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.IntStream;

/**
 * 性能测试数据生成工具类
 * 用于生成大量测试数据以进行数据库性能测试
 * 
 * <AUTHOR>
 */
public class PerformanceTestDataGenerator {

    private static final String[] CHINESE_SURNAMES = {
        "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
        "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗"
    };

    private static final String[] CHINESE_NAMES = {
        "伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
        "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞"
    };

    private static final String[] DEVICE_PREFIXES = {
        "智能音箱", "蓝牙音响", "无线耳机", "录音设备", "播放器",
        "扩音器", "麦克风", "音频设备", "声音设备", "语音设备"
    };

    private static final String[] CONTENT_TEMPLATES = {
        "欢迎光临，请问有什么可以帮助您的吗？",
        "感谢您的到来，祝您购物愉快！",
        "本店新品上市，欢迎选购！",
        "优质服务，诚信经营，欢迎您的光临！",
        "今日特价商品，机会难得，不要错过！",
        "客户至上，服务第一，期待为您服务！",
        "品质保证，价格优惠，欢迎咨询！",
        "新老客户一律优惠，欢迎选购！",
        "专业服务，贴心关怀，感谢您的信任！",
        "诚信经营，童叟无欺，欢迎监督！"
    };

    /**
     * 生成大量作品数据
     * 
     * @param count 生成数量
     * @return 作品列表
     */
    public static List<VoiceWork> generateVoiceWorks(int count) {
        List<VoiceWork> works = new ArrayList<>(count);
        
        for (int i = 0; i < count; i++) {
            VoiceWork work = new VoiceWork();
            work.setId((long) (i + 1));
            work.setTitle("性能测试作品_" + i + "_" + System.currentTimeMillis());
            work.setContent(getRandomContent());
            work.setVoiceId(getRandomLong(1, 100));
            work.setAnchorId(getRandomLong(1, 50));
            work.setVoiceTime(getRandomInt(10, 300));
            work.setSpeed(getRandomInt(80, 120));
            work.setVolume(getRandomInt(50, 100));
            work.setPitch(getRandomInt(-10, 10));
            work.setUserId(getRandomLong(1, 1000));
            work.setShopId(getRandomLong(1, 100));
            work.setDelStatus(0);
            work.setCreateTime(new Date());
            works.add(work);
        }
        
        return works;
    }

    /**
     * 生成大量语音包数据
     * 
     * @param count 生成数量
     * @return 语音包列表
     */
    public static List<VoicePacket> generateVoicePackets(int count) {
        List<VoicePacket> packets = new ArrayList<>(count);
        
        for (int i = 0; i < count; i++) {
            VoicePacket packet = new VoicePacket();
            packet.setId((long) (i + 1));
            packet.setVoiceWorkId(getRandomLong(1, count / 2));
            packet.setName("性能测试语音包_" + i);
            packet.setVoiceTime(getRandomInt(10, 300));
            packet.setFileUrl("https://test-oss.com/voice/perf_test_" + i + ".mp3");
            packet.setShopId(getRandomLong(1, 100));
            packets.add(packet);
        }
        
        return packets;
    }

    /**
     * 生成大量设备数据
     * 
     * @param count 生成数量
     * @return 设备列表
     */
    public static List<Device> generateDevices(int count) {
        List<Device> devices = new ArrayList<>(count);
        
        for (int i = 0; i < count; i++) {
            Device device = new Device();
            device.setId((long) (i + 1));
            device.setName(getRandomDeviceName() + "_" + i);
            device.setSn("PERF_TEST_" + String.format("%08d", i) + "_" + System.currentTimeMillis());
            device.setUserId(getRandomLong(1, 1000));
            device.setVolume(getRandomInt(30, 100));
            device.setStatus(getRandomInt(0, 1));
            device.setBindStatus(getRandomInt(0, 1));
            device.setUseStatus(getRandomInt(0, 2));
            device.setShopId(getRandomLong(1, 100));
            device.setDelStatus(0);
            device.setCreateTime(new Date());
            device.setUpdateStatusTime(new Date());
            devices.add(device);
        }
        
        return devices;
    }

    /**
     * 生成大量设备语音关联数据
     * 
     * @param count 生成数量
     * @return 设备语音关联列表
     */
    public static List<DeviceVoice> generateDeviceVoices(int count) {
        List<DeviceVoice> deviceVoices = new ArrayList<>(count);
        
        for (int i = 0; i < count; i++) {
            DeviceVoice deviceVoice = new DeviceVoice();
            deviceVoice.setId((long) (i + 1));
            deviceVoice.setType(getRandomInt(1, 2));
            deviceVoice.setDeviceId(getRandomLong(1, count / 10));
            deviceVoice.setVoiceWorkId(getRandomLong(1, count / 5));
            deviceVoice.setDeviceVoiceId(getRandomLong(1, count / 3));
            deviceVoice.setTitle("设备语音_" + i);
            deviceVoice.setContent(getRandomContent());
            deviceVoice.setVoiceUrl("https://test-oss.com/device_voice/perf_test_" + i + ".mp3");
            deviceVoice.setVoiceId(getRandomLong(1, 100));
            deviceVoice.setVoiceTime(getRandomInt(5, 180));
            deviceVoice.setSpeed(getRandomInt(80, 120));
            deviceVoice.setVolume(getRandomInt(50, 100));
            deviceVoice.setPitch(getRandomInt(-10, 10));
            deviceVoice.setBackgroundMusicVolume(getRandomInt(20, 80));
            deviceVoice.setBackgroundMusicId(getRandomLong(1, 50));
            deviceVoice.setSampleRate(getRandomInt(16000, 48000));
            deviceVoice.setDelStatus(0);
            deviceVoices.add(deviceVoice);
        }
        
        return deviceVoices;
    }

    /**
     * 生成大量微信用户数据
     * 
     * @param count 生成数量
     * @return 微信用户列表
     */
    public static List<WechatUser> generateWechatUsers(int count) {
        List<WechatUser> users = new ArrayList<>(count);
        
        for (int i = 0; i < count; i++) {
            WechatUser user = new WechatUser();
            user.setId((long) (i + 1));
            user.setOpenid("perf_test_openid_" + i + "_" + System.currentTimeMillis());
            user.setNickname(getRandomChineseName());
            user.setAvatar("https://test-avatar.com/avatar_" + i + ".jpg");
            user.setGender(getRandomInt(0, 2));
            user.setArea("测试省份_" + (i % 34));
            user.setH5AuthTime(new Date());
            user.setAuthTime(new Date());
            users.add(user);
        }
        
        return users;
    }

    /**
     * 生成大量关注主播数据
     * 
     * @param count 生成数量
     * @return 关注主播列表
     */
    public static List<FollowAnchor> generateFollowAnchors(int count) {
        List<FollowAnchor> follows = new ArrayList<>(count);
        
        for (int i = 0; i < count; i++) {
            FollowAnchor follow = new FollowAnchor();
            follow.setId((long) (i + 1));
            follow.setUserId(getRandomLong(1, count / 10));
            follow.setAnchorId(getRandomLong(1, 100));
            follows.add(follow);
        }
        
        return follows;
    }

    /**
     * 生成大量用户文本模板数据
     * 
     * @param count 生成数量
     * @return 用户文本模板列表
     */
    public static List<UserTextTemplate> generateUserTextTemplates(int count) {
        List<UserTextTemplate> templates = new ArrayList<>(count);
        
        for (int i = 0; i < count; i++) {
            UserTextTemplate template = new UserTextTemplate();
            template.setId((long) (i + 1));
            template.setUserId(getRandomLong(1, count / 20));
            template.setTextContent("用户模板内容_" + i + ": " + getRandomContent());
            templates.add(template);
        }
        
        return templates;
    }

    // 辅助方法
    private static String getRandomChineseName() {
        String surname = CHINESE_SURNAMES[ThreadLocalRandom.current().nextInt(CHINESE_SURNAMES.length)];
        String name = CHINESE_NAMES[ThreadLocalRandom.current().nextInt(CHINESE_NAMES.length)];
        return surname + name;
    }

    private static String getRandomDeviceName() {
        return DEVICE_PREFIXES[ThreadLocalRandom.current().nextInt(DEVICE_PREFIXES.length)];
    }

    private static String getRandomContent() {
        return CONTENT_TEMPLATES[ThreadLocalRandom.current().nextInt(CONTENT_TEMPLATES.length)];
    }

    private static int getRandomInt(int min, int max) {
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }

    private static long getRandomLong(long min, long max) {
        return ThreadLocalRandom.current().nextLong(min, max + 1);
    }

    /**
     * 批量生成测试数据的性能统计信息
     */
    public static class PerformanceStats {
        private long totalRecords;
        private long generationTimeMs;
        private double recordsPerSecond;

        public PerformanceStats(long totalRecords, long generationTimeMs) {
            this.totalRecords = totalRecords;
            this.generationTimeMs = generationTimeMs;
            this.recordsPerSecond = totalRecords * 1000.0 / generationTimeMs;
        }

        public void printStats(String dataType) {
            System.out.println("=== " + dataType + " 数据生成性能统计 ===");
            System.out.println("总记录数: " + totalRecords);
            System.out.println("生成耗时: " + generationTimeMs + "ms");
            System.out.println("生成速度: " + String.format("%.2f", recordsPerSecond) + " 条/秒");
            System.out.println("平均每条记录耗时: " + String.format("%.4f", (double) generationTimeMs / totalRecords) + "ms");
            System.out.println("========================================");
        }
    }
}
