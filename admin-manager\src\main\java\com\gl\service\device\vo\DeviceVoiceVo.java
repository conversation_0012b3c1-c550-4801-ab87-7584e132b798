package com.gl.service.device.vo;

import com.gl.service.opus.entity.DeviceVoice;
import lombok.Data;

import java.util.List;

/**
 * @author: duanjinze
 * @date: 2022/11/11 9:29
 * @version: 1.0
 */
@Data
public class DeviceVoiceVo {
    /**
     * 设备表id
     */
    private Long deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备id（sn）
     */
    private String sn;

    /**
     * 在线状态 0离线 1在线
     */
    private Integer status;

    /**
     * 设备与语音包表集合
     */
    private List<DeviceVoice> deviceVoiceList;
}
