package com.gl.service.device.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * @author: duanjinze
 * @date: 2022/11/11 9:57
 * @version: 1.0
 */
@Data
public class ExcelDevice {

    @ExcelProperty(value = "设备名称",index = 0)
    @ColumnWidth(20)
    private String name;

    @ExcelProperty(value = "设备id",index = 1)
    @ColumnWidth(20)
    private String sn;

    @ExcelProperty(value = "所属用户",index = 2)
    @ColumnWidth(20)
    private String nickname;

    @ExcelProperty(value = "用户手机",index = 3)
    @ColumnWidth(20)
    private String phone;

    @ExcelProperty(value = "作品数",index = 4)
    @ColumnWidth(14)
    private String workCount;

    /**
     * 在线状态 0离线 1在线
     */
    @ExcelProperty(value = "设备状态",index = 5)
    @ColumnWidth(14)
    private String status;

    /**
     * 绑定状态 0未绑定 1已绑定
     * （当做解绑与绑定操作时 0解绑 1绑定）
     */
    @ExcelProperty(value = "绑定状态",index = 6)
    @ColumnWidth(14)
    private String bindStatus;

    /**
     * 使用状态 0停用 1使用（正常）
     */
    @ExcelProperty(value = "使用状态",index = 7)
    @ColumnWidth(14)
    private String useStatus;

    @ExcelProperty(value = "创建时间",index = 8)
    @ColumnWidth(20)
    private String createTime;

}
