package com.gl.framework.exception.handler;

import com.gl.framework.common.util.StringUtils;
import com.gl.framework.config.http.RestCustomException;
import com.gl.framework.exception.BaseException;
import com.gl.framework.exception.CustomException;
import com.gl.framework.exception.user.CaptchaException;
import com.gl.framework.exception.user.UserPasswordNotMatchException;
import com.gl.framework.web.response.Result;
import com.gl.framework.web.response.ResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.security.auth.login.AccountExpiredException;

/**
 * 全局异常处理器
 */

@RestController
@ControllerAdvice
public class GlobalExceptionHandler {
	private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

	/**
	 * 基础异常
	 */
	@ExceptionHandler(BaseException.class)
	public Result baseException(BaseException e) {
		return Result.fail(e.getMessage());
	}

	/**
	 * 业务异常
	 */
	@ExceptionHandler(CustomException.class)
	public Result businessException(CustomException e) {
		if (StringUtils.isNull(e.getCode())) {
			return Result.fail(e.getMessage());
		}
		return Result.fail(e.getCode(), e.getMessage());
	}

	/**
	 * 资源不存在异常
	 */
	@ExceptionHandler(NoHandlerFoundException.class)
	public Result handlerNoFoundException(Exception e) {
		log.error(e.getMessage(), e);
		return Result.fail(ResultCode.NOT_FOUND.getCode(), e.getMessage());
	}

	/**
	 * 访问权限异常
	 */
	@ExceptionHandler(AccessDeniedException.class)
	public Result handleAuthorizationException(AccessDeniedException e) {
		log.error(e.getMessage());
		return Result.fail(ResultCode.FORBIDDEN.getCode(), e.getMessage());
	}

	/**
	 * 账号过期异常
	 */
	@ExceptionHandler(AccountExpiredException.class)
	public Result handleAccountExpiredException(AccountExpiredException e) {
		log.error(e.getMessage(), e);
		return Result.fail(e.getMessage());
	}

	/**
	 * 登录账号不正确异常
	 */
	@ExceptionHandler(UsernameNotFoundException.class)
	public Result handleUsernameNotFoundException(UsernameNotFoundException e) {
		log.error(e.getMessage(), e);
		return Result.fail(e.getMessage());
	}

	/**
	 * 用户名或者密码不正确
	 */
	@ExceptionHandler(UserPasswordNotMatchException.class)
	public Result handleBadCredentialsException(UserPasswordNotMatchException e) {
		log.error(e.getMessage(), e);
		return Result.fail(40009, e.getMessage());
	}

	/**
	 * 自定义验证异常
	 */
	@ExceptionHandler(BindException.class)
	public Result validatedBindException(BindException e) {
		log.error(e.getMessage(), e);
		String message = e.getAllErrors().get(0).getDefaultMessage();
		return Result.fail(message);
	}

	/**
	 * 验证码异常
	 */
	@ExceptionHandler(CaptchaException.class)
	public Result validExceptionHandler(CaptchaException e) {
		log.error(e.getMessage(), e);
		String message = e.getMessage();
		return Result.fail(message);
	}

	/**
	 * 自定义验证异常
	 */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	public Result validExceptionHandler(MethodArgumentNotValidException e) {
		log.error(e.getMessage(), e);
		String message = e.getBindingResult().getFieldError().getDefaultMessage();
		return Result.fail(message);
	}

	/**
	 * RestTemplate请求异常
	 */
	@ExceptionHandler(RestCustomException.class)
	public Result restExceptionHandler(RestCustomException e) {
		log.error(e.getMessage(), e);
		return Result.fail();
	}

	@ExceptionHandler(Exception.class)
	public Result handleException(Exception e) {
		log.error(e.getMessage(), e);
		return Result.fail(e.getMessage());
	}
}
