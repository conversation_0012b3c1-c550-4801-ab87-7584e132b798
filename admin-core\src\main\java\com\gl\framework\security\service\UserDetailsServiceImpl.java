package com.gl.framework.security.service;

import com.gl.framework.common.enums.UserStatusEnum;
import com.gl.framework.constant.Constants;
import com.gl.framework.exception.BaseException;
import com.gl.framework.exception.CustomException;
import com.gl.framework.manager.AsyncManager;
import com.gl.framework.manager.factory.AsyncFactory;
import com.gl.framework.security.LoginUser;
import com.gl.system.service.SysRoleService;
import com.gl.system.service.SysUserService;
import com.gl.system.vo.SysUserVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 用户验证处理
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
	private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

	@Autowired
	private SysUserService userService;

	@Autowired
	private SysRoleService roleService;

	@Autowired
	private SysPermissionService permissionService;

	@Override
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		SysUserVo user = userService.selectUserByLoginName(username);
		check(user, username);
		return createLoginUser(user);
	}

	/**
	 * 构建登录用户，存储更多用户信息
	 *
	 * @param user
	 * @return
	 */
	private UserDetails createLoginUser(SysUserVo user) {
		Long userId = user.getId();
		// 用户角色ID列表
		List<Long> roleIds = roleService.selectRoleListByUserId(userId);
		// 用户角色权限字符串组
		Set<String> roleKeys = roleService.selectRolePermissionByUserId(userId);
		// 数据权限列表(根据系统参数中的配置取岗位或者角色授权的用户部门信息)
		List<Long> dataScopes = userService.getUserDataScopes(user);
		if(dataScopes == null || dataScopes.isEmpty()){
			dataScopes = new ArrayList<>();
		}
		// 菜单权限标识列表
		Set<String> menuPermissionSet = permissionService.getMenuPermission(userId);
		return new LoginUser(user, roleIds, roleKeys, dataScopes, menuPermissionSet);
	}

	private void check(SysUserVo user, String loginName) throws UsernameNotFoundException{
		if (Objects.equals(UserStatusEnum.DISABLE.code(), user.getStatus())) {
			log.info("登录用户：{} 已被停用.", loginName);
			AsyncManager.me().execute(AsyncFactory.recordLoginLog(loginName, Constants.LOGIN_FAIL, "用户被禁用"));
			throw new CustomException("登录用户已被停用");
		}else if (Objects.equals(user.getDeleted(), 1)) {
			log.info("登录用户：{} 已被删除.", loginName);
			AsyncManager.me().execute(AsyncFactory.recordLoginLog(loginName, Constants.LOGIN_FAIL, "您的账号：" + loginName + " 已被删除"));
			throw new BaseException("对不起，您的账号：" + loginName + " 已被删除");
		}
	}
}
