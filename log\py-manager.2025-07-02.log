09:23:29.158 [34mIN<PERSON><PERSON> [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
09:23:29.240 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 17152 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
09:23:29.242 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
09:23:34.881 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
09:23:34.882 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
09:23:36.755 [34mIN<PERSON><PERSON> [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1858 ms. Found 54 JPA repository interfaces.
09:23:38.773 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
09:23:38.798 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
09:23:40.088 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@1a7cb3a4' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:23:40.104 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:23:40.129 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:23:40.144 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:23:40.147 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:23:41.509 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
09:23:41.534 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
09:23:41.536 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
09:23:41.536 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
09:23:42.064 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
09:23:42.065 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 12716 ms
09:23:45.004 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
09:23:45.305 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
09:23:47.618 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
09:23:48.093 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
09:23:48.212 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
09:23:48.490 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
09:23:49.240 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
09:23:50.788 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
09:23:50.799 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
09:23:52.592 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
09:23:52.593 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
09:23:52.596 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
09:23:56.479 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
09:23:57.454 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
09:23:57.587 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@48c8c722, org.springframework.security.web.context.SecurityContextPersistenceFilter@4bbc9862, org.springframework.security.web.header.HeaderWriterFilter@5b83545d, org.springframework.security.web.authentication.logout.LogoutFilter@22938166, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@b75b890, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@664f1c53, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@49dce561, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@12f826c, org.springframework.security.web.session.SessionManagementFilter@72fb922c, org.springframework.security.web.access.ExceptionTranslationFilter@1989a143, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6619692e]
09:23:57.896 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
09:23:57.896 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
09:23:57.896 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
09:23:57.896 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
09:23:57.947 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
09:23:58.066 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 32.179 seconds (JVM running for 51.571)
09:23:58.075 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

09:23:58.499 [34mINFO [0;39m [RMI TCP Connection(22)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:23:58.499 [34mINFO [0;39m [RMI TCP Connection(22)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
09:23:58.503 [34mINFO [0;39m [RMI TCP Connection(22)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
09:24:09.229 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
09:24:09.229 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
09:24:09.549 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
09:27:15.634 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:27:15.691 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:27:15.712 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:27:15.713 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id) t]
09:27:15.797 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:27:15.798 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
09:27:17.887 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询店铺详情，id:22
09:27:17.892 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:27:17.893 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT shop.id AS id, shop.shop_name AS shopName, u.id AS userId, u.nickname AS userName, u.phone AS userPhone FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id AND ref.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref.user_id WHERE shop.id = ?]
09:27:17.899 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
09:31:07.262 [1;31mERROR[0;39m [MQTT Ping: admin-service-clientId-Sub] o.e.p.c.mqttv3.internal.ClientState - admin-service-clientId-Sub: Timed out as no write activity, keepAlive=20,000,000,000 lastOutboundActivity=4,626,621,596,000 lastInboundActivity=4,626,654,222,200 time=4,761,144,149,900 lastPing=4,626,621,600,900
09:31:07.268 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId] c.gl.config.mqtt.MqttClientCallBack - admin-service-clientIdclient 与服务器断开连接！！已断开连接
09:31:07.269 [1;31mERROR[0;39m [MQTT Rec: admin-service-clientId-Sub] c.g.c.mqtt.MqttSubscriptCallBack - admin-service-clientId-Sub与服务器断开连接！！已断开连接
09:44:33.905 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
09:44:33.906 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
09:44:33.906 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
09:44:33.946 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
09:44:34.063 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
09:44:34.077 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
09:44:34.081 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
09:44:43.769 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
09:44:43.790 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 18596 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
09:44:43.790 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
09:44:45.723 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
09:44:45.724 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
09:44:46.096 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 364 ms. Found 54 JPA repository interfaces.
09:44:46.417 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
09:44:46.428 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
09:44:46.655 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@237add' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:44:46.680 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:44:46.706 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:44:46.727 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:44:46.730 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:44:47.134 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
09:44:47.143 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
09:44:47.144 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
09:44:47.144 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
09:44:47.279 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
09:44:47.279 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3449 ms
09:44:47.635 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
09:44:47.676 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
09:44:48.162 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
09:44:48.344 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
09:44:48.411 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
09:44:48.585 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
09:44:48.872 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
09:44:49.965 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
09:44:49.975 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
09:44:51.226 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
09:44:51.226 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
09:44:51.229 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
09:44:53.635 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
09:44:54.339 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
09:44:54.464 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@23591a2c, org.springframework.security.web.context.SecurityContextPersistenceFilter@295d8b70, org.springframework.security.web.header.HeaderWriterFilter@42be0eca, org.springframework.security.web.authentication.logout.LogoutFilter@1db077a4, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@1a0f349, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@f5475d7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f991f00, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@431782fe, org.springframework.security.web.session.SessionManagementFilter@2174fda1, org.springframework.security.web.access.ExceptionTranslationFilter@4a907e5a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@49a6b730]
09:44:54.709 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
09:44:54.710 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
09:44:54.710 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
09:44:54.710 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
09:44:54.729 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
09:44:54.838 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 11.811 seconds (JVM running for 13.529)
09:44:54.845 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

09:44:55.364 [34mINFO [0;39m [RMI TCP Connection(6)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:44:55.364 [34mINFO [0;39m [RMI TCP Connection(6)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
09:44:55.368 [34mINFO [0;39m [RMI TCP Connection(6)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
09:45:00.026 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:00.107 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:00.108 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE user_id = ?]
09:45:00.184 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:00.184 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE shop_id = ?]
09:45:00.362 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:00.366 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:00.373 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:00.374 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id) t]
09:45:00.395 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:00.395 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
09:45:04.238 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.shop.service.ShopService - 查询店铺详情，id:22
09:45:04.241 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:04.242 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT shop.id AS id, shop.shop_name AS shopName, u.id AS userId, u.nickname AS userName, u.phone AS userPhone FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id AND ref.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref.user_id WHERE shop.id = ?]
09:45:04.250 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询用户：WechatUser(id=0, openid=1, nickname=123, avatar=null, gender=null, phone=null, area=null, authTime=null, h5Openid=null, h5AuthTime=null, unionid=null, nicknames=null)
09:45:08.027 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:08.032 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:08.032 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE user_id = ?]
09:45:08.036 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:08.037 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT * FROM dub_user_shop_ref WHERE shop_id = ?]
09:45:08.386 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:08.391 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:08.395 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:08.395 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id) t]
09:45:08.397 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:08.397 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
09:45:10.023 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:10.023 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE ref.id IS NOT NULL AND wechat.phone <> ''  AND wechat.phone IS NOT NULL AND ref.shop_id = ?]
09:45:23.542 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询用户列表，sql:SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL 
09:45:23.542 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:23.543 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE  wechat.phone <> ''   AND wechat.phone IS NOT NULL  AND ref.id IS NULL ]
09:45:29.641 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:29.645 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:45:29.652 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:29.653 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id) t]
09:45:29.657 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:29.658 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
09:45:31.302 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:31.302 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE ref.id IS NOT NULL AND wechat.phone <> ''  AND wechat.phone IS NOT NULL AND ref.shop_id = ?]
09:45:36.069 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:45:36.070 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id WHERE ref.id IS NOT NULL AND wechat.phone <> ''  AND wechat.phone IS NOT NULL AND ref.shop_id = ?]
09:46:31.894 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
09:46:31.896 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
09:46:31.898 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
09:46:32.025 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
09:46:32.048 [31mWARN [0;39m [SpringApplicationShutdownHook] o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
09:46:32.358 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
09:46:32.700 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
09:46:32.730 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
09:46:56.059 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
09:46:56.086 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 15912 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
09:46:56.087 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
09:46:58.136 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
09:46:58.136 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
09:46:58.496 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 353 ms. Found 54 JPA repository interfaces.
09:46:58.820 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
09:46:58.829 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
09:46:59.077 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@634ca3e7' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:46:59.086 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:46:59.100 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:46:59.110 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:46:59.112 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:46:59.424 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
09:46:59.433 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
09:46:59.433 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
09:46:59.433 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
09:46:59.562 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
09:46:59.562 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3428 ms
09:46:59.915 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
09:46:59.948 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
09:47:00.420 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
09:47:00.583 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
09:47:00.631 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
09:47:00.739 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
09:47:01.114 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
09:47:02.478 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
09:47:02.500 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
09:47:04.052 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
09:47:04.052 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
09:47:04.059 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
09:47:06.828 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
09:47:07.533 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
09:47:07.653 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b4f7e1c, org.springframework.security.web.context.SecurityContextPersistenceFilter@4dd4a4e, org.springframework.security.web.header.HeaderWriterFilter@188f626a, org.springframework.security.web.authentication.logout.LogoutFilter@731660c1, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@5dfc2a4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54832ad9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2174fda1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6258ea84, org.springframework.security.web.session.SessionManagementFilter@62a30701, org.springframework.security.web.access.ExceptionTranslationFilter@60adea01, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@58d85a00]
09:47:07.878 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
09:47:07.878 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
09:47:07.878 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
09:47:07.878 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
09:47:07.899 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
09:47:08.009 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 12.768 seconds (JVM running for 14.75)
09:47:08.015 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

09:47:08.449 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:47:08.449 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
09:47:08.453 [34mINFO [0;39m [RMI TCP Connection(12)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
09:47:16.398 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:47:16.476 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
09:47:16.505 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:47:16.508 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(*) FROM (SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id) t]
09:47:16.584 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:47:16.586 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT  shop.id,  shop.create_time,  shop.shop_name,  shop.type,  shop.parent_id,  parentShop.shop_name as parentName,  GROUP_CONCAT(itemShop.shop_name, ',') as itemNames,  shop.status,  ref2.user_id,  u.nickname,  COUNT(DISTINCT ref.user_id ) AS userCount,  COUNT(DISTINCT device.id ) AS deviceCount  FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0 WHERE  shop.id in (22) GROUP BY shop.id, ref2.user_id ORDER BY shop.create_time DESC  LIMIT ? OFFSET ? ]
09:47:22.334 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:47:22.334 [39mDEBUG[0;39m [http-nio-8770-exec-3] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id AND ref.shop_id = ?]
09:47:30.427 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:47:30.427 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id AND ref.shop_id = ?]
09:47:34.615 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
09:47:34.616 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id FROM dub_wechat_user wechat LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id AND ref.shop_id = ?]
09:47:41.844 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [SELECT shop.id, shop.shop_name, COUNT(s2.id) AS shop_count FROM dub_shop shop LEFT JOIN dub_shop s2 ON s2.parent_id = shop.id AND s2.`status` = 1 WHERE shop.parent_id = 0 AND shop.`status` = 1 GROUP BY shop.id HAVING shop_count = 0]
