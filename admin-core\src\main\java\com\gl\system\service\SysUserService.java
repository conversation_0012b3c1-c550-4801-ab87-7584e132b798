package com.gl.system.service;

import com.gl.framework.common.enums.StatusEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.framework.security.service.TokenService;
import com.gl.framework.web.domain.PageData;
import com.gl.system.entity.*;
import com.gl.system.repository.*;
import com.gl.system.service.mapper.UserMapper;
import com.gl.system.vo.SysDeptVo;
import com.gl.system.vo.SysUserVo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class SysUserService {
	@Autowired
	private TokenService tokenService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SysUserRepository userRepository;
    @Autowired
    private SysDeptRepository deptRepository;
    @Autowired
    private SysRoleRepository roleRepository;
    @Autowired
    private SysUserRoleRepository userRoleRepository;
    @Autowired
    private SysUserAllotRoleRepository userAllotRoleRepository;
    @Autowired
    private SysUserDeptRepository sysUserDeptRepository;
    @Autowired
    private SysPositionUserRepository positionUserRepository;
    @Autowired
    private SysConfigService configService;
    @Autowired
    JPAQueryFactory queryFactory;
    @Autowired
    private SysUserLeaveLogRepository sysUserLeaveLogRepository;

    private static final String DATA_PERMISSION_TYPE = "data.permission.type";

    /**
     * 根据条件分页查询用户列表
     *
     * @param filter 条件过滤
     * @return 用户信息集合信息
     */
    public PageData<SysUserVo> selectUserList(SysUserVo filter) {
        PageData<SysUserVo> pageData = new PageData<>();

        Integer pageNumber = filter.getPageNumber() - 1;
        //页面条数
        Integer pageSize = filter.getPageSize();
        Pageable pageable = PageRequest.of(pageNumber, pageSize);

        // 数据权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        SysUserVo user = loginUser.getUser();

        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());
        if (!isSuperAdmin && StringUtils.isEmpty(dataScopes)) {
            return pageData;
        }

        StringBuilder sql = new StringBuilder("FROM sys_user u\n" +
                "left join sys_dept d on d.id = u.dept_id \n" +
                "LEFT JOIN \n" +
                "(SELECT u.id,\n" +
                "GROUP_CONCAT(DISTINCT r.role_name ORDER BY r.role_name ASC SEPARATOR ';') as role_name,\n" +
                "GROUP_CONCAT(DISTINCT r.id ORDER BY r.id ASC SEPARATOR ';') as role_id,\n" +
                "GROUP_CONCAT(DISTINCT p.name ORDER BY p.name ASC SEPARATOR ';') as position_name,\n" +
                "GROUP_CONCAT(DISTINCT p.id ORDER BY p.id ASC SEPARATOR ';') as position_id\n" +
                "FROM sys_user u\n" +
                "LEFT JOIN sys_user_role ur ON ur.user_id = u.id \n" +
                "LEFT JOIN sys_role r ON r.id = ur.role_id \n" +
                "LEFT JOIN sys_position_user pu ON pu.user_id = u.id \n" +
                "LEFT JOIN sys_position p ON p.id = pu.position_id \n" +
                "group by u.id) m ON u.id = m.id \n" +
                "WHERE (  u.deleted = 0  or  u.deleted is null ) and u.type = 1 ");

        StringBuilder countSql = new StringBuilder("SELECT count(1) as total \n").append(sql);
        StringBuilder dataSql = new StringBuilder("SELECT u.id, u.dept_id, u.login_name, u.user_name, u.email, u.phone, " +
                "d.dept_name, " +
                "u.gender, u.avatar, u.status, u.create_time," +
                "m.role_id, m.role_name," +
                "m.position_name, m.position_id ").append(sql);


        StringBuilder whereSql = new StringBuilder("");

        List<Long> idList = null;
        if (filter != null) {
            if (StringUtils.isNotBlank(filter.getLoginName())) {
                whereSql.append("and u.login_name like CONCAT('%','" + filter.getLoginName() + "','%') ");
            }
            if (StringUtils.isNotBlank(filter.getUserName())) {
                whereSql.append("and u.user_name like CONCAT('%','" + filter.getUserName() + "','%') ");
            }
            if (StringUtils.isNotBlank(filter.getPhone())) {
                whereSql.append("and u.phone like CONCAT('%','" + filter.getPhone() + "','%') ");
            }
            if (filter.getStatus() != null) {
                whereSql.append("and u.status = " + filter.getStatus() + " ");
            }
            if (StringUtils.isNotBlank(filter.getBeginTime()) && StringUtils.isNotBlank(filter.getEndTime())) {
                whereSql.append("and u.create_time between '" + filter.getBeginTime() + "' and '" + filter.getEndTime() + "' ");
            }
            if (filter.getDeptId() != null) {

                String ancestors = deptRepository.findAncestorsById(filter.getDeptId());
                idList = deptRepository.findByAncestorsLike(ancestors + "," + filter.getDeptId() + "%");

                if (idList != null) {

                    idList.add(filter.getDeptId());

                    List<Long> inIdsList = (List<Long>) CollectionUtils.intersection(dataScopes, idList);
                    inIdsList.add(filter.getDeptId());
                    StringBuilder idStr = new StringBuilder();
                    for (Long deptId : inIdsList) {
                        idStr.append(deptId).append(",");
                    }

                    String idString = idStr.toString().substring(0, idStr.length() - 1);

                    whereSql.append(" and u.dept_id in (" + idString + ") ");
                }
            }
        }

        String couSql = countSql.append(whereSql).toString();
        Map<String, Object> map = jdbcTemplate.queryForMap(couSql);
        Object totalObj = map.get("total");
        if (totalObj == null || Integer.parseInt(totalObj.toString()) <= 0) {
            return pageData;
        }

        String dSql = dataSql.append(whereSql).append(" limit ").append(pageNumber * pageSize).append(", ").append(pageSize).toString();
        log.info(dSql);
        List<SysUserVo> list = jdbcTemplate.query(dSql, new UserMapper());

        Long total = Long.parseLong(totalObj.toString());

        pageData.setTotal(total);
        pageData.setData(list);

        return pageData;
    }


    private List<SysUserVo> convert(QueryResults<SysUser> jpaQuery) {
        List<SysUser> list = jpaQuery.getResults();
        List<SysUserVo> listVo = new ArrayList<SysUserVo>();

        for (SysUser entity : list) {
            BeanCopier beanCopier = BeanCopier.create(SysUser.class, SysUserVo.class, false);
            SysUserVo vo = new SysUserVo();
            beanCopier.copy(entity, vo, null);
            listVo.add(vo);
        }

        return listVo;
    }

    /**
     * 用户数据转换
     *
     * @param entity
     * @return
     */
    private SysUserVo convert(SysUser entity) {
        BeanCopier beanCopier = BeanCopier.create(SysUser.class, SysUserVo.class, false);
        SysUserVo vo = new SysUserVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    /**
     * 部门数据转换
     *
     * @param entity
     * @return
     */
    private SysDeptVo deptConvert(SysDept entity) {
        BeanCopier beanCopier = BeanCopier.create(SysDept.class, SysDeptVo.class, false);
        SysDeptVo vo = new SysDeptVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    /**
     * 通过登录账号查询用户
     *
     * @param loginName 登录账号
     * @return 用户对象信息
     */
    public SysUserVo selectUserByLoginName(String loginName) {
        SysUser sysUser = userRepository.findByLoginName(loginName);
        if (sysUser == null) {
            log.info("登录用户：{} 不存在.", loginName);
            throw new CustomException("用户名或密码错误");
        }
        SysUserVo vo = convert(sysUser);
        log.info("查询用户部门信息1: {}", vo.getDeptId());
        // 查询部门信息
        log.info("查询用户部门信息2: {}", vo.getDeptId());
        Optional<SysDept> optional = deptRepository.findById(vo.getDeptId());
        if (optional.isPresent()) {
            SysDept sysDept = optional.get();
            vo.setDept(deptConvert(sysDept));
        } else {
            log.warn("用户未分配部门");
        }

        return vo;
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUserVo selectUserById(Long userId) {
        Optional<SysUser> optional = userRepository.findById(userId);
        if (!optional.isPresent()) {
            throw new CustomException("用户不存在");
        }
        SysUser sysUser = optional.get();

        BeanCopier beanCopier = BeanCopier.create(SysUser.class, SysUserVo.class, false);
        SysUserVo vo = new SysUserVo();
        beanCopier.copy(sysUser, vo, null);

        if(vo.getDeptId() != null){
            Optional<SysDept> deptOptional = deptRepository.findById(vo.getDeptId());
            if (!deptOptional.isPresent()) {
                throw new CustomException("部门不存在");
            }
            SysDept sysDept = deptOptional.get();
            BeanCopier deptBeanCopier = BeanCopier.create(SysDept.class, SysDeptVo.class, false);
            SysDeptVo deptVo = new SysDeptVo();
            deptBeanCopier.copy(sysDept, deptVo, null);
            vo.setDept(deptVo);
        }

        return vo;
    }

    /**
     * 查询用户所属角色名称组
     *
     * @param userId 用户ID
     * @return 结果
     */
    public String selectUserRoleGroup(Long userId) {
        StringBuffer idsStr = new StringBuffer();

        Optional<SysUser> optional = userRepository.findById(userId);
        if (!optional.isPresent()) {
            return idsStr.toString();
        }

        List<Long> roleIds = userRoleRepository.findByUserId(userId).stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        if (roleIds.isEmpty()) {
            return idsStr.toString();
        }

        List<SysRole> list = roleRepository.findByIdIn(roleIds);
        for (SysRole role : list) {
            idsStr.append(role.getRoleName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString())) {
            return idsStr.substring(0, idsStr.length() - 1);
        }

        return idsStr.toString();
    }

    /**
     * 校验登录账号是否唯一
     *
     * @param id
     * @param loginName
     * @return
     */
    public Boolean checkLoginNameUniqueNotSelf(Long id, String loginName) {

        JPAQuery<SysUser> jpaQuery = queryFactory.select(QSysUser.sysUser).from(QSysUser.sysUser);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(QSysUser.sysUser.id.longValue().ne(id))
                .and(QSysUser.sysUser.loginName.eq(loginName)).and(QSysUser.sysUser.deleted.eq(0));

        jpaQuery.where(builder);

        QueryResults<SysUser> qs = jpaQuery.fetchResults();

        Long count = qs.getTotal();
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验登录账号是否唯一
     *
     * @param loginName
     * @return
     */
    public Boolean checkLoginNameUnique(String loginName) {
        int count = userRepository.countByLoginNameAndDeleted(loginName,0);
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param phone
     * @return
     */
    public Boolean checkPhoneUnique(String phone) {
        int count = userRepository.countByPhoneAndDeleted(phone,0);
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param id
     * @param phone
     * @return
     */
    public Boolean checkPhoneUniqueNotSelf(Long id, String phone) {

        JPAQuery<SysUser> jpaQuery = queryFactory.select(QSysUser.sysUser).from(QSysUser.sysUser);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(QSysUser.sysUser.id.longValue().ne(id))
                .and(QSysUser.sysUser.phone.eq(phone)).and(QSysUser.sysUser.deleted.eq(0));

        jpaQuery.where(builder);

        QueryResults<SysUser> qs = jpaQuery.fetchResults();

        Long count = qs.getTotal();
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验email是否唯一
     *
     * @param email
     * @return
     */
    public Boolean checkEmailUnique(String email) {
        int count = userRepository.countByEmailAndDeleted(email,0);
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验email是否唯一
     *
     * @param id
     * @param email
     * @return
     */
    public Boolean checkEmailUniqueNotSelf(Long id, String email) {
    	// 如果为空
    	if(StringUtils.isEmpty(email)){
    		return true;
    	}
        JPAQuery<SysUser> jpaQuery = queryFactory.select(QSysUser.sysUser).from(QSysUser.sysUser);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(QSysUser.sysUser.id.longValue().ne(id))
                .and(QSysUser.sysUser.email.eq(email)).and(QSysUser.sysUser.deleted.eq(0));

        jpaQuery.where(builder);

        QueryResults<SysUser> qs = jpaQuery.fetchResults();

        Long count = qs.getTotal();
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUserVo user) {
        if (StringUtils.isNotNull(user.getId()) && SecurityUtils.isSuperAdmin(user.getId())) {
            throw new CustomException("不允许操作超级管理员用户");
        }
    }

    /**
     * 新增用户信息
     *
     * @param vo 用户信息
     * @return 结果
     */
    @Transactional
    public void saveUser(SysUserVo vo) {
        Optional<SysDept> optional = deptRepository.findById(vo.getDeptId());
        if (!optional.isPresent()) {
            throw new CustomException("部门不存在");
        }
        Integer status = optional.get().getStatus();
        if (status == null || status == StatusEnum.DISABLE.value()) {
            throw new CustomException("部门已停用");
        }

        BeanCopier beanCopier = BeanCopier.create(SysUserVo.class, SysUser.class, false);
        SysUser entity = new SysUser();
        beanCopier.copy(vo, entity, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        entity.setCreateUserId(user.getId());
        entity.setCreateTime(new Date());
        entity.setAvatar("");
        entity.setDeleted(0);
        entity.setType(1);
        // 新增用户信息
        entity = userRepository.save(entity);
        vo.setId(entity.getId());

        SysUserAllotRole userAllotRole = null;
        for (Long aLong : vo.getAllotRoleIds()) {
            userAllotRole= new SysUserAllotRole();
            userAllotRole.setUserId(entity.getId());
            userAllotRole.setRoleId(aLong);
            userAllotRoleRepository.save(userAllotRole);
        }

        // 新增用户与角色管理
        this.insertUserRole(vo);

        addUserPosition(vo);
    }

    private void addUserPosition(SysUserVo vo) {
        List<Long> positionIds = vo.getPositionIds();
        if (StringUtils.isNull(positionIds)) {
            return;
        }

        // 新增用户与岗位关系
        List<SysPositionUser> list = positionIds.stream().map(positionId -> {
            SysPositionUser ur = new SysPositionUser();
            ur.setUserId(vo.getId());
            ur.setPositionId(positionId);
            return ur;
        }).collect(Collectors.toList());
        positionUserRepository.saveAll(list);
    }

    /**
     * 修改用户信息
     *
     * @param vo 用户信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(SysUserVo vo, HttpServletRequest request) {
        Optional<SysDept> deptOptional = deptRepository.findById(vo.getDeptId());
        if (!deptOptional.isPresent()) {
            throw new CustomException("部门不存在");
        }
        Integer status = deptOptional.get().getStatus();
        if (status == null || status == StatusEnum.DISABLE.value()) {
            throw new CustomException("部门已停用");
        }

        Long userId = vo.getId();
        Optional<SysUser> optional = userRepository.findById(userId);
        if (!optional.isPresent()) {
            throw new CustomException("用户不存在");
        }
        SysUser entity = optional.get();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        // 删除用户与角色关联
        userRoleRepository.deleteByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(vo);

        //删除用户与岗位们的关系表
        //positionUserRepository.deleteByUserId(vo.getId());
        //保存用户与岗位的关系
        /*List<SysPositionUser> listPostUser = new ArrayList<SysPositionUser>();
        for (Long positionId : vo.getPositionIds()) {
            SysPositionUser puEntity = new SysPositionUser();
            puEntity.setUserId(vo.getId());
            puEntity.setPositionId(positionId);
            listPostUser.add(puEntity);
        }
        positionUserRepository.saveAll(listPostUser);*/

        //删除用户与角色的关系表
        userAllotRoleRepository.deleteByUserId(vo.getId());
        //保存用户与角色的关系
        List<SysUserAllotRole> listAllotRole = new ArrayList<SysUserAllotRole>();
        for (Long aLong : vo.getAllotRoleIds()) {
            SysUserAllotRole arEntity=new SysUserAllotRole();
            arEntity.setUserId(entity.getId());
            arEntity.setRoleId(aLong);
            listAllotRole.add(arEntity);

        }
        userAllotRoleRepository.saveAll(listAllotRole);
        //修改招商，零售客户表 的部门负责人以及机构（部门）
        if(entity.getDeptId() != vo.getDeptId()){
        	Optional<SysDept> sysDept = deptRepository.findById(vo.getDeptId());
            if ( sysDept.isPresent()){
                SysDept dept = sysDept.get();
                String rtSql="UPDATE rt_customer SET dept_head_id="+dept.getHeadUserId()+"  , dept_id="+vo.getDeptId()+"  WHERE sales_id= "+userId;
                String mchSql="UPDATE mch_customer SET dept_head_id="+dept.getHeadUserId()+"  , dept_id="+vo.getDeptId()+"  WHERE sales_id=" +userId;
                jdbcTemplate.update(rtSql);
                jdbcTemplate.update(mchSql);


                // 修改用户数据时，需要同步更新用户缓存数据
                user.setDeptId(vo.getDeptId());
                SysDeptVo deptVo = new SysDeptVo();
                BeanUtils.copyProperties(dept, deptVo);
                user.setDept(deptVo);
                loginUser.setUser(user);
                updateScurityContext(loginUser, request);
            }
        }

        // 修改用户信息
        entity.setUserName(vo.getUserName());
        entity.setGender(vo.getGender());
        entity.setLoginName(vo.getLoginName());
        entity.setPhone(vo.getPhone());
        entity.setEmail(vo.getEmail());
        entity.setDeptId(vo.getDeptId());
        entity.setStatus(vo.getStatus());
        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());
        userRepository.save(entity);
    }

    /**
     * 修改用户状态
     *
     * @param vo 用户信息
     * @return 结果
     */
    public void updateUserStatus(SysUserVo vo) {
        Optional<SysUser> optional = userRepository.findById(vo.getId());
        if (!optional.isPresent()) {
            throw new CustomException("用户不存在");
        }
        SysUser entity = optional.get();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());
        entity.setStatus(vo.getStatus());
        userRepository.save(entity);
    }

    /**
     * 修改用户基本信息
     *
     * @param vo 用户信息
     * @return 结果
     */
    public void updateUserProfile(SysUserVo vo) {
        Optional<SysUser> optional = userRepository.findById(vo.getId());
        if (!optional.isPresent()) {
            throw new CustomException("用户不存在");
        }
        SysUser entity = optional.get();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        entity.setUserName(vo.getUserName());
        entity.setPhone(vo.getPhone());
        entity.setEmail(vo.getEmail());
        entity.setGender(vo.getGender());
        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());
        userRepository.save(entity);
    }

    /**
     * 修改用户头像
     *
     * @param userId 用户ID
     * @param avatar 头像
     */
    public void updateUserAvatar(Long userId, String avatar) {
        Optional<SysUser> optional = userRepository.findById(userId);
        if (!optional.isPresent()) {
            throw new CustomException("用户不存在");
        }
        SysUser entity = optional.get();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        entity.setAvatar(avatar);
        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());
        userRepository.save(entity);
    }

    /**
     * 重置用户密码
     *
     * @param vo 用户信息
     * @return 结果
     */
    public void resetPwd(SysUserVo vo) {
        Optional<SysUser> optional = userRepository.findById(vo.getId());
        if (!optional.isPresent()) {
            throw new CustomException("用户不存在");
        }
        SysUser entity = optional.get();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        entity.setPassword(SecurityUtils.encryptPassword(vo.getPassword()));
        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());
        userRepository.save(entity);
    }

    /**
     * 重置用户密码
     *
     * @param loginName 登录账号
     * @param password  密码
     * @return 结果
     */
    public void resetUserPwd(String loginName, String password) {
        SysUser entity = userRepository.findByLoginName(loginName);
        if (entity == null) {
            throw new CustomException("用户不存在");
        }

        entity.setPassword(password);
        userRepository.save(entity);
    }

    /**
     * 新增用户角色信息
     *
     * @param vo 用户对象
     */
    public void insertUserRole(SysUserVo vo) {
        List<Long> roleIds = vo.getRoleIds();
        if (StringUtils.isNull(roleIds)) {
            return;
        }

        // 新增用户与角色管理
        List<SysUserRole> list = roleIds.stream().map(roleId -> {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(vo.getId());
            ur.setRoleId(roleId);
            return ur;
        }).collect(Collectors.toList());
        userRoleRepository.saveAll(list);
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public void deleteUserById(Long userId) {
        /*// 删除用户与角色关联
        userRoleRepository.deleteByUserId(userId);

        // 删除用户
        userRepository.deleteById(userId);*/

    	 // 软删除
        Optional<SysUser> userOpt = userRepository.findById(userId);
        if(!userOpt.isPresent()){
        	throw new CustomException("用户未找到");
        }
        SysUser entity = userOpt.get();
        entity.setDeleted(1);
        userRepository.save(entity);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public void deleteUserByIds(List<Long> userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUserVo(userId));
        }

        //userRoleRepository.deleteBatch(userIds);
        //positionUserRepository.deleteBatch(userIds);
        //userRepository.deleteBatch(userIds);

        // 软删除
        List<SysUser> list = userRepository.findByIdIn(userIds);
        for (SysUser sysUser : list) {
			sysUser.setDeleted(1);
		}
        userRepository.saveAll(list);
    }

    /**
     * 查询用户部门数据权限列表
     *
     * @param user 用户
     * @return 部门权限列表
     */
    public List<Long> getUserDataScopes(SysUserVo user) {

        String dataType = configService.selectConfigByKey(DATA_PERMISSION_TYPE);

        if ("1".equals(dataType)) {
            return findDataPermissionByRole(user);
        } else if ("2".equals(dataType)) {
            return findDataPermissionByPosition(user);
        } else if ("3".equals(dataType)) {
            return getUserDeptList(user);
        }
        log.error("系统参数管理中没有配置data.permission.type参数. 1-角色控制数据权限 2-岗位控制数据权限 3-用户控制数据权限");

        return (new ArrayList<Long>());

    }

    /**
     * 查询用户部门数据权限列表
     *
     * @param
     * @return 部门权限列表
     */
    public List<Long> getUserDeptList(SysUserVo user) {
    	JPAQuery<Long> jpaQuery = null;
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());

        List<Long> data = null;
        if (isSuperAdmin) {
            jpaQuery = queryFactory.select(QSysDept.sysDept.id).from(QSysDept.sysDept);
            QueryResults<Long> qs = jpaQuery.fetchResults();
            data = qs.getResults();
        } else {
        	data = sysUserDeptRepository.findByUserId(user.getId()).stream().map(SysUserDept::getDeptId).collect(Collectors.toList());
        }

        if (data == null) {
            return null;
        }
        return data;
    }

    private List<Long> findDataPermissionByPosition(SysUserVo user) {
        JPAQuery<Long> jpaQuery = null;

        BooleanBuilder builder = new BooleanBuilder();

        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());
        if (isSuperAdmin) {
            jpaQuery = queryFactory.select(QSysDept.sysDept.id).from(QSysDept.sysDept);
        } else {
            jpaQuery = queryFactory.selectDistinct(QSysDept.sysDept.id).from(QSysDept.sysDept)
                    .leftJoin(QSysPositionDept.sysPositionDept).on(QSysPositionDept.sysPositionDept.deptId.longValue().eq(QSysDept.sysDept.id.longValue()))
                    .leftJoin(QSysPosition.sysPosition).on(QSysPosition.sysPosition.id.longValue().eq(QSysPositionDept.sysPositionDept.positionId.longValue()))
                    .leftJoin(QSysPositionUser.sysPositionUser).on(QSysPosition.sysPosition.id.longValue().eq(QSysPositionUser.sysPositionUser.positionId.longValue()))
                    .leftJoin(QSysUser.sysUser).on(QSysUser.sysUser.id.longValue().eq(QSysPositionUser.sysPositionUser.userId.longValue()));

            builder.and(QSysUser.sysUser.status.intValue().eq(StatusEnum.NORMAL.value()));
            builder.and(QSysUser.sysUser.id.longValue().eq(user.getId()));

            jpaQuery.where(builder);
        }

        QueryResults<Long> qs = jpaQuery.fetchResults();
        List<Long> data = qs.getResults();

        if (data == null) {
            return null;
        }

        return data;
    }

    private List<Long> findDataPermissionByRole(SysUserVo user) {

        JPAQuery<Long> jpaQuery = null;

        BooleanBuilder builder = new BooleanBuilder();

        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());
        if (isSuperAdmin) {
            jpaQuery = queryFactory.select(QSysDept.sysDept.id).from(QSysDept.sysDept);
        } else {
            jpaQuery = queryFactory.selectDistinct(QSysDept.sysDept.id).from(QSysDept.sysDept)
                    .leftJoin(QSysRoleDept.sysRoleDept).on(QSysRoleDept.sysRoleDept.deptId.eq(QSysDept.sysDept.id))
                    .leftJoin(QSysRole.sysRole).on(QSysRole.sysRole.id.eq(QSysRoleDept.sysRoleDept.roleId))
                    .leftJoin(QSysUserRole.sysUserRole).on(QSysUserRole.sysUserRole.roleId.eq(QSysRole.sysRole.id))
                    .leftJoin(QSysUser.sysUser).on(QSysUser.sysUser.id.eq(QSysUserRole.sysUserRole.userId));

            builder.and(QSysUser.sysUser.status.intValue().eq(StatusEnum.NORMAL.value()));
            builder.and(QSysUser.sysUser.id.longValue().eq(user.getId()));

            jpaQuery.where(builder);
        }

        QueryResults<Long> qs = jpaQuery.fetchResults();
        List<Long> data = qs.getResults();

        if (data == null) {
            return null;
        }

        return data;
    }

    /**
	 * 保存用户数据权限(部门)
	 *
	 * @param id
	 * @param departmentIds
	 * <AUTHOR>
	 * @date 2021年11月9日
	 */
	public void saveUserDepartment(Long id, Set<Long> departmentIds) {
		Optional<SysUser> userOpt = userRepository.findById(id);
		if (!userOpt.isPresent()) {
			throw new CustomException("用户不存在");
		}
		jdbcTemplate.update("DELETE FROM sys_user_dept WHERE user_id=?", id);
		jdbcTemplate.batchUpdate("INSERT INTO sys_user_dept (dept_id, user_id) VALUES (?,?)",
				departmentIds, departmentIds.size(), (preparedStatement, aLong) -> {
					preparedStatement.setLong(1, aLong);
					preparedStatement.setLong(2, id);
				});

	}

    /**
     * 离职继承
     * @param leaveUserId
     * @param inheritUserId
     */
    public void leave(Long leaveUserId, Long inheritUserId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        String sql = "select * from sys_user where id =?";
        List<SysUserVo> leaveUser = jdbcTemplate.query(sql,new BeanPropertyRowMapper<>(SysUserVo.class),leaveUserId);
        List<SysUserVo> inheritUser = jdbcTemplate.query(sql,new BeanPropertyRowMapper<>(SysUserVo.class),inheritUserId);
        if(leaveUser.size()==0){
            throw new CustomException("离职用户不存在");
        }
        if(inheritUser.size()==0){
            throw new CustomException("继承用户不存在");
        }
        if(inheritUser.get(0).getStatus()==0){
            throw new CustomException("继承用户已停用");
        }
        String userSql = "UPDATE `sys_user` SET STATUS =0 WHERE id =?";
        jdbcTemplate.update(userSql,leaveUserId);//停用离职用户
        Optional<SysUser> userOpt = userRepository.findById(inheritUserId);//继承用户的部门
        Optional<SysDept> sysDept = deptRepository.findById(userOpt.get().getDeptId());//继承用户的部门的负责人
        String sql1 = "UPDATE `sys_dept` SET head_user_id = ? WHERE head_user_id = ?";
        jdbcTemplate.update(sql1,inheritUserId,leaveUserId);//修改部门表部门负责人
        //零售
        String sql2 = "UPDATE rt_customer SET dept_head_id=?,dept_id=? WHERE dept_head_id=?";
        jdbcTemplate.update(sql2,sysDept.get().getHeadUserId(),userOpt.get().getDeptId(),leaveUserId);//修改客户表部门负责人,部门id，销售id
        String sql3 = "UPDATE rt_customer SET sales_id=? WHERE sales_id=?";
        jdbcTemplate.update(sql3,inheritUserId,leaveUserId);//修改客户表销售id
        //招商
        String sql4 = "UPDATE mch_customer SET dept_head_id=?,dept_id=? WHERE dept_head_id=?";
        jdbcTemplate.update(sql4,sysDept.get().getHeadUserId(),userOpt.get().getDeptId(),leaveUserId);//修改客户表部门负责人,部门id，销售id
        String sql5 = "UPDATE mch_customer SET sales_id=? WHERE sales_id=?";
        jdbcTemplate.update(sql5,inheritUserId,leaveUserId);//修改客户表销售id

        SysUserLeaveLog userLeaveLog = new SysUserLeaveLog();//插入用户离职继承记录
        userLeaveLog.setLeaveUserId(leaveUserId);
        userLeaveLog.setInheritUserId(inheritUserId);
        userLeaveLog.setOperatorUserId(user.getId());
        userLeaveLog.setOperatorTime(new Date());
        sysUserLeaveLogRepository.save(userLeaveLog);
    }

    /**
     * 同步修改用户换rendis与security数据
     * @param loginUser
     * <AUTHOR>
     * @date 2022年4月26日
     */
    public void updateScurityContext(LoginUser loginUser, HttpServletRequest request){
		tokenService.verifyToken(loginUser);
		UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
		authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
		SecurityContextHolder.getContext().setAuthentication(authenticationToken);
	}
}
