package com.gl.system.entity;

import com.gl.framework.entity.IdEntity;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户和角色关联表实体
 */
@Entity
@Table(name = "sys_user_role")
public class SysUserRole extends IdEntity {
	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 角色ID
	 */
	private Long roleId;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	@Override
	public String toString() {
		return "SysUserRole [id=" + id + ", userId=" + userId + ", roleId=" + roleId + "]";
	}

}
