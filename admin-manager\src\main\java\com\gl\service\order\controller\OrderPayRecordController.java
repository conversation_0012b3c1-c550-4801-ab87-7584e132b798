package com.gl.service.order.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.order.service.OrderPayRecordService;
import com.gl.service.order.vo.dto.OrderPayRecordDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 订单支付记录
 */
@Controller
@RequestMapping("/orderPayRecord")
public class OrderPayRecordController {

    @Autowired
    private OrderPayRecordService orderPayRecordService;

    /**
     * 根据订单id查询支付记录
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('orderPayRecord:orderPayRecord:list')")
    public Result getByOrderId(OrderPayRecordDto dto) {
        return orderPayRecordService.getByOrderId(dto);
    }

}
