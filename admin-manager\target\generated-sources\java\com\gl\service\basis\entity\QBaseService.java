package com.gl.service.basis.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBaseService is a Querydsl query type for BaseService
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBaseService extends EntityPathBase<BaseService> {

    private static final long serialVersionUID = 2038030270L;

    public static final QBaseService baseService = new QBaseService("baseService");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath phone = createString("phone");

    public final StringPath wechat = createString("wechat");

    public QBaseService(String variable) {
        super(BaseService.class, forVariable(variable));
    }

    public QBaseService(Path<? extends BaseService> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBaseService(PathMetadata metadata) {
        super(BaseService.class, metadata);
    }

}

