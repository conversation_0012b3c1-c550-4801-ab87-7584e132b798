package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.system.service.SysLoginLogService;
import com.gl.system.vo.SysLoginLogVo;
import com.gl.framework.web.domain.PageData;
import com.gl.framework.web.response.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 登录日志控制器
 */
@RestController
@RequestMapping("/system/loginlog")
public class SysLoginLogController {

    @Autowired
    private SysLoginLogService loginLogService;

    /**
     * 登录日志分页列表
     *
     * @param filter
     * @return
     */
    @GetMapping("/list")
    @PreAuthorize("@ps.hasPermi('system:loginlog:list')")
    public PageData<SysLoginLogVo> list(SysLoginLogVo filter) {
        return loginLogService.selectLoginLogList(filter);
    }

    /**
     * 删除登录日志
     *
     * @param ids
     * @return
     */
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ps.hasPermi('system:loginlog:remove')")
    @Log(title = "登陆日志", businessType = BusinessType.DELETE, businessTypeName = "删除")
    public Result remove(@PathVariable List<Long> ids) {
        loginLogService.deleteLoginLogByIds(ids);
        return Result.success();
    }

    /**
     * 清空登录日志
     *
     * @return
     */
    @DeleteMapping("/clean")
    @PreAuthorize("@ps.hasPermi('system:loginlog:remove')")
    @Log(title = "登陆日志", businessType = BusinessType.CLEAN, businessTypeName = "清空登录日志")
    public Result clean() {
        loginLogService.cleanLoginLog();
        return Result.success();
    }

}
