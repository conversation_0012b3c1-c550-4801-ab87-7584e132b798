package com.gl.commons.enums;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2025-06-16-14:58
 */
public enum StoreTypeEnum {
    /**
     * 总店
     */
    PARENT(1,"总店"),
    /**
     * 分店
     */
    BRANCH(2,"分店");
    private Integer code;
    private String name;
    StoreTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public Integer getCode() {
        return code;
    }
}
