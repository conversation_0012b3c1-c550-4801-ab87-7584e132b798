package com.gl.service.message.vo.dto;

import com.gl.framework.web.domain.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: duanjinze
 * @date: 2022/11/11 17:08
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseServiceMessageDto extends BaseVo {
    /**
     * 搜索框 【留言内容】
     */
    private String searchCondition;

    /**
     * 留言id集合
     */
    private List<Long> ids;

}
