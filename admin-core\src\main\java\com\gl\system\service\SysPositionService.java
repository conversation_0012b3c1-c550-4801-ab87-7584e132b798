package com.gl.system.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.system.entity.*;
import com.gl.system.repository.SysPositionDeptRepository;
import com.gl.system.repository.SysPositionRepository;
import com.gl.system.repository.SysPositionUserRepository;
import com.gl.system.vo.SysPositionVo;
import com.gl.system.vo.SysUserVo;
import com.gl.framework.web.domain.PageData;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 */
@Service
public class SysPositionService {

    @Autowired
    private SysPositionRepository positionRepository;

    @Autowired
    private SysPositionUserRepository positionUserRepository;
    @Autowired
    private SysPositionDeptRepository positionDeptRepository;
    @Autowired
    JPAQueryFactory queryFactory;

    /**
     * 根据条件分页查询岗位数据
     *
     * @param filter 过滤条件
     * @return 角色数据集合信息
     */
    public PageData<SysPositionVo> list(SysPositionVo filter) {

        // 数据权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        SysUserVo user = loginUser.getUser();
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());

        PageData<SysPositionVo> pageData = new PageData<>();

        Integer pageNumber = filter.getPageNumber() - 1;
        //页面条数
        Integer pageSize = filter.getPageSize();
        Pageable pageable = PageRequest.of(pageNumber, pageSize);


        JPAQuery<SysPosition> jpaQuery = queryFactory.select(QSysPosition.sysPosition).from(QSysPosition.sysPosition);

        BooleanBuilder builder = new BooleanBuilder();

        if (StringUtils.isNotEmpty(filter.getName())) {
            builder.or(QSysPosition.sysPosition.name.like('%' + filter.getName().replaceAll("'", "") + '%'));
        }
        if (StringUtils.isNotEmpty(filter.getCode())) {
            builder.or(QSysPosition.sysPosition.code.like('%' + filter.getCode().replaceAll("'", "") + '%'));
        }

        jpaQuery.where(builder)
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize());


        //拿到分页结果
        QueryResults<SysPosition> qs = jpaQuery.fetchResults();

        pageData.setTotal(qs.getTotal());

        List<SysPositionVo> data = convert(qs);
        pageData.setData(data);

        return pageData;
    }

    private List<SysPositionVo> convert(QueryResults<SysPosition> jpaQuery) {
        List<SysPosition> list = jpaQuery.getResults();
        List<SysPositionVo> listVo = new ArrayList<SysPositionVo>();

        for (SysPosition entity : list) {
            BeanCopier beanCopier = BeanCopier.create(SysPosition.class, SysPositionVo.class, false);
            SysPositionVo vo = new SysPositionVo();
            beanCopier.copy(entity, vo, null);
            listVo.add(vo);
        }

        return listVo;
    }

    /**
     * 数据转换
     *
     * @param entity
     * @return
     */
    private SysPositionVo convert(SysPosition entity) {
        BeanCopier beanCopier = BeanCopier.create(SysPosition.class, SysPositionVo.class, false);
        SysPositionVo vo = new SysPositionVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    /**
     * 查看用户能看到的岗位
     */

    public List<SysPositionVo> queryPosition() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        SysUserVo user = loginUser.getUser();
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());

        JPAQuery<SysPosition> jpaQuery = queryFactory.selectDistinct(QSysPosition.sysPosition).from(QSysPosition.sysPosition)
                .leftJoin(QSysPositionUser.sysPositionUser).on(QSysPositionUser.sysPositionUser.positionId.longValue().eq(QSysPosition.sysPosition.id.longValue()))
                .leftJoin(QSysUser.sysUser).on(QSysUser.sysUser.id.longValue().eq(QSysPositionUser.sysPositionUser.userId.longValue()));

        BooleanBuilder builder = new BooleanBuilder();
        if (!isSuperAdmin) {
            builder.and(QSysUser.sysUser.id.longValue().eq(user.getId()));

            jpaQuery.where(builder);
        }

        QueryResults<SysPosition> qs = jpaQuery.fetchResults();
        List<SysPosition> list = qs.getResults();
        if (list == null) {
            return Collections.emptyList();
        }

        List<SysPositionVo> listVo = list.stream().map(this::convert).collect(Collectors.toList());
        return listVo;
    }


    /**
     * 根据用户ID查看用户能看到的岗位
     */

    public List<SysPositionVo> userPosition(Long userId) {
        JPAQuery<SysPosition> jpaQuery = queryFactory.selectDistinct(QSysPosition.sysPosition).from(QSysUserAllotRole.sysUserAllotRole)
                .leftJoin(QSysPosition.sysPosition).on(QSysPosition.sysPosition.id.longValue().eq(QSysUserAllotRole.sysUserAllotRole.roleId.longValue()));
        BooleanBuilder builder = new BooleanBuilder();
            builder.and(QSysUserAllotRole.sysUserAllotRole.userId.longValue().eq(userId));
            jpaQuery.where(builder);


        QueryResults<SysPosition> qs = jpaQuery.fetchResults();
        List<SysPosition> list = qs.getResults();
        if (list == null) {
            return Collections.emptyList();
        }

        List<SysPositionVo> listVo = list.stream().map(this::convert).collect(Collectors.toList());
        return listVo;
    }

    /**
     * 通过ID查询岗位
     *
     * @param id 岗位ID
     * @return 岗位对象信息
     */
    public SysPositionVo selectPostById(Long id) {
        Optional<SysPosition> optional = positionRepository.findById(id);
        if (!optional.isPresent()) {
            return null;
        }

        SysPosition entity = optional.get();
        return this.convert(entity);
    }

    /**
     * 校验岗位名称是否唯一
     *
     * @param vo 角色信息
     * @return 结果
     */
    public Boolean checkPostNameUnique(SysPositionVo vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        SysUserVo user = loginUser.getUser();

        Long total = positionRepository.countByName(vo.getName());
        return (total > 0 ? true : false);
    }

    /**
     * 校验岗位编码是否唯一
     *
     * @param vo 岗位信息
     * @return 结果
     */
    public Boolean checkPostCodeUnique(SysPositionVo vo) {
        Long total = positionRepository.countByCode(vo.getCode());
        return (total > 0 ? true : false);
    }

    /**
     * 校验岗位编码是否唯一
     *
     * @param vo 岗位信息
     * @return 结果
     */
    public Boolean checkPostIdAndCodeUnique(SysPositionVo vo) {
        Long total = positionRepository.countByIdAndCode(vo.getId(), vo.getCode());
        return (total > 0 ? true : false);
    }

    /**
     * 新增岗位信息
     *
     * @param vo 岗位信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(SysPositionVo vo) {
        BeanCopier beanCopier = BeanCopier.create(SysPositionVo.class, SysPosition.class, false);
        SysPosition entity = new SysPosition();
        beanCopier.copy(vo, entity, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        entity.setCreateUserId(user.getId());
        entity.setCreateTime(new Date());

        positionRepository.save(entity);
    }

    /**
     * 修改岗位信息
     *
     * @param vo 岗位信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(SysPositionVo vo) {
        Optional<SysPosition> optional = positionRepository.findById(vo.getId());
        if (!optional.isPresent()) {
            throw new CustomException("岗位数据不存在");
        }

        SysPosition entity = optional.get();

        BeanCopier beanCopier = BeanCopier.create(SysPositionVo.class, SysPosition.class, false);
        beanCopier.copy(vo, entity, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());

        positionRepository.save(entity);
    }

    /**
     * 通过用户ID查询用户的岗位
     *
     * @param userId
     * @return
     */
    public List<Long> queryPositionIds(Long userId) {

        List<SysPositionUser> list = positionUserRepository.findByUserId(userId);

        if (list == null) {
            return Collections.emptyList();
        }

        List<Long> listIds = new ArrayList<Long>();
        for (SysPositionUser entity : list) {
            listIds.add(entity.getPositionId());
        }


        return listIds;
    }

    @Transactional
    public void deleteByIds(List<Long> ids) {
        if (ids == null) {
            return;
        }

        for (Long id : ids) {
            positionDeptRepository.deleteByPositionId(id);
            positionRepository.deleteById(id);
        }
    }

    @Transactional
    public void authDataScope(SysPositionVo position) {

        positionDeptRepository.deleteByPositionId(position.getId());

        List<Long> listIds = position.getDeptIds();

        if (listIds == null || listIds.isEmpty()) {
            return;
        }

        positionDeptRepository.deleteByPositionId(position.getId());

        List<SysPositionDept> list = new ArrayList<>();
        for (Long id : listIds) {
            SysPositionDept entity = new SysPositionDept();
            entity.setPositionId(position.getId());
            entity.setDeptId(id);
            list.add(entity);
        }

        positionDeptRepository.saveAll(list);

    }
}
