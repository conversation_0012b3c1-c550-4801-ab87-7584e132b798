package com.gl.service.order.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "dub_order_pay_record")
public class OrderPayRecord {

    /** 主键id */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 订单ID */
    @Basic
    @Column(name = "order_id")
    private Long orderId;

    /** '微信支付的交易流水号' */
    @Basic
    @Column(name = "transaction_id")
    private String transactionId;

    /** 实际支付金额,单位分 */
    @Basic
    @Column(name = "amount")
    private Integer amount;

    /** '支付时间' */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

    /** '完成时间' */
    @Basic
    @Column(name = "end_time")
    private Date endTime;

    /** 支付状态 0-支付失败  1-已支付 2-待支\n付 4-取消支付 */
    @Basic
    @Column(name = "status")
    private Integer status;


}
