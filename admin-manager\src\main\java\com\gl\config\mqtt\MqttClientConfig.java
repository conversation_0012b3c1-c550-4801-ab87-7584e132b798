package com.gl.config.mqtt;

import com.gl.service.deviceOperationLog.service.DeviceOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.PostConstruct;

@Configuration
@Slf4j
public class MqttClientConfig {

    @Value("${mqtt.broker}")
    private String brokerUrl;

    @Value("${mqtt.clientId}")
    private String clientId;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Autowired
    private MqttClientCallBack mqttClientCallBack;
    @Autowired
    private DeviceOperationLogService deviceOperationLogService;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * 客户端对象
     */
    private MqttAsyncClient client;

    /**
     * 在bean初始化后连接到服务器
     */
    @PostConstruct
    public void init() {
        connect();
    }

    /**
     * 客户端连接服务端
     */
    public void connect() {
        //连接设置
        MqttConnectOptions options = new MqttConnectOptions();
        //是否清空session，设置false表示服务器会保留客户端的连接记录（订阅主题，qos）,客户端重连之后能获取到服务器在客户端断开连接期间推送的消息
        //设置为true表示每次连接服务器都是以新的身份
        options.setCleanSession(false);
        //设置连接用户名
        options.setUserName(username);
        //设置连接密码
        options.setPassword(password.toCharArray());
        //设置超时时间，单位为秒
        options.setConnectionTimeout(60);
        //设置心跳时间 单位为秒，表示服务器每隔 1.5*10秒的时间向客户端发送心跳判断客户端是否在线
        options.setKeepAliveInterval(20);
        // 开启自动重连
        options.setAutomaticReconnect(true);
        // 设置最大重连时间间隔 (可选)，单位是毫秒，设置为 5000 表示最多等待 5 秒再尝试重连
        options.setMaxReconnectDelay(5000);
        //设置遗嘱消息的话题，若客户端和服务器之间的连接意外断开，服务器将发布客户端的遗嘱信息
//        options.setWill("willTopic", (clientId + "与服务器断开连接").getBytes(), 0, false);
        try {
            //创建MQTT客户端对象
            client = new MqttAsyncClient(brokerUrl, clientId, new MemoryPersistence());
            //设置回调
            client.setCallback(mqttClientCallBack);
            // 使用异步连接
            client.connect(options, null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    log.info("MQTT连接成功");
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    log.error("MQTT连接失败：" + exception.getMessage());
                }
            });
        } catch (MqttException e) {
            log.error("mqtt连接失败。。" + e.getMessage());
        }
    }

    public void publish(String sn, String message, int qos, boolean retained, Long userId, String timeStamp) {
        MqttMessage mqttMessage = new MqttMessage();
        mqttMessage.setQos(qos);
        mqttMessage.setRetained(retained);
        mqttMessage.setPayload(message.getBytes());
        try {
            String topic = "device/command/request/" + sn;
            // 使用异步客户端发布消息，并处理结果
            client.publish(topic, mqttMessage, null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    deviceOperationLogService.sendSave(sn, new String(message.getBytes()), "request", userId, timeStamp);
                    log.info("发送成功, topic={}, message={}, userId={}, timeStamp={}", topic, message, userId, timeStamp);
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    log.error("发送失败：" + exception.getMessage());
                }
            });
        } catch (MqttException e) {
            log.error("发送失败：" + e.getMessage());
        }
    }

    /**
     * 断开连接
     */
    public void disConnect() {
        try {
            client.disconnect();
        } catch (MqttException e) {
            log.error(e.getMessage(), e);
        }
    }


    // 生成时间戳
    public synchronized String getTimestamp() {
        long tt = 0L;
        String timestamp = null;
        while (true) {
            tt = System.currentTimeMillis();
            timestamp = "2-" + tt;
            String redisTT = redisTemplate.opsForValue().get(timestamp);
            if (StringUtils.isEmpty(redisTT)) {
                break;
            }
            try {
                Thread.sleep(2);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }
        return timestamp;
    }

    // 获取MQTT返回的结果，如果超过20秒可以通过查询数据的方式获取
    public String getTopicResponse(String key) {

        String payload = null;
        int index = 0;
        while (true) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            if (index > 20) {
                break;
            }

            payload = redisTemplate.opsForValue().get(key);
            if (StringUtils.isNotEmpty(payload)) {
                break;
            }

            index++;
        }

        return payload;
    }
}
