package com.gl.system.repository;


import com.gl.system.entity.SysUserAllotRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface SysUserAllotRoleRepository extends JpaRepository<SysUserAllotRole, Long>, JpaSpecificationExecutor<SysUserAllotRole> {

    @Transactional
    void deleteByUserId(Long userId);


}
