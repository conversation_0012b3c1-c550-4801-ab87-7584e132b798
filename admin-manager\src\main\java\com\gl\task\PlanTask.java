package com.gl.task;

import com.alibaba.fastjson.JSONObject;
import com.gl.config.mqtt.MqttClientConfig;
import com.gl.framework.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PlanTask {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private MqttClientConfig mqttClientConfig;

    // 每隔5秒执行一次
//    @Scheduled(cron = "0/1 * * * * ?")
//    public void runPlan() {
//        log.info("开始执行播报计划");
//
//        String sql = "select p.id, p.shop_id, p.device_ids, w.title, w.content, vp.name, vp.voice_time, vp.file_url, d.`sn`\n"
//                +
//                "from `dub_broadcast_plan` p\n" +
//                "left join `dub_broadcast_plan_voice_work_ref` pv on pv.`plan_id` = p.`id`\n" +
//                "left join `dub_voice_work` w on w.id = pv.`voice_work_id`\n" +
//                "left join `dub_voice_packet` vp on vp.id = w.`voice_id`\n" +
//                "left join `dub_device` d on d.id in (p.`device_ids`)\n" +
//                "where ( (curdate() between STR_TO_DATE(p.`start_date`, '%Y-%m-%d') and STR_TO_DATE(p.`end_date`, '%Y-%m-%d')) or p.type = 1 )\n"
//                +
//                "and now() between str_to_date(CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' ', start_time),'%Y-%m-%d %H:%i') and str_to_date(CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' ', end_time),'%Y-%m-%d %H:%i')\n"
//                +
//                "and TIMESTAMPDIFF(SECOND, str_to_date(CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' ', start_time, ':00'),'%Y-%m-%d %H:%i:%s'), now()) % p.`interval_time` = 0";
//
//        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
//
//        if (list == null || list.isEmpty()) {
//            return;
//        }
//
//        for (Map<String, Object> map : list) {
//            String sn = String.valueOf(map.get("sn"));
//            String fileUrl = String.valueOf(map.get("file_url"));
//            if (StringUtils.isEmpty(fileUrl)) {
//                continue;
//            }
//            urlCmd(sn, fileUrl, 0L);
//        }
//    }
//
//    @Async
//    public void urlCmd(String sn, String url, Long userId) {
//        log.info("定时任务开始发送设备音频文件 sn = {}, url = {}", sn, url);
//        String timeStamp = getTimestamp();
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("cmd", "url_cmd");
//        jsonObject.put("parm", url);
//        jsonObject.put("timeStamp", timeStamp);
//        mqttClientConfig.publish(sn, jsonObject.toJSONString(), 2, false, 0L, timeStamp);
//    }
//
//    // 生成时间戳
//    public synchronized String getTimestamp() {
//        long tt = System.currentTimeMillis();
//        String timestamp = "2-" + String.valueOf(tt);
//        while (true) {
//            tt = System.currentTimeMillis();
//            timestamp = "2-" + String.valueOf(tt);
//            String redisTT = redisTemplate.opsForValue().get(timestamp);
//            if (org.apache.commons.lang3.StringUtils.isEmpty(redisTT)) {
//                break;
//            }
//        }
//        return timestamp;
//    }
}
