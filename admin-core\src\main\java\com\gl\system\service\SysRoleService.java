package com.gl.system.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.gl.system.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gl.framework.common.util.DateUtils;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.domain.PageData;
import com.gl.system.repository.SysRoleDeptRepository;
import com.gl.system.repository.SysRoleMenuRepository;
import com.gl.system.repository.SysRoleRepository;
import com.gl.system.repository.SysUserRoleRepository;
import com.gl.system.vo.SysRoleVo;
import com.gl.system.vo.SysUserVo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;

/**
 * 角色服务实现类
 */
@Service
public class SysRoleService {

    @Autowired
    private SysRoleRepository roleRepository;
    @Autowired
    private SysUserRoleRepository userRoleRepository;
    @Autowired
    private SysRoleDeptRepository roleDeptRepository;
    @Autowired
    private SysRoleMenuRepository roleMenuRepository;
    @Autowired
    JPAQueryFactory queryFactory;

    /**
     * 根据条件分页查询角色数据
     *
     * @param filter 过滤条件
     * @return 角色数据集合信息
     */
    public PageData<SysRoleVo> selectRoleList(SysRoleVo filter) {

        // 数据权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        SysUserVo user = loginUser.getUser();
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());

        PageData<SysRoleVo> pageData = new PageData<>();

        Integer pageNumber = filter.getPageNumber() - 1;
        //页面条数
        Integer pageSize = filter.getPageSize();
        Pageable pageable = PageRequest.of(pageNumber, pageSize);


        JPAQuery<Tuple> jpaQuery = queryFactory.select(QSysRole.sysRole, QSysUser.sysUser.userName).from(QSysRole.sysRole);
        jpaQuery.leftJoin(QSysUser.sysUser).on(QSysRole.sysRole.createUserId.longValue().eq(QSysUser.sysUser.id.longValue()));


        BooleanBuilder builder = new BooleanBuilder();

        if (StringUtils.isNotEmpty(filter.getRoleName())) {
            builder.or(QSysRole.sysRole.roleName.like('%' + filter.getRoleName().replaceAll("'", "") + '%'));
        }
        if (StringUtils.isNotEmpty(filter.getRoleKey())) {
            builder.or(QSysRole.sysRole.roleKey.like('%' + filter.getRoleKey().replaceAll("'", "") + '%'));
        }
        if (filter.getStatus() != null) {
            builder.or(QSysRole.sysRole.status.intValue().eq(filter.getStatus()));
        }
        if (StringUtils.isNotEmpty(filter.getBeginTime()) && StringUtils.isNotEmpty(filter.getEndTime())) {
            builder.or(QSysRole.sysRole.createTime.between(DateUtils.parse(filter.getBeginTime() + " 00:00:00"), DateUtils.parse(filter.getEndTime() + " 23:59:59")));
        }

        jpaQuery.where(builder)
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize());

        jpaQuery.orderBy(QSysRole.sysRole.sortNum.asc());


        //拿到分页结果
        QueryResults<Tuple> qs = jpaQuery.fetchResults();

        pageData.setTotal(qs.getTotal());

        List<SysRoleVo> data = convertTuple(qs);
        pageData.setData(data);

        List<Long> roleIds = data.stream().map(SysRoleVo::getId).collect(Collectors.toList());
        // 查询角色对应的菜单ID组
        Map<Long, List<Long>> roleIdToMenuIdsMap = roleMenuRepository.findByRoleIdIn(roleIds).stream().collect(Collectors.groupingBy(SysRoleMenu::getRoleId, Collectors.mapping(SysRoleMenu::getMenuId, Collectors.toList())));

        data.forEach(vo -> {
            Long id = vo.getId();
            vo.setMenuIds(roleIdToMenuIdsMap.get(id));
        });

        return pageData;
    }

//    public PageData<SysRoleVo> selectAllotRoleList(SysRoleVo filter) {
//
//        // 数据权限
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        List<Long> dataScopes = loginUser.getDataScopes();
//        SysUserVo user = loginUser.getUser();
//        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());
//
//        PageData<SysRoleVo> pageData = new PageData<>();
//
//        Integer pageNumber = filter.getPageNumber() - 1;
//        //页面条数
//        Integer pageSize = filter.getPageSize();
//        Pageable pageable = PageRequest.of(pageNumber, pageSize);
//
//
//        JPAQuery<Tuple> jpaQuery = queryFactory.select(QSysRole.sysRole, QSysUser.sysUser.userName).from(QSysRole.sysRole);
//        jpaQuery.leftJoin(QSysUserAllotRole.sysUserAllotRole).on(QSysRole.sysRole.id.longValue().eq(QSysUserAllotRole.sysUserAllotRole.roleId.longValue()));
//        jpaQuery.where(QSysUserAllotRole.sysUserAllotRole.userId.eq()).
//
//        BooleanBuilder builder = new BooleanBuilder();
//
//        if (StringUtils.isNotEmpty(filter.getRoleName())) {
//            builder.or(QSysRole.sysRole.roleName.like('%' + filter.getRoleName().replaceAll("'", "") + '%'));
//        }
//        if (StringUtils.isNotEmpty(filter.getRoleKey())) {
//            builder.or(QSysRole.sysRole.roleKey.like('%' + filter.getRoleKey().replaceAll("'", "") + '%'));
//        }
//        if (filter.getStatus() != null) {
//            builder.or(QSysRole.sysRole.status.intValue().eq(filter.getStatus()));
//        }
//        if (StringUtils.isNotEmpty(filter.getBeginTime()) && StringUtils.isNotEmpty(filter.getEndTime())) {
//            builder.or(QSysRole.sysRole.createTime.between(DateUtils.parse(filter.getBeginTime() + " 00:00:00"), DateUtils.parse(filter.getEndTime() + " 23:59:59")));
//        }
//
//        jpaQuery.where(builder)
//                .offset(pageable.getOffset())
//                .limit(pageable.getPageSize());
//
//        jpaQuery.orderBy(QSysRole.sysRole.sortNum.asc());
//
//
//        //拿到分页结果
//        QueryResults<Tuple> qs = jpaQuery.fetchResults();
//
//        pageData.setTotal(qs.getTotal());
//
//        List<SysRoleVo> data = convertTuple(qs);
//        pageData.setData(data);
//
//        List<Long> roleIds = data.stream().map(SysRoleVo::getId).collect(Collectors.toList());
//        // 查询角色对应的菜单ID组
//        Map<Long, List<Long>> roleIdToMenuIdsMap = roleMenuRepository.findByRoleIdIn(roleIds).stream().collect(Collectors.groupingBy(SysRoleMenu::getRoleId, Collectors.mapping(SysRoleMenu::getMenuId, Collectors.toList())));
//
//        data.forEach(vo -> {
//            Long id = vo.getId();
//            vo.setMenuIds(roleIdToMenuIdsMap.get(id));
//        });
//
//        return pageData;
//    }

    private List<SysRoleVo> convertTuple(QueryResults<Tuple> jpaQuery) {
        List<Tuple> list = jpaQuery.getResults();
        List<SysRoleVo> listVo = new ArrayList<SysRoleVo>();

        for (Tuple tuple : list) {
            SysRole entity = tuple.get(0, SysRole.class);
            BeanCopier beanCopier = BeanCopier.create(SysRole.class, SysRoleVo.class, false);
            SysRoleVo vo = new SysRoleVo();
            beanCopier.copy(entity, vo, null);

            String userName = tuple.get(1, String.class);
            vo.setCreateUser(userName);
            listVo.add(vo);
        }

        return listVo;
    }

    /**
     * 数据转换
     *
     * @param entity
     * @return
     */
    private SysRoleVo convert(SysRole entity) {
        BeanCopier beanCopier = BeanCopier.create(SysRole.class, SysRoleVo.class, false);
        SysRoleVo vo = new SysRoleVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<Long> roleIdList = userRoleRepository.findByUserId(userId).stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        if (roleIdList.isEmpty()) {
            return Collections.emptySet();
        }

        List<SysRole> roleList = roleRepository.findByIdIn(roleIdList);
        Set<String> permsSet = new HashSet<>();
        for (SysRole role : roleList) {
            if (StringUtils.isNotNull(role)) {
                permsSet.addAll(Arrays.asList(role.getRoleKey().trim().split(",")));
            }
        }

        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    public List<SysRoleVo> selectRoleAll() {
        return roleRepository.findAll().stream().map(this::convert).collect(Collectors.toList());
    }



    /**
     * 根据用户ID获取角色
     *
     * @return 角色列表
     */
    public List<SysRoleVo> selectUserRole(Long userId) {
        List<Long> roleId = userRoleRepository.findByUserId(userId).stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        return roleRepository.findByIdIn(roleId).stream().map(this::convert).collect(Collectors.toList());
    }


    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    public List<Long> selectRoleListByUserId(Long userId) {
        return userRoleRepository.findByUserId(userId).stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    public SysRoleVo selectRoleById(Long roleId) {
        Optional<SysRole> optional = roleRepository.findById(roleId);
        if (!optional.isPresent()) {
            return null;
        }

        SysRole entity = optional.get();
        return this.convert(entity);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    public String checkRoleNameUnique(SysRoleVo role) {
        Long roleId = StringUtils.isNull(role.getId()) ? -1L : role.getId();
        SysRole info = roleRepository.findByRoleName(role.getRoleName());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != roleId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 校验角色权限是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    public String checkRoleKeyUnique(SysRoleVo role) {
        Long roleId = StringUtils.isNull(role.getId()) ? -1L : role.getId();
        SysRole info = roleRepository.findByRoleKey(role.getRoleKey());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != roleId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    public void checkRoleAllowed(SysRoleVo role) {
        if (StringUtils.isNotNull(role.getId()) && role.isAdmin()) {
            throw new CustomException("不允许操作超级管理员角色");
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int countUserRoleByRoleId(Long roleId) {
        return userRoleRepository.countByRoleId(roleId);
    }

    public List<SysUserRole> findUserRoleByRoleId(Long roleId) {
        return userRoleRepository.findByRoleId(roleId);
    }

    /**
     * 新增角色信息
     *
     * @param vo 角色信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveRole(SysRoleVo vo) {
        BeanCopier beanCopier = BeanCopier.create(SysRoleVo.class, SysRole.class, false);
        SysRole entity = new SysRole();
        beanCopier.copy(vo, entity, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        entity.setCreateUserId(user.getId());
        entity.setCreateTime(new Date());
        // 新增角色信息
        entity = roleRepository.save(entity);

        // 新增角色菜单信息
        //this.saveRoleMenu(entity.getId(), vo.getMenuIds());
    }

    /**
     * 修改角色信息
     *
     * @param vo 角色信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(SysRoleVo vo) {
        Optional<SysRole> optional = roleRepository.findById(vo.getId());
        if (!optional.isPresent()) {
            throw new CustomException("角色数据不存在");
        }

        SysRole entity = optional.get();

        BeanCopier beanCopier = BeanCopier.create(SysRoleVo.class, SysRole.class, false);
        beanCopier.copy(vo, entity, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());

        // 修改角色信息
        entity = roleRepository.save(entity);

        // 删除角色与菜单关联
        //roleMenuRepository.deleteByRoleId(vo.getId());

        // 新增角色菜单信息
        //this.saveRoleMenu(entity.getId(), vo.getMenuIds());
    }

    /**
     * 修改角色状态
     *
     * @param vo 角色信息
     * @return 结果
     */
    public void updateRoleStatus(SysRoleVo vo) {
        Optional<SysRole> optional = roleRepository.findById(vo.getId());
        if (!optional.isPresent()) {
            throw new CustomException("角色数据不存在");
        }

        SysRole entity = optional.get();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());
        entity.setStatus(vo.getStatus());

        roleRepository.save(entity);
    }

    /**
     * 修改数据权限信息
     *
     * @param vo 角色信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void authDataScope(SysRoleVo vo) {
        // 删除角色与部门关联
        roleDeptRepository.deleteByRoleId(vo.getId());

        // 新增角色和部门信息（数据权限）
        this.saveRoleDept(vo.getId(), vo.getDeptIds());
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param roleId  角色ID
     * @param deptIds 部门ID组（数据权限）
     */
    public void saveRoleDept(Long roleId, List<Long> deptIds) {
        List<SysRoleDept> data = deptIds.stream().map(deptId -> {
            SysRoleDept rd = new SysRoleDept();
            rd.setRoleId(roleId);
            rd.setDeptId(deptId);
            return rd;
        }).collect(Collectors.toList());

        roleDeptRepository.saveAll(data);
    }



    /**
     * 新增角色菜单信息
     *
     * @param roleId  角色Id
     * @param menuIds 菜单ID组
     */
    public void saveRoleMenu(Long roleId, List<Long> menuIds) {
        List<SysRoleMenu> data = menuIds.stream().map(menuId -> {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(roleId);
            rm.setMenuId(menuId);
            return rm;
        }).collect(Collectors.toList());

        roleMenuRepository.saveAll(data);
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public void deleteRoleById(Long roleId) {
        roleRepository.deleteById(roleId);
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     */
    public void deleteRoleByIds(List<Long> roleIds) {
        for (Long roleId : roleIds) {
            this.checkRoleAllowed(new SysRoleVo(roleId));
            if (this.countUserRoleByRoleId(roleId) > 0) {
                List<SysUserRole> list = this.findUserRoleByRoleId(roleId);
                //删除用户角色关系
                userRoleRepository.deleteAll(list);
                //throw new CustomException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }

        List<SysRole> entities = roleRepository.findByIdIn(roleIds);
        //改为硬删除
        roleRepository.deleteAll(entities);

        // 删除角色所关联的菜单
        roleMenuRepository.deleteByRoleIdIn(roleIds);
    }
    
    /**
     * 新增/修改角色菜单权限
     *
     * @param roleId  角色Id
     * @param menuIds 菜单ID组
     */
    public void roleMenu(Long roleId, List<Long> menuIds) {
        List<SysRoleMenu> data = menuIds.stream().map(menuId -> {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(roleId);
            rm.setMenuId(menuId);
            return rm;
        }).collect(Collectors.toList());
        
        // 删除角色与菜单关联
        roleMenuRepository.deleteByRoleId(roleId);

        // 新增角色菜单信息
        if(data == null || data.isEmpty()){
        	return;
        }
        roleMenuRepository.saveAll(data);
    }
}
