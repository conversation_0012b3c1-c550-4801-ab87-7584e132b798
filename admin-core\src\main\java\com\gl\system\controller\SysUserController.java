package com.gl.system.controller;

import com.alibaba.excel.EasyExcel;
import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.interceptor.annotation.RepeatSubmit;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.domain.PageData;
import com.gl.framework.web.response.Result;
import com.gl.system.service.SysRoleService;
import com.gl.system.service.SysUserAllotRoleService;
import com.gl.system.service.SysUserService;
import com.gl.system.vo.SysRoleVo;
import com.gl.system.vo.SysUserAllotRoleVo;
import com.gl.system.vo.SysUserVo;
import com.gl.system.vo.export.UserExportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/system/user")
public class SysUserController {

    @Autowired
    private SysUserService userService;

    @Autowired
    private SysRoleService roleService;
    @Autowired
    private SysUserAllotRoleService userAllotRoleService;

    /**
     * 用户分页列表
     *
     * @param filter
     * @return
     */
    @GetMapping("/list")
    @PreAuthorize("@ps.hasPermi('system:user:list')")
    public PageData<SysUserVo> list(SysUserVo filter) {
        return userService.selectUserList(filter);
    }

    /**
     * 获取用户详细信息
     *
     * @param userId
     * @return
     */
    @GetMapping(value = {"/", "/{userId}"})
    @PreAuthorize("@ps.hasPermi('system:user:query')")
    public Map<String, Object> getInfo(@PathVariable(required = false) Long userId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin(loginUser.getUser().getId());

        Map<String, Object> map = new HashMap<String, Object>();
        List<SysRoleVo> roles = roleService.selectRoleAll();

        if(userId != null){
        	map.put("user", userService.selectUserById(userId));
        	// 登录用户可分配职能
            Long loginUserId = loginUser.getUser().getId();
            List<SysUserAllotRoleVo> loginUserPosts = userAllotRoleService.selectAllotRole(loginUserId);

        	List<SysRoleVo> list = roleService.selectUserRole(userId);
        	// 职能
            map.put("userRoles", list == null ? new ArrayList<Long>() : list);
            List<SysUserAllotRoleVo> userPosts =userAllotRoleService.selectAllotRole(userId);
            // 可分配职能
            map.put("userPosts", userPosts);

            // 职能下拉框 = 职能 + 登录用户可分配职能
            List<SysUserAllotRoleVo> userAllotRoles = new ArrayList<>();
            if (list != null) {
                list.forEach(role ->{
                    SysUserAllotRoleVo vo = new SysUserAllotRoleVo();
                    vo.setId(role.getId());
                    vo.setRoleId(role.getId());
                    vo.setRoleName(role.getRoleName());
                    userAllotRoles.add(vo);
                });
            }
            userAllotRoles.addAll(loginUserPosts);
            map.put("userAallotRoles", userAllotRoles.stream().distinct().collect(Collectors.toList()));


            // 可分配职能下拉框 = 编辑用户可分配职能 + 登录用户可分配职能
            List<SysUserAllotRoleVo> allotRoles =userAllotRoleService.selectAllotRole(userId);
            allotRoles.addAll(loginUserPosts);
            map.put("allotRoles", allotRoles.stream().distinct().collect(Collectors.toList()));
        }else{
        	// 新增用户时，当前登录用户拥有的可分配职能
        	Long loginUserId = loginUser.getUser().getId();
        	List<SysUserAllotRoleVo>allotRoles =userAllotRoleService.selectAllotRole(loginUserId);
            map.put("allotRoles", allotRoles);
            map.put("userAallotRoles", allotRoles);
        }

        if (isSuperAdmin){
        	//ADMIN拥有所有可分配职能
            map.put("allotRoles", SecurityUtils.isSuperAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
            map.put("userAallotRoles", SecurityUtils.isSuperAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        }

        return map;
    }

    /**
     * 新增用户
     *
     * @param vo
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @PreAuthorize("@ps.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT, businessTypeName = "新增用户")
    public Result add(@Validated @RequestBody SysUserVo vo) {

        String loginName = vo.getLoginName();

        /*LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();*/

        if (!userService.checkLoginNameUnique(vo.getLoginName())) {
            return Result.fail("新增用户'" + loginName + "'失败，登录账号已经存在");
        }
        if (StringUtils.isNotEmpty(vo.getEmail()) && !userService.checkEmailUnique(vo.getEmail())) {
            return Result.fail("新增用户'" + loginName + "'失败，邮箱已经存在");
        }
        if (!userService.checkPhoneUnique(vo.getPhone())) {
            return Result.fail("新增用户'" + loginName + "'失败，手机号已经存在");
        }

        vo.setPassword(SecurityUtils.encryptPassword(vo.getPassword()));
        userService.saveUser(vo);
        return Result.success();
    }

    /**
     * 修改用户
     *
     * @param vo
     * @return
     */
    @PutMapping
    @PreAuthorize("@ps.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE, businessTypeName = "修改用户")
    public Result edit(@Validated @RequestBody SysUserVo vo, HttpServletRequest request) {

        log.info(vo.toString());

        String loginName = vo.getLoginName();

        if (SecurityUtils.isSuperAdmin(vo.getId())) {
            return Result.fail("修改用户'" + loginName + "'失败，此用户不可修改");
        }
        if (!userService.checkLoginNameUniqueNotSelf(vo.getId(), vo.getLoginName())) {
            return Result.fail("修改用户'" + loginName + "'失败，登录账号已经存在");
        }
        if (StringUtils.isNotEmpty(vo.getEmail()) && !userService.checkEmailUniqueNotSelf(vo.getId(), vo.getEmail())) {
            return Result.fail("修改用户'" + loginName + "'失败，邮箱已经存在");
        }
        if (!userService.checkPhoneUniqueNotSelf(vo.getId(), vo.getPhone())) {
            return Result.fail("修改用户'" + loginName + "'失败，手机号已经存在");
        }

        userService.updateUser(vo, request);
        return Result.success();
    }

    /**
     * 删除用户
     *
     * @param userIds
     * @return
     */
    @DeleteMapping("/{userIds}")
    @PreAuthorize("@ps.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE, businessTypeName = "删除用户")
    public Result remove(@PathVariable List<Long> userIds) {
        userService.deleteUserByIds(userIds);
        return Result.success();
    }

    /**
     * 重置密码
     *
     * @param vo
     * @return
     */
    @PutMapping("/reset_pwd")
    @PreAuthorize("@ps.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.RESET_PASSWORD, businessTypeName = "重置密码")
    public Result resetPwd(@RequestBody SysUserVo vo) {
        if (vo == null || StringUtils.isBlank(vo.getPassword())) {
            return Result.fail("password is empty");
        }
        userService.checkUserAllowed(vo);
        userService.resetPwd(vo);
        return Result.success();
    }

    /**
     * 状态修改
     *
     * @param vo
     * @return
     */
    @PutMapping("/status")
    @PreAuthorize("@ps.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE, businessTypeName = "状态修改")
    public Result changeStatus(@RequestBody SysUserVo vo) {
        userService.checkUserAllowed(vo);
        userService.updateUserStatus(vo);
        return Result.success();
    }

    @GetMapping(value = {"/login/info"})
    public Map<String, Object> getLoginInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        Map<String, Object> map = new HashMap<>();
        map.put("id", user.getId());
        return map;
    }


    /**
     * 导出
     *
     * @param filter
     * @param response
     * @throws IOException
     */
    @GetMapping("/export")
    @PreAuthorize("@ps.hasPermi('system:user:export')")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT, businessTypeName = "导出")
    public void export(SysUserVo filter, HttpServletResponse response) throws IOException {
        // 查询数据
        PageData<SysUserVo> pageData = userService.selectUserList(filter);
        List<SysUserVo> data = pageData.getData();
        List<UserExportVo> exportData = data.stream().map(vo -> {
            UserExportVo exportVo = new UserExportVo();
            exportVo.setLoginName(vo.getLoginName());
            exportVo.setUserName(vo.getUserName());
            exportVo.setDeptName(vo.getDept().getDeptName());
            exportVo.setEmail(vo.getEmail());
            exportVo.setPhone(vo.getPhone());
            Integer gender = vo.getGender();
            exportVo.setGender(gender == 0 ? "男" : gender == 1 ? "女" : "未知");
            Integer status = vo.getStatus();
            exportVo.setStatus(status == 1 ? "正常" : "停用");
            exportVo.setCreator(vo.getCreator());
            exportVo.setCreateTime(vo.getCreateTime());
            return exportVo;
        }).collect(Collectors.toList());

        EasyExcel.write(response.getOutputStream(), UserExportVo.class).sheet("用户信息").doWrite(exportData);
    }

    /**
	 * 保存用户数据权限设置(部门)
	 *
	 * @param id
	 *            用户id
	 * @param departmentIds
	 *            部门id集
	 * <AUTHOR>
	 * @date 2021年11月9日
	 */
	@RequestMapping(value = "/auth/{id}", method = RequestMethod.POST)
	@ResponseBody
	public Result saveUserDepartment(@PathVariable Long id, @RequestBody Set<Long> departmentIds) {
		userService.saveUserDepartment(id, departmentIds);
		return Result.success();
	}

    /**
     * 离职继承
     */
    @RequestMapping(value = "/leave/{leaveUserId}/{inheritUserId}", method = RequestMethod.POST)
    @ResponseBody
    @Log(title = "用户管理", businessType = BusinessType.OTHER, businessTypeName = "离职继承")
    public Result leave(@PathVariable Long leaveUserId,@PathVariable Long inheritUserId){
        userService.leave(leaveUserId,inheritUserId);
        return Result.success();
    }
}
