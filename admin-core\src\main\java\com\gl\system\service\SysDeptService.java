package com.gl.system.service;

import com.gl.framework.common.enums.StatusEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.system.entity.QSysDept;
import com.gl.system.entity.QSysPositionDept;
import com.gl.system.entity.SysDept;
import com.gl.system.entity.SysUser;
import com.gl.system.repository.SysDeptRepository;
import com.gl.system.repository.SysUserRepository;
import com.gl.system.vo.SysDeptTreeVo;
import com.gl.system.vo.SysDeptVo;
import com.gl.system.vo.SysUserVo;
import com.gl.framework.web.domain.TreeSelect;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 */
@Service
public class SysDeptService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SysDeptRepository deptRepository;

    @Autowired
    private SysUserRepository userRepository;

    @Autowired
    JPAQueryFactory queryFactory;

    /**
     * 查询部门管理数据
     *
     * @param filter 部门信息
     * @return 部门信息集合
     */
    public List<SysDeptVo> selectDeptList(SysDeptVo filter) {
        // 数据权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        List<Long> dataScopes = loginUser.getDataScopes();

        boolean admin = SecurityUtils.isSuperAdmin(user.getId());
        // 将用户自己所属机构增加到可选机构列表中 20220401
        if(user.getDeptId() != null){
        	dataScopes.add(user.getDeptId());
        }
        
        if (!admin && StringUtils.isEmpty(dataScopes)) {
            return Collections.emptyList();
        }

        JPAQuery jpaQuery = queryFactory.select(QSysDept.sysDept).from(QSysDept.sysDept);

        BooleanBuilder builder = new BooleanBuilder();
        if (filter != null) {
            if (filter.getParentId() != null) {
                builder.and(QSysDept.sysDept.parentId.longValue().eq(filter.getParentId()));
            }
            if (StringUtils.isNotBlank(filter.getDeptName())) {
                builder.and(QSysDept.sysDept.deptName.like("%"+filter.getDeptName()+"%"));
            }
            if (filter.getStatus() != null) {
                builder.and(QSysDept.sysDept.status.intValue().eq(filter.getStatus()));
            }
        }

        if (!admin) {
            builder.and(QSysDept.sysDept.id.in(dataScopes));
        }
        
        // 只查询未删除的
        builder.and(QSysDept.sysDept.deleted.eq(0));

        jpaQuery.where(builder).orderBy(QSysDept.sysDept.id.asc(), QSysDept.sysDept.sortNum.asc());

        QueryResults<SysDept> qs = jpaQuery.fetchResults();
        List<SysDept> list = qs.getResults();

        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        List<SysDeptVo> listVo = list.stream().map(this::convert).collect(Collectors.toList());
        listVo.forEach(a -> {
            if (a.getHeadUserId() != null) {
                Optional<SysUser> optional = userRepository.findById(a.getHeadUserId());
                if (optional.isPresent()) {
                    SysUser sysUser = optional.get();
                    a.setHeadUser(sysUser.getUserName());
                }
            }
        });

        return listVo;
    }

    /**
     * 按部门类型查询显示部门
     * @param filter
     * @return
     */
    public List<SysDeptVo> selectDeptList1(Integer belong) {
        // 数据权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        List<Long> dataScopes = loginUser.getDataScopes();

        boolean admin = SecurityUtils.isSuperAdmin(user.getId());
        // 将用户自己所属机构增加到可选机构列表中 20220401
        if(user.getDeptId() != null){
            dataScopes.add(user.getDeptId());
        }

        if (!admin && StringUtils.isEmpty(dataScopes)) {
            return Collections.emptyList();
        }

        JPAQuery jpaQuery = queryFactory.select(QSysDept.sysDept).from(QSysDept.sysDept);

        BooleanBuilder builder = new BooleanBuilder();
        if (belong != null) {
                builder.and(QSysDept.sysDept.type.eq(belong));
        }

        if (!admin) {
            builder.and(QSysDept.sysDept.id.in(dataScopes));
        }
        
        // 只查询未删除的
        builder.and(QSysDept.sysDept.deleted.eq(0));

        jpaQuery.where(builder).orderBy(QSysDept.sysDept.type.asc(),QSysDept.sysDept.id.asc());
        QueryResults<SysDept> qs = jpaQuery.fetchResults();
        List<SysDept> list = qs.getResults();

        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        List<SysDeptVo> listVo = list.stream().map(this::convert).collect(Collectors.toList());
        listVo.forEach(a -> {
            if (a.getHeadUserId() != null) {
                Optional<SysUser> optional = userRepository.findById(a.getHeadUserId());
                if (optional.isPresent()) {
                    SysUser sysUser = optional.get();
                    a.setHeadUser(sysUser.getUserName());
                }
            }
        });

        return listVo;
    }

    /**
     * 构造部门树状结构，在项目管理查询时使用
     */
    public List<SysDeptTreeVo> listTreeDepts(SysDeptVo vo) {

        List<SysDeptVo> listVo = selectDeptList(vo);

        List<SysDeptTreeVo> rootList = new ArrayList<>();
        List<SysDeptTreeVo> leafList = new ArrayList<>();

        for (SysDeptVo vos : listVo) {
            SysDeptTreeVo treeVo = new SysDeptTreeVo();
            treeVo.setId(vos.getId());
            treeVo.setParentId(vos.getParentId());
            treeVo.setLabel(vos.getDeptName());
            treeVo.setChildren(new ArrayList<>());

            if (treeVo.getParentId().longValue() == 0) {
                rootList.add(treeVo);
            } else {
                leafList.add(treeVo);
            }

        }

        return getTree(rootList, leafList);
    }


    private List<SysDeptTreeVo> getTree(List<SysDeptTreeVo> rootList, List<SysDeptTreeVo> leafList) {
        if (leafList != null && !leafList.isEmpty()) {
            //声明一个map，用来过滤已操作过的数据
            Map<Long, Long> map = Maps.newHashMapWithExpectedSize(leafList.size());
            if(rootList.size() > 0){
                rootList.forEach(beanTree -> getChild(beanTree, map, leafList));
            }else {
                List<Long> longs = new ArrayList<>();
                leafList.forEach(beanTree -> getChilds(beanTree, longs, leafList));
                for (Long aLong : longs) {
                    leafList.removeIf(vo -> aLong.equals(vo.getId()));
                }
                return leafList.stream().distinct().collect(Collectors.toList());
            }
            return rootList;
        }
        return null;
    }

    private void getChilds(SysDeptTreeVo treeDto, List<Long> longs, List<SysDeptTreeVo> leafList) {
        List<SysDeptTreeVo> childList = Lists.newArrayList();
        for (SysDeptTreeVo vo : leafList) {
            if(vo.getParentId().equals(treeDto.getId())){
                childList.add(vo);
                longs.add(vo.getId());
                treeDto.setChildren(childList);//子数据集
            }
        }
    }

    private void getChild(SysDeptTreeVo treeDto, Map<Long, Long> map, List<SysDeptTreeVo> leafList) {
        List<SysDeptTreeVo> childList = Lists.newArrayList();
        leafList.stream()
                .filter(c -> !map.containsKey(c.getId()))
                .filter(c -> c.getParentId().equals(treeDto.getId()))
                .forEach(c -> {
                    map.put(c.getId(), c.getParentId());
                    getChild(c, map, leafList);
                    childList.add(c);
                });
        treeDto.setChildren(childList);//子数据集
    }

    /**
     * 数据转换
     *
     * @param entity
     * @return
     */
    private SysDeptVo convert(SysDept entity) {
        BeanCopier beanCopier = BeanCopier.create(SysDept.class, SysDeptVo.class, false);
        SysDeptVo vo = new SysDeptVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    public List<SysDeptVo> selectAllDept() {
        return deptRepository.findAll().stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDeptVo> buildDeptTree(List<SysDeptVo> depts) {
        List<SysDeptVo> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (SysDeptVo dept : depts) {
            tempList.add(dept.getId());
        }
        for (Iterator<SysDeptVo> iterator = depts.iterator(); iterator.hasNext(); ) {
            SysDeptVo dept = (SysDeptVo) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDeptVo> depts) {
        List<SysDeptVo> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Long> selectDeptListByRoleId(Long roleId) {
        return deptRepository.selectDeptListByRoleId(roleId).stream().map(SysDept::getId).collect(Collectors.toList());
    }
    
    /**
     * 根据用户ID查询部门树信息
     *
     * @param userId 用户ID
     * @return 选中部门列表
     */
    public List<Long> selectDeptListByUserId(Long userId) {
        return deptRepository.selectDeptListByUserId(userId).stream().map(SysDept::getId).collect(Collectors.toList());
    }

    /**
     * 根据岗位ID查询部门树信息
     *
     * @param positionId 岗位ID
     * @return 选中部门列表
     */
    public List<Long> selectDeptListByPositionId(Long positionId) {

        JPAQuery<Long> jpaQuery = queryFactory.selectDistinct(QSysPositionDept.sysPositionDept.deptId).from(QSysPositionDept.sysPositionDept)
                .where(QSysPositionDept.sysPositionDept.positionId.longValue().eq(positionId));

        QueryResults<Long> qs = jpaQuery.fetchResults();
        if (qs.isEmpty()) {
            return Collections.emptyList();
        }

        return qs.getResults();
    }


    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDeptVo selectDeptById(Long deptId) {
        Optional<SysDept> optional = deptRepository.findById(deptId);
        if (!optional.isPresent()) {
            throw new CustomException("部门数据不存在");
        }
        SysDept entity = optional.get();
        Optional<SysDept> parentOptional = deptRepository.findById(entity.getParentId());
        if (parentOptional.isPresent()) {
            entity.setParentName(parentOptional.get().getDeptName());
        }
        return this.convert(entity);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId) {
        final String SQL = "SELECT COUNT(*) FROM sys_dept WHERE `status` = 0 AND FIND_IN_SET(?, ancestors) AND deleted=0";
        return jdbcTemplate.queryForObject(SQL, Integer.class, deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId) {
        int result = deptRepository.countByParentId(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId) {
        int result = userRepository.countByDeptId(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @return 结果
     */
    public String checkDeptNameUnique(SysDeptVo vo) {
        Long deptId = StringUtils.isNull(vo.getId()) ? -1L : vo.getId();
        SysDept dept = deptRepository.findByDeptNameAndParentIdAndDeleted(vo.getDeptName(), vo.getParentId(),0);
        if (StringUtils.isNotNull(dept) && dept.getId().longValue() != deptId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 新增部门信息
     *
     * @param vo 部门信息
     * @return 结果
     */
    public void saveDept(SysDeptVo vo) {
        Optional<SysDept> optional = deptRepository.findById(vo.getParentId());
        if (!optional.isPresent()) {
            throw new CustomException("部门数据不存在");
        }
        SysDept parentDept = optional.get();

        // 如果父节点不为正常状态,则不允许新增子节点
        if (StatusEnum.NORMAL.value() != parentDept.getStatus()) {
            throw new CustomException("部门停用，不允许新增");
        }
        vo.setAncestors(parentDept.getAncestors() + "," + vo.getParentId());

        BeanCopier beanCopier = BeanCopier.create(SysDeptVo.class, SysDept.class, false);
        SysDept entity = new SysDept();
        beanCopier.copy(vo, entity, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        entity.setStatus(StatusEnum.NORMAL.value());
        entity.setDeleted(0);
        entity.setCreateUserId(user.getId());
        entity.setCreateTime(new Date());

        deptRepository.save(entity);
    }

    /**
     * 修改部门信息
     *
     * @param vo 部门信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDept(SysDeptVo vo) {
        Optional<SysDept> newParentOptional = deptRepository.findById(vo.getParentId());

        Optional<SysDept> oldDeptOptional = deptRepository.findById(vo.getId());
        if (!oldDeptOptional.isPresent()) {
            throw new CustomException("部门数据不存在");
        }
        SysDept oldDept = oldDeptOptional.get();

        if (newParentOptional.isPresent() && StringUtils.isNotNull(oldDept)) {
            SysDept newParentDept = newParentOptional.get();
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getId();
            String oldAncestors = oldDept.getAncestors();
            vo.setAncestors(newAncestors);
            updateDeptChildren(vo.getId(), newAncestors, oldAncestors);
        }

        BeanCopier beanCopier = BeanCopier.create(SysDeptVo.class, SysDept.class, false);
        beanCopier.copy(vo, oldDept, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        oldDept.setUpdateUserId(user.getId());
        oldDept.setUpdateTime(new Date());
        oldDept = deptRepository.save(oldDept);

        // 如果该部门是启用状态，则启用该部门的所有上级部门
        if (StatusEnum.NORMAL.value() == vo.getStatus()) {
            this.updateParentDeptStatus(oldDept);
        }
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatus(SysDept dept) {
        String ancestors = dept.getAncestors();
        List<Long> parentIds = Arrays.asList(ancestors.split(",")).stream().map(Long::new).collect(Collectors.toList());
        List<SysDept> parentDeptList = deptRepository.findByParentIdIn(parentIds);

        parentDeptList.forEach(entity -> {
            entity.setStatus(dept.getStatus());
            entity.setUpdateUserId(dept.getUpdateUserId());
            entity.setUpdateTime(dept.getUpdateTime());
        });

        deptRepository.saveAll(parentDeptList);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        List<SysDept> children = deptRepository.selectChildrenDeptById(deptId);

        for (SysDept child : children) {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }

        deptRepository.saveAll(children);
    }

    /**
     * 删除部门
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public void deleteDeptById(Long deptId) {
        Optional<SysDept> optional = deptRepository.findById(deptId);
        if (!optional.isPresent()) {
            throw new CustomException("部门数据不存在");
        }

        //deptRepository.deleteById(deptId);
        // 软删除
        SysDept entity = optional.get();
        entity.setDeleted(1);
        deptRepository.save(entity);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDeptVo> list, SysDeptVo t) {
        // 得到子节点列表
        List<SysDeptVo> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDeptVo tChild : childList) {
            if (hasChild(list, tChild)) {
                // 判断是否有子节点
                Iterator<SysDeptVo> it = childList.iterator();
                while (it.hasNext()) {
                    SysDeptVo n = (SysDeptVo) it.next();
                    recursionFn(list, n);
                }
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDeptVo> getChildList(List<SysDeptVo> list, SysDeptVo t) {
        List<SysDeptVo> tlist = new ArrayList<>();
        Iterator<SysDeptVo> it = list.iterator();
        while (it.hasNext()) {
            SysDeptVo n = (SysDeptVo) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDeptVo> list, SysDeptVo t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 查找全部子节点的ID
     *
     * @param id
     */
    public List<Long> findDeptAllChild(Long id) {
        String sql = "SELECT au.id \n" +
                "FROM (SELECT * FROM sys_dept WHERE parent_id IS NOT NULL) au,\n" +
                "     (SELECT @pid := " + id + ") pd \n " +
                "WHERE FIND_IN_SET(parent_id, @pid) > 0 \n" +
                "  AND @pid := CONCAT(@pid, ',', id)\n" +
                "UNION SELECT id FROM sys_dept WHERE id = " + id;

        return jdbcTemplate.queryForList(sql, Long.class);
    }

    /**
     * 部门权限用户下拉框
     * @param keyword
     * @return
     * <AUTHOR>
     * @date 2022年5月7日
     */
    public Result getUsers(String keyword) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        boolean admin = SecurityUtils.isSuperAdmin(user.getId());

        StringBuilder sql = new StringBuilder();
        sql.append("select id,user_name as userName, phone from sys_user where status = 1 and deleted = 0");
        
        if(StringUtils.isNotEmpty(keyword)){
        	sql.append(" and (user_name like '%"+keyword+"%' or phone like '%"+keyword+"%') ");
    	}
        
        if (admin) {
            List<Map<String, Object>> data = jdbcTemplate.queryForList(sql.toString());
            return Result.success(data);
        } else {
            if (dataScopes.isEmpty()) {
                return Result.success(Collections.emptyList());
            } else {
                sql.append(" and dept_id in " + SqlUtils.foreachIn(dataScopes.size()));
                List<Map<String, Object>> data = jdbcTemplate.queryForList(sql.toString(), dataScopes.toArray());
                return Result.success(data);
            }
        }
    }

}
