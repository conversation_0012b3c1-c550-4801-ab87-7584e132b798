package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAiClass is a Querydsl query type for AiClass
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAiClass extends EntityPathBase<AiClass> {

    private static final long serialVersionUID = -1211385761L;

    public static final QAiClass aiClass = new QAiClass("aiClass");

    public final StringPath content = createString("content");

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> orderIndex = createNumber("orderIndex", Integer.class);

    public final StringPath title = createString("title");

    public QAiClass(String variable) {
        super(AiClass.class, forVariable(variable));
    }

    public QAiClass(Path<? extends AiClass> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAiClass(PathMetadata metadata) {
        super(AiClass.class, metadata);
    }

}

