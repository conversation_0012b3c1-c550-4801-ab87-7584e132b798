package com.gl.aspectj.annotation;

import java.lang.annotation.*;

/**
 * 门店数据权限控制注解。
 * 适用于 Service 层方法。通过 AOP 在方法执行前统一校验当前登录用户
 * 是否具备访问/操作目标门店数据的权限。
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ShopPermission {

    /**
     * 是否为写操作（新增、修改、删除）。
     * false：只校验数据可见性（查询、详情等）；非管理员也可通过
     * true ：写操作，站点账号必须是门店管理员才能通过
     */
    boolean operate() default false;

    /**
     * 当前接口主要操作的业务实体类型。
     * 传入对应 Entity 的 Class，例如 Device.class、Shop.class、BroadcastPlan.class。
     * 不指定（默认为 Void.class）时按照类名/字段名和 checkDevice/checkShop 规则自动推断。
     */
    Class<?> entity() default Void.class;

    /**
     * 当接口直接以 Long / List<Long> 参数表示业务主键时（例如只有 id，无 DTO），
     * 且方法中同时存在多个 Long 类型参数导致无法准确识别时，可通过该属性显式指定
     * 参数名，以便进行精确的门店权限校验。
     * <p>
     * 示例：delete(Long planId, Long otherId)
     * 设置 idParam = "planId" 即可仅对 planId 进行校验。
     * <br/>
     * 若为空串，则保持旧逻辑，对方法中出现的所有 Long 参数按照 entity()/checkDevice()/checkShop
     * 的规则依次判定。
     */
    String idParam() default "";
}