package com.gl.service.opus.utils;


import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 *   @ Date：2024-03-09-14:20
 */
public class FileRenameUtils {

    // 获得命名
    public static String isVoiceName(char[] chars, Long number) {

        // 1.验证是否是汉字
        boolean checkcountname = checkcountname(String.valueOf(chars[0]));
        if (checkcountname) {
            return chars[0] + StringUtils.leftPad(number.toString(), 6, "0");
        }

        if (chars.length == 1) {

            // 2.验证是否是英文
            boolean englishStr = isEnglishStr(String.valueOf(chars[0]));
            if (englishStr) {
                return chars[0] + StringUtils.leftPad(number.toString(), 7, "0");
            }

            // 3验证是否是数字
            boolean digit = Character.isDigit(chars[0]);
            if (digit) {
                return chars[0] + StringUtils.leftPad(number.toString(), 7, "0");
            } else {
                return StringUtils.leftPad(number.toString(), 8, "0");
            }

        }

        String voiceName = "";
        if (chars.length >= 2) {
            for (int i = 0; i < 2; i++) {
                boolean englishStr = isEnglishStr(String.valueOf(chars[i]));
                // 2.验证是否是英文
                if (englishStr) {
                    voiceName = voiceName + chars[i];
                }
                // 3验证是否是数字
                boolean digit = Character.isDigit(chars[i]);
                if (digit) {
                    voiceName = voiceName + chars[i];
                }
            }
        }

        if (voiceName.length() == 0) {
            return StringUtils.leftPad(number.toString(), 8, "0");
        } else if (voiceName.length() == 1) {
            return voiceName + StringUtils.leftPad(number.toString(), 7, "0");
        } else if (voiceName.length() == 2) {
            return voiceName + StringUtils.leftPad(number.toString(), 6, "0");
        }

        return null;
    }

    public static String createVoiceName(String text, Integer number) {
        String cleanedText = cleanText(text);
        String beforeName = (number < 10) ? "0" + number : String.valueOf(number);
        String afterName = cleanedText.length() < 3 ? cleanedText : cleanedText.substring(0, 3);
        afterName = filterNonChineseEnglishNumbers(afterName);
        return beforeName + afterName;
    }

    public static String filterNonChineseEnglishNumbers(String input) {
        String regex = "[\\u4e00-\\u9fa5\\w\\d]+";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(input);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            result.append(matcher.group());
        }
        return String.valueOf(result);
    }

    public static String createWorkName(String text, Integer number) {
        String beforeName = (number < 10) ? "0" + number : String.valueOf(number);
        String afterName = text.length() < 4 ? text
                : text.length() < 5 ? text.substring(2, 4)
                        : text.length() > 5 ? text.substring(2, 5) : text.substring(0, 3);
        return beforeName + afterName;
    }

    public static String cleanText(String text) {
        String punctuationPattern = "[\\p{Punct}\\u3000\\u3001\\u3002\\uff0c\\uff0e\\uff0f\\uff1a\\uff1b\\uff1c\\uff1d\\uff1e\\uff1f\\uff01"
                +
                "\\u2018\\u2019\\u201c\\u201d\\u3014\\u3015\\u3008\\u3009\\u300a\\u300b\\u300c\\u300d\\u300e\\u300f\\u3010\\u3011"
                +
                "\\u3016\\u3017\\u3018\\u3019\\u301a\\u301b\\uff5e\\uffe5\\u2026\\u2014\\uff08\\uff09]";
        return Pattern.compile(punctuationPattern).matcher(text).replaceAll("");
    }

    public static boolean checkcountname(String countname) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(countname);
        if (m.find()) {
            return true;
        }
        return false;
    }

    // 是否是英文字符串
    public static boolean isEnglishStr(String charaString) {
        return charaString.matches("^[a-zA-Z]*");
    }
}
