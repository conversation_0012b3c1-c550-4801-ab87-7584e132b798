package com.gl.service.message.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 客服信息
 * @author: duanjinze
 * @date: 2022/11/11 17:02
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_base_service_message")
public class BaseServiceMessage {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Basic
    @Column(name = "user_id")
    private Long userId;

    /**
     * 内容
     */
    @Basic
    @Column(name = "content")
    private String content;

    /**
     * 时间
     */
    @Basic
    @Column(name = "message_time")
    private Date messageTime;
}
