package com.gl.service.websocket.entity;

import lombok.Data;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-02-01-11:26
 */
@Data
public class VoiceTransforMessage {
    private String frameBuffer;
    private String token;
    private Boolean isLastFrame;
    private String voiceName;
    private Long anchorId;
    private Integer uploadNum;
    private String bgm;
    private Integer bugRate;
    /**
     * 语调
     */
    private Integer pitchRate = 0;
    /**
     * 语速
     */
    private Integer speechRate;
    /**
     * 音量
     */
    private Integer volume;

    private Integer beforeDelay = 0;

    private Integer afterDelay = 0;

    private Double bgmCenterVolum = 1.0;
}
