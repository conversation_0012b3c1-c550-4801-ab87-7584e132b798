package com.gl.service.webhook.service;

import com.gl.service.device.repository.DeviceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class WebhookService {

    @Autowired
    private DeviceRepository deviceRepository;

    public void updateStatus(String sn, Integer status) {
        deviceRepository.updateStatusBySn(sn, status, new Date());
    }
}
