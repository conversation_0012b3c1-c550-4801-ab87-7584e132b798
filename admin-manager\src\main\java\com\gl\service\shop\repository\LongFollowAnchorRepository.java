package com.gl.service.shop.repository;


import com.gl.service.opus.entity.LongFollowAnchor;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface LongFollowAnchorRepository extends PagingAndSortingRepository<LongFollowAnchor,Long>, JpaSpecificationExecutor<LongFollowAnchor> {


	List<LongFollowAnchor> findByAnchorIdAndUserId(Long anchorId, Long id);
}
