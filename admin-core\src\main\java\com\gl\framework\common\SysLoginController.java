package com.gl.framework.common;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.security.LoginBody;
import com.gl.framework.security.LoginUser;
import com.gl.framework.security.service.SysLoginService;
import com.gl.system.service.SysMenuService;
import com.gl.system.vo.RouterVo;
import com.gl.system.vo.SysMenuVo;
import com.gl.system.vo.SysUserVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录验证控制器
 */
@RestController
public class SysLoginController {

    private static final Logger log = LoggerFactory.getLogger(SysLoginController.class);

    private SysLoginService loginService;

    @Autowired
    public void setSysLoginService(SysLoginService loginService) {
        this.loginService = loginService;
    }


    private SysMenuService menuService;

    @Autowired
    public void setSysMenuService(SysMenuService menuService) {
        this.menuService = menuService;
    }

    /**
     * 登录方法
     *
     * @param loginBody 登陆信息
     * @return 结果
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody LoginBody loginBody) {
        try {
            // 生成令牌
            String token = loginService.login(loginBody.getLoginName(), loginBody.getPassword(), loginBody.getCode(), loginBody.getUuid());
            Map<String, Object> map = new HashMap<String, Object>();
            map.put(Constants.TOKEN, token);
            return map;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("userinfo")
    public Map<String, Object> getInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        user.setPassword(StringUtils.EMPTY);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("user", user);
        map.put("roles", loginUser.getRoleKeys());
        map.put("permissions", loginUser.getPermissions());

        return map;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("routers")
    public List<RouterVo> getRouters() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Long userId = user.getId();
        List<SysMenuVo> menus = menuService.selectMenuTreeByUserId(userId);
        List<RouterVo> routers = menuService.buildMenus(menus);

        return routers;
    }
}
