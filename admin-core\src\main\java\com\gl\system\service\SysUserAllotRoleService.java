package com.gl.system.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.domain.PageData;
import com.gl.framework.web.response.Result;
import com.gl.system.entity.*;
import com.gl.system.repository.SysRoleDeptRepository;
import com.gl.system.repository.SysRoleMenuRepository;
import com.gl.system.repository.SysRoleRepository;
import com.gl.system.repository.SysUserRoleRepository;
import com.gl.system.vo.SysRoleVo;
import com.gl.system.vo.SysUserAllotRoleVo;
import com.gl.system.vo.SysUserVo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 */
@Service
public class SysUserAllotRoleService {

    @Autowired
    private SysRoleRepository roleRepository;
    @Autowired
    private SysUserRoleRepository userRoleRepository;
    @Autowired
    private SysRoleDeptRepository roleDeptRepository;
    @Autowired
    private SysRoleMenuRepository roleMenuRepository;
    @Autowired
    JPAQueryFactory queryFactory;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final String LIMIT = "LIMIT ? OFFSET ?";
    public Result selectRoleList() {
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        if (user == null) {
            return Result.fail("查询失败！请登录");
        }
        args.add(user.getId());
        //List<Long> dataScopes = loginUser.getDataScopes();
        boolean admin = SecurityUtils.isSuperAdmin(user.getId());
        if (!admin) {
            sql.append("SELECT a.role_id as roleId,b.role_name as roleName FROM `sys_user_allot_role` a LEFT JOIN sys_role b ON a.role_id=b.id WHERE a.user_id=?");
        }else {
            sql.append("SELECT id as roleId,role_name as roleName FROM sys_role WHERE `status`=1 and deleted=0");
            args.clear();
        }

        List<SysUserAllotRoleVo> data = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(SysUserAllotRoleVo.class),args.toArray());
        if(data.isEmpty()){
            return Result.success(Collections.emptyList());
        }
        PageData<SysUserAllotRoleVo> pageData = new PageData<>();
        pageData.setData(data);
        return Result.success(pageData);
    }

    public List<SysUserAllotRoleVo> selectAllotRole(Long userId) {

        String sql ="SELECT a.role_id as roleId,a.role_id as id ,b.role_name as roleName FROM `sys_user_allot_role` a LEFT JOIN sys_role b ON a.role_id=b.id WHERE a.user_id=? and a.role_id is not null and b.role_name is not null\n";
        List<SysUserAllotRoleVo> data = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SysUserAllotRoleVo.class),userId);
        return data;
    }

}
