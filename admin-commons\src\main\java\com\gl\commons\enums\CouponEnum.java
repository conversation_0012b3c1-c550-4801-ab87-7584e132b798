package com.gl.commons.enums;

public enum CouponEnum {
    //新用户
    NEW_USER(1,"首次登录新用户"),
    //指定用户
    ASSIGN_USER(2,"指定用户发放"),
    //满减
    FULL(3,"");

    private Integer type;

    private String value;

    CouponEnum(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public static String getName(int type) {
        for (CouponEnum couponEnum : CouponEnum.values()) {
            if (couponEnum.getType() == type) {
                return couponEnum.value;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getValue() {
        return value;
    }
}
