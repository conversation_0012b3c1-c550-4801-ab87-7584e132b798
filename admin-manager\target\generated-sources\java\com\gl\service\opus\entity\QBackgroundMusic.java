package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBackgroundMusic is a Querydsl query type for BackgroundMusic
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBackgroundMusic extends EntityPathBase<BackgroundMusic> {

    private static final long serialVersionUID = 562415814L;

    public static final QBackgroundMusic backgroundMusic = new QBackgroundMusic("backgroundMusic");

    public final NumberPath<Long> createId = createNumber("createId", Long.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> musicTime = createNumber("musicTime", Integer.class);

    public final StringPath musicUrl = createString("musicUrl");

    public final StringPath name = createString("name");

    public final NumberPath<Long> shopId = createNumber("shopId", Long.class);

    public final NumberPath<Long> typeId = createNumber("typeId", Long.class);

    public QBackgroundMusic(String variable) {
        super(BackgroundMusic.class, forVariable(variable));
    }

    public QBackgroundMusic(Path<? extends BackgroundMusic> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBackgroundMusic(PathMetadata metadata) {
        super(BackgroundMusic.class, metadata);
    }

}

