package com.gl.service.device.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.commons.enums.StoreUserTypeEnum;
import com.gl.commons.enums.UserTypeEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.device.repository.DeviceVoiceRepository;
import com.gl.service.device.vo.*;
import com.gl.service.device.vo.dto.DeviceAddWorkDto;
import com.gl.service.device.vo.dto.DeviceDto;
import com.gl.service.device.vo.dto.DeviceUpdateVolume;
import com.gl.service.device.vo.dto.DeviceVoiceDto;
import com.gl.service.opus.entity.Device;
import com.gl.service.opus.entity.DeviceVoice;
import com.gl.service.opus.entity.VoicePacket;
import com.gl.service.opus.entity.VoiceWork;
import com.gl.service.opus.repository.VoicePacketRepository;
import com.gl.service.opus.repository.VoiceWorkRepository;
import com.gl.service.shop.entity.Shop;
import com.gl.service.shop.entity.ShopUserRef;
import com.gl.service.shop.repository.ShopRepository;
import com.gl.service.shop.repository.ShopUserRefRepository;
import com.gl.service.shop.service.ShopService;
import com.gl.service.shop.vo.ShopAddVo;
import com.gl.service.shop.vo.ShopSelectListVo;
import com.gl.system.vo.SysUserVo;
import com.gl.util.DateUtils;
import com.gl.util.GetShopRefUtil;
import com.gl.aspectj.annotation.ShopPermission;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 1.详情中的试听的接口没写
 *
 * @author: duanjinze
 * @date: 2022/11/10 17:49
 * @version: 1.0
 */
@Service
@Slf4j
public class DeviceService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private DeviceVoiceRepository deviceVoiceRepository;
    @Autowired
    private GetShopRefUtil shopRefUtil;
    @Autowired
    private ShopService shopService;
    @Autowired
    private GetShopRefUtil getShopRefUtil;

    @Autowired
    private ShopUserRefRepository shopUserRefRepository;
    @Autowired
    private VoicePacketRepository voicePacketRepository;

    public Result list(DeviceDto dto, Integer exportType) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (dto.getStatus() != null) {
                where.append(" and d.status = ? ");
                args.add(dto.getStatus());
            }
            if (dto.getShopId() != null) {
                where.append(" and s.id = ? ");
                args.add(dto.getShopId());
            }
            if (dto.getBindStatus() != null) {
                where.append(" and d.bind_status = ? ");
                args.add(dto.getBindStatus());
            }
            if (dto.getUseStatus() != null) {
                where.append(" and d.use_status = ? ");
                args.add(dto.getUseStatus());
            }
            if (dto.getShopId() != null) {
                where.append(" and s.id = ? ");
                args.add(dto.getShopId());
            }

            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and (d.id like ? or d.name like ? or d.sn like ? or wu.nickname like ? or wu.phone like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }

        // 门店权限查询
        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {// 绑定了门店
            where.append(" and  ( create_id = ?  or   s.id in " + SqlUtils.foreachIn(shopRef.size()) + " )");
            args.add(getShopRefUtil.getWxUserId());
            args.addAll(shopRef);
        }

        String sql = "SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, " + "(select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = "
                + "ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id "
                + "and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, "
                + "d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id as shopId  "
                + "FROM dub_device d  " + "LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 "
                + "LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id " + "LEFT JOIN dub_shop s on s.id=d.shop_id " + "WHERE d.del_status != 1 ";

        log.info("查询设备列表，sql:{}", sql + where);
        Long count = jdbcTemplate.queryForObject(String.format("select count(1) from (%s%s) t", sql, where), Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }
        where.append(" order by d.create_time DESC,d.id DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<DeviceVo> deviceVos = jdbcTemplate.query(String.format("%s%s", sql, where), new BeanPropertyRowMapper<>(DeviceVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", deviceVos);
        return result;
    }

    public void exportList(DeviceDto dto, HttpServletResponse response) throws IOException {
        Result list = list(dto, 2);
        HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(list.getData()), HashMap.class);
        Object result = hashMap.get("result");
        List<ExcelDevice> excelDevices = null;
        if (result != null) {
            List<DeviceVo> deviceVos = JSON.parseArray(JSON.toJSONString(result), DeviceVo.class);
            excelDevices = deviceVos.stream().map(item -> {
                ExcelDevice excelDevice = new ExcelDevice();
                excelDevice.setName(item.getName());
                excelDevice.setSn(item.getSn());
                excelDevice.setNickname(StringUtils.isBlank(item.getNickname()) ? "" : item.getNickname());
                excelDevice.setPhone(StringUtils.isBlank(item.getPhone()) ? "" : item.getPhone());
                excelDevice.setWorkCount(item.getWorkCount().toString());
                excelDevice.setStatus(item.getStatus() == 0 ? "离线" : "在线");
                excelDevice.setBindStatus(item.getBindStatus() == 0 ? "未绑定" : "已绑定");
                excelDevice.setUseStatus(item.getUseStatus() == 0 ? "停用" : "正常");
                excelDevice.setCreateTime(item.getCreateTime() == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(item.getCreateTime()));
                return excelDevice;
            }).collect(Collectors.toList());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = "设备管理_" + System.currentTimeMillis();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExcelDevice.class).sheet("设备管理").doWrite(excelDevices);
        // 导出excel
        log.info("设备管理导出end");
    }


    public Result addOrUpdate(DeviceVo vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        if (vo == null) {
            return Result.fail("数据不能为空");
        }
        if (StringUtils.isBlank(vo.getName())) {
            return Result.fail("设备名称不能为空");
        }
        if (StringUtils.isBlank(vo.getSn())) {
            return Result.fail("设备sn不能为空");
        }

        if (vo.getShopId() == null) {// 前端没有选门店
            // 管理员新增设备
            if (ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType())) {// 小程序用户
                Long shopId = getShopRefUtil.getOneShop();// 查询用户是否绑定了门店
                if (shopId != null) {// 判断用户是否绑定了门店
                    // 已绑定门店
                    vo.setShopId(shopId);
                    vo.setBindStatus(1);
                } else {
                    ShopAddVo shopAddVo = new ShopAddVo();
                    String time = DateUtils.datetimeToString(new Date(), "yyyyMMdd");
                    shopAddVo.setShopName("门店" + time);
                    shopAddVo.setUserId(user.getSiteId());
                    // 创建门店
                    Result result = shopService.addOrUpdate(shopAddVo);
                    Shop data = (Shop) result.getData();
                    vo.setShopId(data.getId());
                    vo.setBindStatus(1);
                }
            }
        } else {
            vo.setBindStatus(1);
        }

        Date date = new Date();
        if (vo.getId() == null) {
            // 新增
            Device device = new Device();
            device.setName(vo.getName());
            device.setSn(vo.getSn());
            device.setVolume(vo.getVolume());
            device.setShopId(vo.getShopId());
            device.setStatus(0);
            device.setBindStatus(vo.getBindStatus());
            device.setUseStatus(1);
            device.setDelStatus(0);
            device.setCreateTime(date);
            device.setCreateId(shopRefUtil.getWxUserId());
            device.setUpdateStatusTime(date);
            deviceRepository.save(device);
        } else {
            Optional<Device> byId = deviceRepository.findById(vo.getId());
            Device device;
            if (byId.isPresent()) {
                // 修改
                device = byId.get();
                device.setName(vo.getName());
                device.setSn(vo.getSn());
                device.setVolume(vo.getVolume());
                device.setShopId(vo.getShopId());
                device.setBindStatus(vo.getBindStatus());
            } else {
                // 新增
                device = new Device();
                device.setName(vo.getName());
                device.setSn(vo.getSn());
                device.setVolume(vo.getVolume());
                device.setShopId(vo.getShopId());
                device.setStatus(0);
                device.setBindStatus(vo.getBindStatus());
                device.setUseStatus(1);
                device.setDelStatus(0);
                device.setCreateId(shopRefUtil.getWxUserId());
                device.setCreateTime(date);
                device.setUpdateStatusTime(date);
            }
            deviceRepository.save(device);
        }
        return Result.success();
    }

    /**
     * 解绑与绑定
     *
     * @return
     */
    @ShopPermission(entity = Device.class, idParam = "id")
    public Result bindAndUntie(DeviceVo vo) {
        if (vo.getId() == null) {
            return Result.fail("id不能为空");
        }
        if (vo.getBindStatus() == null) {
            return Result.fail("解绑锁状态不能为空");
        }
        if (vo.getBindStatus() == 1) {
            if (vo.getUserId() == null) {
                return Result.fail("绑定用户id不能为空");
            }
        }
        log.info("修改设备绑定状态 vo： {}", vo);
        Optional<Device> byId = deviceRepository.findById(vo.getId());
        if (!byId.isPresent()) {
            return Result.fail("设备不存在");
        }
        Device device = byId.get();
        if (vo.getBindStatus() == 0) {
            device.setShopId(null);
            device.setUserId(null);
        } else {
            device.setShopId(vo.getShopId());
            device.setUserId(vo.getUserId());
        }
        device.setBindStatus(vo.getBindStatus());
        log.info("修改设备绑定状态： {}", device);
        deviceRepository.save(device);
        return Result.success();
    }

    /**
     * 单个修改使用状态
     */
    @ShopPermission(entity = Device.class, idParam = "deviceId")
    public Result updateUseStatus(DeviceDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getDeviceId() == null) {
            return Result.fail("设备id不能为空");
        }
        if (dto.getUseStatus() == null) {
            return Result.fail("使用状态不能为空");
        }
        deviceRepository.updateUseStatusById(dto.getDeviceId(), dto.getUseStatus());
        return Result.success();
    }

    @ShopPermission(entity = Device.class, idParam = "ids")
    public Result delete(DeviceDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getIds().isEmpty()) {
            return Result.fail("设备id不能为空");
        }
        for (Long id : dto.getIds()) {
            deviceRepository.updateDelStatusById(id);
        }
        return Result.success();
    }

    @ShopPermission(entity = Device.class, idParam = "deviceId")
    public Result detail(Long deviceId) {
        if (deviceId == null) {
            return Result.fail("设备id不能为空");
        }
        String sqlBase = "SELECT dd.id as deviceId,dd.name as deviceName,dd.sn,dd.status from dub_device dd WHERE dd.id = ? ";
        List<DeviceVoiceVo> deviceVoiceVos = jdbcTemplate.query(sqlBase, new BeanPropertyRowMapper<>(DeviceVoiceVo.class), deviceId);
        if (deviceVoiceVos.isEmpty()) {
            return Result.fail("无此设备数据");
        }
        DeviceVoiceVo deviceVoiceVo = deviceVoiceVos.get(0);
        String sql = "SELECT ddv.id,ddv.device_id,ddv.title,ddv.content,ddv.speed,ddv.volume,ddv.pitch,ddv.background_music_volume,ddv.sortby,ddv.voice_url "
                + "FROM dub_device_voice ddv " + "WHERE ddv.device_id = ? and ddv.del_status != 1 " + "order by ddv.sortby  ";
        List<DeviceVoice> deviceVoices = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DeviceVoice.class), deviceId);
        deviceVoiceVo.setDeviceVoiceList(deviceVoices);
        return Result.success(deviceVoiceVo);
    }

    /**
     * 删除设备与语音包关系表
     */
    public Result deleteDeviceAndVoice(DeviceVoiceDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getId() == null) {
            return Result.fail("设备与语音包关系表id不能为空");
        }
        Optional<DeviceVoice> byId = deviceVoiceRepository.findById(dto.getId());
        if (!byId.isPresent()) {
            return Result.fail("设备与语音包关系表不存在");
        }
        DeviceVoice deviceVoice = byId.get();
        checkDevice(deviceVoice.getDeviceId());
        deviceVoiceRepository.updateDelStatusById(dto.getId());
        return Result.success();
    }

    @ShopPermission(operate = true, entity = Device.class, idParam = "deviceId")
    public void checkDevice(Long deviceId) {
    }

    public Result updateSortBy(DeviceVoiceDto dto) {
        DeviceVoice deviceVoice = deviceVoiceRepository.getById(dto.getId());
        deviceVoice.setSortby(dto.getSortBy());
        deviceVoiceRepository.save(deviceVoice);
        return Result.success();
    }

    public Result getDeviceList() {
        String sql = "select id,name  from dub_device where del_status=0 and use_status = 1 ";
        List<ShopSelectListVo> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopSelectListVo.class));
        return Result.success(list);
    }

    @ShopPermission(entity = Device.class, idParam = "id")
    public Result relieveShop(DeviceVo vo) {
        Device device = deviceRepository.getById(vo.getId());
        device.setShopId(null);// 解绑门店
        device.setUserId(null);
        device.setBindStatus(0);
        deviceRepository.save(device);
        return Result.success();
    }

    @Autowired
    private VoiceWorkRepository voiceWorkRepository;

    @ShopPermission(entity = Device.class, idParam = "deviceId")
    public Result addWork(DeviceAddWorkDto dto) {
        // 作品
        VoiceWork voiceWork = voiceWorkRepository.getById(dto.getWorkId());
        // 语音包
        VoicePacket voicePacket = voicePacketRepository.getById(voiceWork.getVoiceId());
        String fileUrl = voicePacket.getFileUrl();
        // 设备语音
        DeviceVoice deviceVoice = new DeviceVoice();
        deviceVoice.setType(1);// 作品
        deviceVoice.setDeviceId(dto.getDeviceId());
        deviceVoice.setVoiceWorkId(voiceWork.getId());// 原作品id
        deviceVoice.setDeviceVoiceId(null);
        deviceVoice.setTitle(voiceWork.getTitle());
        deviceVoice.setContent(voiceWork.getContent());
        deviceVoice.setVoiceUrl(fileUrl);
        deviceVoice.setVoiceId(voiceWork.getAnchorId());
        deviceVoice.setVoiceTime(voiceWork.getVoiceTime());
        deviceVoice.setSpeed(voiceWork.getSpeed());
        deviceVoice.setVolume(voiceWork.getVolume());
        deviceVoice.setPitch(voiceWork.getPitch());
        deviceVoice.setBackgroundMusicId(voiceWork.getBackgroundMusicId());
        deviceVoice.setBackgroundMusicVolume(voiceWork.getBackgroundMusicVolume());
        deviceVoice.setSampleRate(voiceWork.getSampleRate());
        deviceVoice.setDelStatus(0);
        deviceVoiceRepository.save(deviceVoice);
        return Result.success();
    }

    @ShopPermission(entity = Device.class, idParam = "deviceId")
    public Result delWork(DeviceAddWorkDto dto) {
        deviceVoiceRepository.deleteById(dto.getId());
        return Result.success();
    }

    @ShopPermission(entity = Device.class, idParam = "deviceIdList")
    public Result updateVolume(DeviceUpdateVolume dto) {
        ArrayList<Long> deviceIdList = dto.getDeviceIdList();
        List<Device> list = new ArrayList<>();
        for (Long deviceId : deviceIdList) {
            Device device = deviceRepository.getById(deviceId);
            device.setVolume(dto.getVolume());// 音量
            deviceRepository.save(device);
        }
        return Result.success();
    }

    /**
     * 获取用户 可选设备列表
     *
     * @return
     */
    public Result getUserDeviceSelect() {
        return Result.success(getUserDevList());
    }


    public Result getTreeUserDeviceSelect() {
        List<DeviceTreeOptionVo> tree = new ArrayList<>();
        List<DeviceOptionVo> list = getUserDevList();
        if (CollUtil.isEmpty(list)) {
            return Result.success(tree);
        }
        Map<Long, List<DeviceOptionVo>> devMap = list.stream().filter(e -> e.getShopId() != null).collect(Collectors.groupingBy(DeviceOptionVo::getShopId));
        devMap.forEach((k, v) -> {
            DeviceTreeOptionVo parent = new DeviceTreeOptionVo();
            parent.setValue(k);
            parent.setLabel(v.get(0).getShopName());
            List<DeviceTreeOptionVo> childs = v.stream().map(e -> {
                DeviceTreeOptionVo child = new DeviceTreeOptionVo();
                child.setValue(e.getId());
                child.setLabel(e.getName());
                return child;
            }).collect(Collectors.toList());
            parent.setChildren(childs);
            tree.add(parent);
        });
        return Result.success(tree);
    }

    private List<DeviceOptionVo> getUserDevList() {
        StringBuilder sql = new StringBuilder("SELECT " + "   dd.*, " + "   shop.shop_name " + "FROM " + "   dub_device dd "
                + "   LEFT JOIN dub_shop shop ON shop.id = dd.shop_id  " + "WHERE " + "   dd.del_status = 0");
        List<Object> args = new ArrayList<>();
        if (getShopRefUtil.isNeedWxFilter()) {
            List<Long> shopids = getShopRefUtil.getShopRef();
            if (CollUtil.isNotEmpty(shopids)) {
                sql.append(" AND dd.shop_id IN ").append(SqlUtils.foreachIn(shopids.size()));
                args.addAll(shopids);
            }
        }
        return jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(DeviceOptionVo.class), args.toArray());
    }

    public Result add(DeviceVo vo) {
        Device device = deviceRepository.findById(vo.getId()).get();
        if (device.getShopId() != null) {
            return Result.fail("设备已绑定门店");
        }
        if (vo.getShopId() == null) {
            vo.setShopId(getShopRefUtil.getOneShop());
        } else {
            ShopUserRef shopUserRef = shopUserRefRepository.findByShopIdAndUserIdAndRole(vo.getShopId(),
                    getShopRefUtil.getWxUserId(), StoreUserTypeEnum.ADMIN.getCode());
            if (shopUserRef == null) {
                return Result.fail("不是门店管理员无权限操作");
            }
        }
        return addOrUpdate(vo);
    }

    @ShopPermission(entity = Device.class, idParam = "id")
    public Result update(DeviceVo vo) {
        return addOrUpdate(vo);
    }
}
