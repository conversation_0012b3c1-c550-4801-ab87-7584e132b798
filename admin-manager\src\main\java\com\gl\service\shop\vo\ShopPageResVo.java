package com.gl.service.shop.vo;

import com.gl.service.shop.entity.Shop;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date: 2025/3/2
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShopPageResVo extends Shop {

    // 设备数
    private Integer deviceCount;
    // 商户人数
    private Integer userCount;

    private String nickname;
    private Long userId;

    private String itemNames;
    private String parentName;


    private Boolean isEdit;

}
