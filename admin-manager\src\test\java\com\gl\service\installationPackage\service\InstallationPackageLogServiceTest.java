package com.gl.service.installationPackage.service;

import com.gl.framework.web.response.Result;
import com.gl.service.installationPackage.entity.InstallationPackageLog;
import com.gl.service.installationPackage.repository.InstallationPackageLogRepository;
import com.gl.service.installationPackage.vo.installationPackageLog.InstallationPackageLogVo;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDelDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogRenewalDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallationPackageLogService单元测试类
 * 测试安装包更新日志服务的所有业务逻辑
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("安装包更新日志服务测试")
class InstallationPackageLogServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private InstallationPackageLogRepository installationPackageLogRepository;

    @Mock
    private AsyncPackageService asyncPackageService;

    @InjectMocks
    private InstallationPackageLogService installationPackageLogService;

    private InstallationPackageLogDto testDto;
    private InstallationPackageLogDelDto testDelDto;
    private InstallationPackageLogRenewalDto testRenewalDto;
    private List<InstallationPackageLogVo> testLogVoList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testDto = new InstallationPackageLogDto();
        testDto.setSearchCondition("test");
        testDto.setBeginTime("2024-01-01 00:00:00");
        testDto.setEndTime("2024-12-31 23:59:59");
        testDto.setPageSize(10);
        testDto.setPageNumber(0);

        testDelDto = new InstallationPackageLogDelDto();
        testDelDto.setIds(Arrays.asList(1L, 2L, 3L));

        testRenewalDto = new InstallationPackageLogRenewalDto();
        testRenewalDto.setInstallationPackageId(100L);
        testRenewalDto.setDeviceIdList(Arrays.asList(1L, 2L, 3L));

        // 创建测试VO数据
        testLogVoList = new ArrayList<>();
        InstallationPackageLogVo vo1 = new InstallationPackageLogVo();
        vo1.setId(1L);
        vo1.setShopName("测试店铺1");
        vo1.setDeviceName("测试设备1");
        vo1.setSn("SN001");
        vo1.setVersionName("v1.0.0");
        vo1.setCreateTime(new Date());
        vo1.setStatus(1);
        testLogVoList.add(vo1);

        InstallationPackageLogVo vo2 = new InstallationPackageLogVo();
        vo2.setId(2L);
        vo2.setShopName("测试店铺2");
        vo2.setDeviceName("测试设备2");
        vo2.setSn("SN002");
        vo2.setVersionName("v1.0.1");
        vo2.setCreateTime(new Date());
        vo2.setStatus(2);
        testLogVoList.add(vo2);
    }

    @Test
    @DisplayName("测试list方法 - 正常查询带搜索条件和时间范围")
    void testList_WithSearchConditionAndTimeRange_ShouldReturnSuccessResult() {
        // Given
        Long expectedCount = 2L;
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testLogVoList);

        // When
        Result result = installationPackageLogService.list(testDto, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "查询应该成功");
        @SuppressWarnings("unchecked")
        Map<String, Object> dataMap = (Map<String, Object>) result.getData();
        assertEquals(expectedCount, dataMap.get("total"), "总数应该匹配");
        assertEquals(testLogVoList, dataMap.get("result"), "结果列表应该匹配");

        // 验证SQL查询调用
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 查询结果为空")
    void testList_WithNoResults_ShouldReturnEmptyResult() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(0L);

        // When
        Result result = installationPackageLogService.list(testDto, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "查询应该成功");
        @SuppressWarnings("unchecked")
        Map<String, Object> dataMap = (Map<String, Object>) result.getData();
        assertEquals(0, dataMap.get("total"), "总数应该为0");
        assertNull(dataMap.get("result"), "结果列表应该为空");

        // 验证只调用了count查询，没有调用数据查询
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 空DTO参数")
    void testList_WithNullDto_ShouldReturnValidResult() {
        // Given
        Long expectedCount = 1L;
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testLogVoList.subList(0, 1));

        // When
        Result result = installationPackageLogService.list(null, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "查询应该成功");
        @SuppressWarnings("unchecked")
        Map<String, Object> dataMap = (Map<String, Object>) result.getData();
        assertEquals(expectedCount, dataMap.get("total"), "总数应该匹配");
    }

    @Test
    @DisplayName("测试list方法 - 导出类型不为1时不分页")
    void testList_WithExportTypeNotOne_ShouldNotAddPagination() {
        // Given
        Long expectedCount = 2L;
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testLogVoList);

        // When
        Result result = installationPackageLogService.list(testDto, 0);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "查询应该成功");
        @SuppressWarnings("unchecked")
        Map<String, Object> dataMap = (Map<String, Object>) result.getData();
        assertEquals(expectedCount, dataMap.get("total"), "总数应该匹配");
        assertEquals(testLogVoList, dataMap.get("result"), "结果列表应该匹配");
    }

    @Test
    @DisplayName("测试list方法 - 只有搜索条件没有时间范围")
    void testList_WithOnlySearchCondition_ShouldReturnValidResult() {
        // Given
        InstallationPackageLogDto dtoWithOnlySearch = new InstallationPackageLogDto();
        dtoWithOnlySearch.setSearchCondition("test");
        dtoWithOnlySearch.setPageSize(10);
        dtoWithOnlySearch.setPageNumber(0);

        Long expectedCount = 1L;
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testLogVoList.subList(0, 1));

        // When
        Result result = installationPackageLogService.list(dtoWithOnlySearch, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "查询应该成功");
        @SuppressWarnings("unchecked")
        Map<String, Object> dataMap = (Map<String, Object>) result.getData();
        assertEquals(expectedCount, dataMap.get("total"), "总数应该匹配");
    }

    @Test
    @DisplayName("测试delete方法 - 正常删除多个记录")
    void testDelete_WithValidIds_ShouldReturnSuccessResult() {
        // Given
        doNothing().when(installationPackageLogRepository).deleteById(anyLong());

        // When
        Result result = installationPackageLogService.delete(testDelDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "删除应该成功");

        // 验证每个ID都被删除
        verify(installationPackageLogRepository, times(3)).deleteById(anyLong());
        verify(installationPackageLogRepository).deleteById(1L);
        verify(installationPackageLogRepository).deleteById(2L);
        verify(installationPackageLogRepository).deleteById(3L);
    }

    @Test
    @DisplayName("测试delete方法 - DTO为空")
    void testDelete_WithNullDto_ShouldReturnFailResult() {
        // When
        Result result = installationPackageLogService.delete(null);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "删除应该失败");
        assertEquals("数据不能为空", result.getMessage(), "错误消息应该匹配");

        // 验证没有调用删除操作
        verify(installationPackageLogRepository, never()).deleteById(anyLong());
    }

    @Test
    @DisplayName("测试delete方法 - ID列表为空")
    void testDelete_WithEmptyIds_ShouldReturnFailResult() {
        // Given
        InstallationPackageLogDelDto emptyDto = new InstallationPackageLogDelDto();
        emptyDto.setIds(new ArrayList<>());

        // When
        Result result = installationPackageLogService.delete(emptyDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "删除应该失败");
        assertEquals("更新日志id不能为空", result.getMessage(), "错误消息应该匹配");

        // 验证没有调用删除操作
        verify(installationPackageLogRepository, never()).deleteById(anyLong());
    }

    @Test
    @DisplayName("测试delete方法 - 删除过程中发生异常")
    void testDelete_WithRepositoryException_ShouldThrowException() {
        // Given
        doThrow(new RuntimeException("数据库连接异常")).when(installationPackageLogRepository).deleteById(1L);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            installationPackageLogService.delete(testDelDto);
        }, "应该抛出运行时异常");

        // 验证至少调用了一次删除操作
        verify(installationPackageLogRepository, atLeastOnce()).deleteById(anyLong());
    }

    @Test
    @DisplayName("测试renewal方法 - 正常更新安装包")
    void testRenewal_WithValidData_ShouldReturnSuccessResult() {
        // Given
        List<InstallationPackageLog> savedLogs = new ArrayList<>();
        for (Long deviceId : testRenewalDto.getDeviceIdList()) {
            InstallationPackageLog log = new InstallationPackageLog();
            log.setId(deviceId);
            log.setDeviceId(deviceId);
            log.setPackageId(testRenewalDto.getInstallationPackageId());
            log.setStatus(1);
            log.setCreateTime(new Date());
            savedLogs.add(log);
        }

        when(installationPackageLogRepository.saveAll(anyList())).thenReturn(savedLogs);
        doNothing().when(asyncPackageService).uploadPackage(anyList());

        // When
        Result result = installationPackageLogService.renewal(testRenewalDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "更新应该成功");

        // 验证保存和异步上传调用
        verify(installationPackageLogRepository, times(1)).saveAll(anyList());
        verify(asyncPackageService, times(1)).uploadPackage(anyList());
    }

    @Test
    @DisplayName("测试renewal方法 - DTO为空")
    void testRenewal_WithNullDto_ShouldReturnFailResult() {
        // When
        Result result = installationPackageLogService.renewal(null);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "更新应该失败");
        assertEquals("数据不能为空", result.getMessage(), "错误消息应该匹配");

        // 验证没有调用保存和上传操作
        verify(installationPackageLogRepository, never()).saveAll(anyList());
        verify(asyncPackageService, never()).uploadPackage(anyList());
    }

    @Test
    @DisplayName("测试renewal方法 - 设备ID列表为空")
    void testRenewal_WithEmptyDeviceIdList_ShouldReturnFailResult() {
        // Given
        InstallationPackageLogRenewalDto emptyDeviceDto = new InstallationPackageLogRenewalDto();
        emptyDeviceDto.setInstallationPackageId(100L);
        emptyDeviceDto.setDeviceIdList(new ArrayList<>());

        // When
        Result result = installationPackageLogService.renewal(emptyDeviceDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "更新应该失败");
        assertEquals("选中设备不能为空", result.getMessage(), "错误消息应该匹配");

        // 验证没有调用保存和上传操作
        verify(installationPackageLogRepository, never()).saveAll(anyList());
        verify(asyncPackageService, never()).uploadPackage(anyList());
    }

    @Test
    @DisplayName("测试renewal方法 - 安装包ID为空")
    void testRenewal_WithNullInstallationPackageId_ShouldReturnFailResult() {
        // Given
        InstallationPackageLogRenewalDto nullPackageDto = new InstallationPackageLogRenewalDto();
        nullPackageDto.setInstallationPackageId(null);
        nullPackageDto.setDeviceIdList(Arrays.asList(1L, 2L));

        // When
        Result result = installationPackageLogService.renewal(nullPackageDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "更新应该失败");
        assertEquals("安装包id不能为空", result.getMessage(), "错误消息应该匹配");

        // 验证没有调用保存和上传操作
        verify(installationPackageLogRepository, never()).saveAll(anyList());
        verify(asyncPackageService, never()).uploadPackage(anyList());
    }

    @Test
    @DisplayName("测试renewal方法 - 保存过程中发生异常")
    void testRenewal_WithRepositoryException_ShouldThrowException() {
        // Given
        when(installationPackageLogRepository.saveAll(anyList()))
                .thenThrow(new RuntimeException("数据库保存异常"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            installationPackageLogService.renewal(testRenewalDto);
        }, "应该抛出运行时异常");

        // 验证调用了保存操作但没有调用上传操作
        verify(installationPackageLogRepository, times(1)).saveAll(anyList());
        verify(asyncPackageService, never()).uploadPackage(anyList());
    }

    @Test
    @DisplayName("测试renewal方法 - 验证创建的日志实体属性")
    void testRenewal_ShouldCreateCorrectLogEntities() {
        // Given
        List<InstallationPackageLog> capturedLogs = new ArrayList<>();
        when(installationPackageLogRepository.saveAll(anyList())).thenAnswer(invocation -> {
            List<InstallationPackageLog> logs = invocation.getArgument(0);
            capturedLogs.addAll(logs);
            return logs;
        });
        doNothing().when(asyncPackageService).uploadPackage(anyList());

        // When
        Result result = installationPackageLogService.renewal(testRenewalDto);

        // Then
        assertEquals(10000, result.getCode(), "更新应该成功");
        assertEquals(3, capturedLogs.size(), "应该创建3个日志实体");

        // 验证每个日志实体的属性
        for (int i = 0; i < capturedLogs.size(); i++) {
            InstallationPackageLog log = capturedLogs.get(i);
            assertEquals(testRenewalDto.getDeviceIdList().get(i), log.getDeviceId(), "设备ID应该匹配");
            assertEquals(testRenewalDto.getInstallationPackageId(), log.getPackageId(), "安装包ID应该匹配");
            assertEquals(Integer.valueOf(1), log.getStatus(), "状态应该为1");
            assertNotNull(log.getCreateTime(), "创建时间不应为空");
        }
    }
}
