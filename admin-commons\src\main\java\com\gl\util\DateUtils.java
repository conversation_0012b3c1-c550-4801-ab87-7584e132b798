package com.gl.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 取得标准格式日期和时间
 *
 * <AUTHOR>
 */
public class DateUtils {
    private static final Logger LOG = LoggerFactory.getLogger(DateUtils.class);

    /**
     * yyyyMMddHHmmss
     */
    public static final String FORMAT_14 = "yyyyMMddHHmmss";

    /**
     * yyyyMMddhhmm
     */
    public static final String FORMAT_12 = "yyyyMMddhhmm";

    /**
     * yyyyMMddhhmm
     */
    public static final String FORMAT_10 = "yyyyMMddhh";

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public static final String FORMAT_DATETIME_14 = "yyyy-MM-dd HH:mm:ss";

    /**
     * yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String FORMAT_DATETIME_HM_14 = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String FORMAT_HM_14 = "yyyyMMddHHmmssSSS";

    /**
     * yyyyMM
     */
    public static final String FORMAT_6 = "yyyyMM";

    /**
     * yyyy
     */
    public static final String FORMAT_4 = "yyyy";

    /**
     * MMdd
     */
    public static final String FORMAT_7 = "yyMM";

    /**
     * yyyyMMdd
     */
    public static final String FORMAT_8 = "yyyyMMdd";

    /**
     * yyyy.MM.dd
     */
    public static final String FORMAT_SPOT_8 = "yyyy.MM.dd";

    /**
     * yyyy-MM-dd
     */
    public static final String FORMAT_DATE_8 = "yyyy-MM-dd";

    /**
     * yyyy-MM-dd HH:mm
     */
    public static final String FORMAT_DATETIME_15 = "yyyy-MM-dd HH:mm";

    private DateUtils() {

    }

    public static Date getYesterday(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        date = calendar.getTime();
        return date;
    }

    public static String format(String time, String rformat, String sformat) {
        SimpleDateFormat fmt = new SimpleDateFormat(rformat);
        Date ndate = null;
        try {
            ndate = fmt.parse(time);
            SimpleDateFormat format = new SimpleDateFormat(sformat);
            return format.format(ndate);
        } catch (ParseException e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }

    public static String format(Date date, String format) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sformat = new SimpleDateFormat(format);
        return sformat.format(date);
    }

    public static Date convert(String time, String format) {
        SimpleDateFormat sformat = new SimpleDateFormat(format);
        try {
            return sformat.parse(time);
        } catch (ParseException e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
        //return null;
    }

    public static String datetimeToString(Date date, String format) {
        Date dateTime = date;
        if (dateTime == null) {
            dateTime = new Date();
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(dateTime);
    }

    public static Date stringToDateTime(String str, String format) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.parse(str);
    }

    /**
     * when date1 earlier than date2 ,return -1 when date1 equals date2 ,return 0
     * when date1 later than date2 ,return 1
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int compareDate(String date1, String date2, String dateFormat) {

        DateFormat df = new SimpleDateFormat(dateFormat);

        try {
            Date dt1 = df.parse(date1);
            Date dt2;
            dt2 = df.parse(date2);

            if (dt1.getTime() > dt2.getTime()) {
                return 1;
            } else if (dt1.getTime() < dt2.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (ParseException e) {
            LOG.error("CompareDate Exception = {}", e.getMessage());
        }
        return 0;
    }

    /**
     * 计算时间差
     *
     * @param nowTime
     * @param endTime
     * @return
     */
    public static Map<String, Long> dateDifferent(Date nowTime, Date endTime) {
        Map<String, Long> map = new HashMap<String, Long>();
        try {
            long nd = 1000 * 24 * 60 * 60;
            long nh = 1000 * 60 * 60;
            long nm = 1000 * 60;
            long ns = 1000;
            long diff = endTime.getTime() - nowTime.getTime();
            // 计算差多少天
            long day = diff / nd;
            // 计算差多少小时
            long hour = diff % nd / nh;
            // 计算差多少分钟
            long min = diff % nd % nh / nm;
            // 计算差多少秒
            long sen = diff % nd % nh % nm / ns;
            map.put("day", day);
            map.put("hour", hour);
            map.put("min", min);
            map.put("sen", sen); // 计算出来的结果是负数
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 计算2个日期相差多少天
     *
     * @param beginDate 开始日期（yyyy-MM-dd）
     * @param endDate   结束日期（yyyy-MM-dd）
     * @return
     * @throws ParseException
     */
    public static int daysBetween(Date beginDate, Date endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        beginDate = sdf.parse(sdf.format(beginDate));
        endDate = sdf.parse(sdf.format(endDate));

        Calendar cal = Calendar.getInstance();
        cal.setTime(beginDate);

        long time1 = cal.getTimeInMillis();
        cal.setTime(endDate);

        long time2 = cal.getTimeInMillis();

        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     * 计算2个日期相差多少小时
     *
     * @param beginDate 开始日期（yyyyMMddHH）
     * @param endDate   开始日期（yyyyMMddHH）
     * @return
     * @throws ParseException
     */
    public static int hoursBetween(Date beginDate, Date endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");
        beginDate = sdf.parse(sdf.format(beginDate));
        endDate = sdf.parse(sdf.format(endDate));

        Calendar cal = Calendar.getInstance();
        cal.setTime(beginDate);

        long time1 = cal.getTimeInMillis();
        cal.setTime(endDate);

        long time2 = cal.getTimeInMillis();

        long between_hours = (time2 - time1) / (1000 * 3600);
        return Integer.parseInt(String.valueOf(between_hours));
    }

    /**
     * 计算2个时间分钟数(多1秒钟都算1分钟)
     *
     * @param beginDate
     * @param endDate
     * @return
     * @throws ParseException
     */
    public static int minuteBetween(Date beginDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            beginDate = sdf.parse(sdf.format(beginDate));
            endDate = sdf.parse(sdf.format(endDate));

            Calendar cal = Calendar.getInstance();
            cal.setTime(beginDate);

            long time1 = cal.getTimeInMillis();
            cal.setTime(endDate);

            long time2 = cal.getTimeInMillis();

            //long between_minutes = (time2 - time1) / (1000 * 60);
            BigDecimal between_minutes = new BigDecimal(time2 - time1).divide(new BigDecimal(1000 * 60), 0, RoundingMode.HALF_UP);
            return between_minutes.intValue();
        } catch (ParseException e) {
            LOG.error("ParseException Exception = {}", e.getMessage());
        }

        return 0;

    }

    /**
     * 获取当前日期前/后minutes分钟的日期
     *
     * @param minutes 前传负数，后传正数
     * @return
     * <AUTHOR>
     */
    public static String rollMinute(long minutes) {
        return LocalDateTime.now().plusMinutes(minutes).format(DateTimeFormatter.ofPattern(FORMAT_DATETIME_14));
    }

    /**
     * 计算指定日期距离当前时间的秒数
     *
     * @param startDate
     * @return
     * <AUTHOR>
     */
    public static long secondsBetween(Date startDate) {
        Instant instant = startDate.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime start = instant.atZone(zoneId).toLocalDateTime();

        LocalDateTime end = LocalDateTime.now();

        Duration duration = Duration.between(start, end);

        return duration.toMillis() * 1000;
    }

    /**
     * 得到指定日期，几天前或几天后的日期
     *
     * @param date 格式：yyyyMMdd
     * @param days
     * @return 格式：yyyyMMdd
     */
    public static String getAddDaysTime(String date, String format, int days) {
        Calendar cal = Calendar.getInstance();
        Date fDate = stringToDateTimeForTags(date, format);
        cal.setTime(fDate);
        cal.add(Calendar.DAY_OF_MONTH, days);
        return new SimpleDateFormat(FORMAT_8).format(cal.getTime());
    }

    /**
     * 得到指定日期，几天前或几天后的日期
     *
     * @param date 格式：yyyyMMdd
     * @param days
     * @return 格式：yyyyMMdd
     */
    public static String getAddDaysTime(Date date, String format, int days) {
        Calendar cal = Calendar.getInstance();
        // Date fDate= stringToDateTimeForTags(date, format);
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, days);
        return new SimpleDateFormat(FORMAT_8).format(cal.getTime());
    }


    /**
     * 得到指定日期，几天前或几天后的日期
     *
     * @param date 格式：yyyyMMdd
     * @param days
     * @return 格式：yyyyMMdd
     */
    public static String getAddDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, days);
        return new SimpleDateFormat(FORMAT_8).format(cal.getTime());
    }

    public static Date stringToDateTimeForTags(String str, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String datetimeToStringForTags(Date date, String format) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

}
