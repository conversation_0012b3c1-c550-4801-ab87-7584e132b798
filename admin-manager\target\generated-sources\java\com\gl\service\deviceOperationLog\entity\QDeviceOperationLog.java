package com.gl.service.deviceOperationLog.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QDeviceOperationLog is a Querydsl query type for DeviceOperationLog
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QDeviceOperationLog extends EntityPathBase<DeviceOperationLog> {

    private static final long serialVersionUID = -1535177704L;

    public static final QDeviceOperationLog deviceOperationLog = new QDeviceOperationLog("deviceOperationLog");

    public final StringPath content = createString("content");

    public final NumberPath<Long> deviceId = createNumber("deviceId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath responseContent = createString("responseContent");

    public final DateTimePath<java.util.Date> responseTime = createDateTime("responseTime", java.util.Date.class);

    public final DateTimePath<java.util.Date> sendTime = createDateTime("sendTime", java.util.Date.class);

    public final NumberPath<Long> shopId = createNumber("shopId", Long.class);

    public final StringPath timeStamp = createString("timeStamp");

    public final StringPath topic = createString("topic");

    public final StringPath type = createString("type");

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QDeviceOperationLog(String variable) {
        super(DeviceOperationLog.class, forVariable(variable));
    }

    public QDeviceOperationLog(Path<? extends DeviceOperationLog> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDeviceOperationLog(PathMetadata metadata) {
        super(DeviceOperationLog.class, metadata);
    }

}

