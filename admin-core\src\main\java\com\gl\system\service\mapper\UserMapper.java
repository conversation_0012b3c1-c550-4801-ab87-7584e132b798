package com.gl.system.service.mapper;

import com.gl.framework.common.util.StringUtils;
import com.gl.system.vo.SysDeptVo;
import com.gl.system.vo.SysUserVo;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class UserMapper implements RowMapper {
    @Override
    public SysUserVo mapRow(ResultSet rs, int i) throws SQLException {

        SysUserVo vo = new SysUserVo();

        vo.setId(rs.getLong("id"));
        vo.setDeptId(rs.getLong("dept_id"));
        vo.setLoginName(rs.getString("login_name"));
        vo.setUserName(rs.getString("user_name"));
        vo.setEmail(rs.getString("email"));
        vo.setPhone(rs.getString("phone"));
        vo.setGender(rs.getString("gender") == null ?null:rs.getInt("gender"));
        vo.setAvatar(rs.getString("avatar"));
        vo.setStatus(rs.getInt("status"));
        vo.setCreateTime(rs.getDate("create_time"));

        SysDeptVo sdVo = new SysDeptVo();
        sdVo.setDeptName(rs.getString("dept_name"));

        vo.setDept(sdVo);

        String roleIds = rs.getString("role_id");
        if (StringUtils.isNotEmpty(roleIds)) {
            List<String> list = Arrays.asList(roleIds.split(";"));
            List<Long> listIds = new ArrayList<>();
            for (String str : list) {
                listIds.add(Long.parseLong(str));
            }

            vo.setRoleIds(listIds);
        }
        String roleNames = rs.getString("role_name");
        if (StringUtils.isNotEmpty(roleNames)) {
            List<String> list = Arrays.asList(roleNames.split(";"));
            List<String> listNames = new ArrayList<>();
            for (String str : list) {
                listNames.add(str);
            }

            vo.setRoleNames(listNames);
        }

        String positionIds = rs.getString("position_id");
        if (StringUtils.isNotEmpty(positionIds)) {
            List<String> list = Arrays.asList(positionIds.split(";"));
            List<Long> listIds = new ArrayList<>();
            for (String str : list) {
                listIds.add(Long.parseLong(str));
            }

            vo.setPositionIds(listIds);
        }
        String positionNames = rs.getString("position_name");
        if (StringUtils.isNotEmpty(positionNames)) {
            List<String> list = Arrays.asList(positionNames.split(";"));
            List<String> listNames = new ArrayList<>();
            for (String str : list) {
                listNames.add(str);
            }

            vo.setPositionNames(listNames);
        }

        return vo;
    }
}
