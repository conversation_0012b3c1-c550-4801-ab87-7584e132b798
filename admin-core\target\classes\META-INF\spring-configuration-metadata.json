{"groups": [{"name": "aliyun-oss", "type": "com.gl.framework.properties.AliyunOssProperties", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "project", "type": "com.gl.framework.properties.ProjectProperties", "sourceType": "com.gl.framework.properties.ProjectProperties"}], "properties": [{"name": "aliyun-oss.access-key-id", "type": "java.lang.String", "description": "OSS accessKeyId", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "aliyun-oss.access-key-secret", "type": "java.lang.String", "description": "OSS accessKeySecret", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "aliyun-oss.endpoint", "type": "java.lang.String", "description": "OSS 域名", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "aliyun-oss.expire-time", "type": "java.lang.Integer", "description": "签名有效期（单位：天）", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "aliyun-oss.private-bucket-host", "type": "java.lang.String", "description": "OSS 私有bucket访问域名", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "aliyun-oss.private-bucket-name", "type": "java.lang.String", "description": "OSS 私有bucket", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "aliyun-oss.public-bucket-host", "type": "java.lang.String", "description": "OSS 公有bucket访问域名", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "aliyun-oss.public-bucket-name", "type": "java.lang.String", "description": "OSS 公有bucket", "sourceType": "com.gl.framework.properties.AliyunOssProperties"}, {"name": "project.captcha-type", "type": "java.lang.String", "description": "验证码类型: math 数组计算; char 字符验证", "sourceType": "com.gl.framework.properties.ProjectProperties"}, {"name": "project.name", "type": "java.lang.String", "description": "项目名称", "sourceType": "com.gl.framework.properties.ProjectProperties"}, {"name": "project.version", "type": "java.lang.String", "description": "版本", "sourceType": "com.gl.framework.properties.ProjectProperties"}], "hints": []}