package com.gl.service.shop.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.redis.RedisUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.framework.security.service.TokenService;
import com.gl.framework.web.response.Result;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.opus.dto.AnchorDTO;
import com.gl.service.opus.entity.FollowAnchor;
import com.gl.service.opus.entity.LongFollowAnchor;
import com.gl.service.opus.entity.UserFollowBgm;
import com.gl.service.opus.entity.UserTextTemplate;
import com.gl.service.opus.repository.BackgroundMusicRepository;
import com.gl.service.opus.repository.DubAnchorRepository;
import com.gl.service.opus.repository.LongAnchorRepository;
import com.gl.service.shop.controller.request.FollowAnchorReq;
import com.gl.service.shop.controller.request.FollowBgmReq;
import com.gl.service.shop.controller.request.TextTemplateAddReq;
import com.gl.service.shop.controller.request.TextTemplateDelReq;
import com.gl.service.shop.repository.FollowAnchorRepository;
import com.gl.service.shop.repository.LongFollowAnchorRepository;
import com.gl.service.shop.repository.UserFollowBgmRepository;
import com.gl.service.shop.repository.UserTextTemplateRepository;
import com.gl.service.template.vo.TemplateVo;
import com.gl.system.entity.*;
import com.gl.system.repository.*;
import com.gl.system.vo.SysUserVo;
import com.gl.util.ImageUrlToBase64WithHttpClient;
import com.gl.wechat.WeCharUserInfo;
import com.gl.wechat.entity.WechatUser;
import com.gl.wechat.repository.WechatUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WeChatService {

    @Resource
    private WechatUserRepository wechatUserRepository;
    @Resource
    private SysUserRepository userRepository;
    @Resource
    private SysRoleRepository roleRepository;
    @Resource
    private SysDeptRepository deptRepository;
    @Resource
    private RedisUtils redisUtils;
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private UserDetailsService userDetailsService;
    @Resource
    private TokenService tokenService;

    @Value("${wechat.mp.appId}")
    private String appId;
    @Value("${wechat.mp.secret}")
    private String appSecret;
    @Value("${wechat.mp.redirectUri}")
    private String redirectUri;

    private static final Long ROLE_ID = 10L;
    private static final Long DEPT_ID = 200L;

    private static final String WECHAT_AUTHORIZE_URL = "https://open.weixin.qq.com/connect/qrconnect";
    private static final String WECHAT_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token";
    private static final String WECHAT_USERINFO_URL = "https://api.weixin.qq.com/sns/userinfo";
    @Autowired
    private SysUserDeptRepository sysUserDeptRepository;
    @Autowired
    private SysUserRoleRepository sysUserRoleRepository;

    @Autowired
    private UserTextTemplateRepository userTextTemplateRepository;

    @Autowired
    private DubAnchorRepository dubAnchorRepository;

    @Autowired
    private LongAnchorRepository longAnchorRepository;

    @Autowired
    private FollowAnchorRepository followAnchorRepository;

    @Autowired
    private LongFollowAnchorRepository longFollowAnchorRepository;

    @Autowired
    private UserFollowBgmRepository userFollowBgmRepository;

    @Autowired
    private BackgroundMusicRepository backgroundMusicRepository;

    public Map<String, Object> webLogin(HttpServletRequest request) {
        String scope = "snsapi_login"; // 网页应用使用snsapi_login
        String state = UUID.randomUUID().toString(); // 生成随机state参数
        // 将state存储在session中，用于防止CSRF攻击
//        request.getSession().setAttribute("oauth_state", state);
        String loginUrl = String.format("%s?appid=%s&redirect_uri=%s&response_type=code&scope=%s&state=%s#wechat_redirect",
                WECHAT_AUTHORIZE_URL,
                appId,
                redirectUri.concat("/login"),
                scope,
                state
        );
        Map<String, Object> result = new HashMap<>();
        result.put("loginUrl", loginUrl);
        result.put("state", state);
        return result;
    }

    // 获取二维码凭证
    public Map<String, String> getQRCodeTicket() throws Exception {
        String uuid = UUID.randomUUID().toString();
        // 获取access_token
        String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;
        String accessTokenResponse = restTemplate.getForObject(accessTokenUrl, String.class);
        log.info("accessTokenResponse: {}", accessTokenResponse);
        JsonNode tokenNode = objectMapper.readTree(accessTokenResponse);
        log.info("tokenNode: {}", tokenNode);
        String accessToken = tokenNode.get("access_token").asText();
        log.info("access_token: {}", accessToken);
        // 创建二维码
        String qrCodeUrl = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=" + accessToken;
        Map<String, Object> qrRequest = getStringObjectMap(uuid);
        log.info("qrRequest: {}", qrRequest);
        String qrResponse = restTemplate.postForObject(qrCodeUrl, qrRequest, String.class);
        log.info("qrResponse: {}", qrResponse);
        JsonNode qrNode = objectMapper.readTree(qrResponse);
        String ticket = qrNode.get("ticket").asText();

        // 构建二维码图片URL
        String qrCodeImgUrl = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + java.net.URLEncoder.encode(ticket, "UTF-8");
        // 存储uuid状态
        redisUtils.set(uuid, new WeCharUserInfo());

        Map<String, String> result = new HashMap<>();
        result.put("uuid", uuid);
        result.put("qrCodeUrl", qrCodeImgUrl);
        return result;
    }

    private Map<String, Object> getStringObjectMap(String uuid) {
        Map<String, Object> qrRequest = new HashMap<>();
        qrRequest.put("expire_seconds", 604800); // 有效期
        qrRequest.put("scene_str", uuid); // 可以传递uuid
        qrRequest.put("action_name", "QR_STR_SCENE");
        log.info("qrRequest1 : {}", qrRequest);
        // 构建 action_info 的嵌套
        Map<String, Object> actionInfoScene = new HashMap<>();
        actionInfoScene.put("scene_str", uuid);
        Map<String, Object> actionInfo = new HashMap<>();
        actionInfo.put("scene", actionInfoScene);
        qrRequest.put("action_info", actionInfo);

        log.info("actionInfo : {}", actionInfo);
        return qrRequest;
    }

    // 处理微信回调
    @Transactional
    public Map<String, Object> handleCallback(String code, String state) throws Exception {
        JsonNode userNode = getWxJsonNode(code);
        WechatUser user = getWechatUser(userNode);
        SysUser sysUser = getSysUser(user);
        LoginUser loginUser = BeanUtil.toBean(userDetailsService.loadUserByUsername(sysUser.getLoginName()), LoginUser.class);
        // 生成token
        String token = tokenService.createToken(loginUser);
        // 生成JWT Token
        Map<String, Object> map = new HashMap<>();
        map.put(Constants.TOKEN, token);
        return map;
    }

    private JsonNode getWxJsonNode(String code) throws JsonProcessingException {
        // 使用code获取access_token和openid
        String tokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appId + "&secret=" + appSecret + "&code=" + code + "&grant_type=authorization_code";
        log.info("tokenUrl: {}", tokenUrl);
        String tokenResponse = restTemplate.getForObject(tokenUrl, String.class);
        if (StringUtils.isEmpty(tokenResponse)) {
            throw new CustomException("登录失败");
        }
        log.info("tokenResponse: {}", tokenResponse);
        JsonNode tokenNode = objectMapper.readTree(tokenResponse);
        if (tokenNode == null || tokenNode.get("access_token") == null) {
            throw new CustomException("登录失败");
        }
        if (tokenNode.get("openid") == null) {
            throw new CustomException("获取微信openid失败");
        }
        String accessToken = tokenNode.get("access_token").asText();
        String openid = tokenNode.get("openid").asText();
        // 获取用户信息
        String userInfoUrl = "https://api.weixin.qq.com/sns/userinfo?access_token=" + accessToken + "&openid=" + openid;
        String userInfoResponse = restTemplate.getForObject(userInfoUrl, String.class);
        if (StringUtils.isEmpty(userInfoResponse)) {
            throw new CustomException("获取用户信息失败");
        }
        JsonNode userNode = objectMapper.readTree(userInfoResponse);
        log.info("userInfoResponse: {}", userInfoResponse);
        if (userNode == null || userNode.get("openid") == null) {
            throw new CustomException("登录失败");
        }
        return userNode;
    }

    private SysUser getSysUser(WechatUser user) throws IOException {
        // 检查系统用户是否存在
        String sql = String.format("select * from sys_user where type = 2 and site_id = %s Limit 1 ", user.getId());
        List<SysUser> sysUsers = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SysUser.class));
        SysUser sysUser;
        if (CollUtil.isEmpty(sysUsers)) {
            // 新用户注册逻辑
            sysUser = new SysUser();
            sysUser.setType(2);
            sysUser.setSiteId(user.getId());
            sysUser.setAvatar(ImageUrlToBase64WithHttpClient.urlToBase64(user.getAvatar()));
            sysUser.setStatus(1);
            sysUser.setPhone(user.getPhone());
            sysUser.setCreateUserId(1L);
            sysUser.setCreateTime(new Date());
            sysUser.setDeptId(DEPT_ID);
            sysUser.setDeleted(0);
            sysUser.setLoginName(user.getH5Openid());
            sysUser.setUserName(user.getNickname());
            sysUser.setPassword(SecurityUtils.encryptPassword(user.getH5Openid()));
            userRepository.save(sysUser);
            log.info("检查系统用户是否存在 sysUser: {}", sysUser);
            // 检查角色是否存在
            boolean roleBoo = roleRepository.existsById(ROLE_ID);
            log.info("检查角色是否存在 roleBoo: {}", roleBoo);
            if (!roleBoo) {
                SysRole role = new SysRole();
                role.setId(ROLE_ID);
                role.setRoleName("门店角色");
                role.setRoleKey("shop_wechat");
                role.setSortNum(1);
                role.setRemark("门店角色");
                role.setStatus(0);
                role.setCreateUserId(1L);
                role.setCreateTime(new Date());
                roleRepository.save(role);
                log.info("检查角色是否存在 role: {}", role);
            }

            // 新用户角色关联
            jdbcTemplate.update("insert into sys_user_role (user_id, role_id) values (?, ?)", sysUser.getId(), ROLE_ID);

            // 检查部门是否存在
            boolean deptBoo = deptRepository.existsById(DEPT_ID);
            log.info("检查部门是否存在1 dept: {}", deptBoo);
            if (!deptBoo) {
                SysDept dept = new SysDept();
                dept.setId(DEPT_ID);
                dept.setDeptName("门店部门");
                dept.setParentId(0L);
                dept.setSortNum(1);
                dept.setLeader("admin");
                dept.setPhone("1234567890");
                dept.setEmail("<EMAIL>");
                dept.setStatus(1);
                dept.setCreateUserId(1L);
                dept.setCreateTime(new Date());
                deptRepository.save(dept);

                log.info("检查部门是否存在 dept: {}", dept);
            }

            // 新用户部门关联
            jdbcTemplate.update("insert into sys_user_dept (user_id, dept_id) values (?, ?)", sysUser.getId(), DEPT_ID);
        } else {
            sysUser = sysUsers.get(0);
        }
        return sysUser;
    }

    private WechatUser getWechatUser(JsonNode userNode) {
        // 检查用户是否已存在
        List<WechatUser> users = jdbcTemplate.query("select * from dub_wechat_user where unionid = ?", new BeanPropertyRowMapper<>(WechatUser.class), userNode.get("unionid").asText());
        WechatUser user;
        if (CollUtil.isEmpty(users)) {
            // 新用户注册逻辑
            user = new WechatUser();
            user.setH5Openid(userNode.get("openid").asText());
            user.setNickname(userNode.get("nickname").asText());
            user.setAvatar(userNode.get("headimgurl").asText());
            user.setH5AuthTime(new Date());
            user.setUnionid(userNode.get("unionid").asText());
            // 其他字段...
            wechatUserRepository.save(user);
        } else {
            user = users.get(0);
        }
        return user;
    }

    // 检查登录状态
    public Map<String, Object> checkLoginStatus(String uuid) {
        WeCharUserInfo userInfo = (WeCharUserInfo) redisUtils.get(uuid);
        Map<String, Object> result = new HashMap<>();
        if (userInfo != null && userInfo.isLoggedIn()) {
            result.put("loggedIn", true);
            result.put("userInfo", userInfo);
            result.put("token", userInfo.getToken()); // 返回Token
            // 移除session
            redisUtils.del(uuid);
        } else {
            result.put("loggedIn", false);
        }
        return result;
    }

    public Map<String, Object> updateWx(HttpServletRequest request) {
        String scope = "snsapi_login"; // 网页应用使用snsapi_login
        String state = UUID.randomUUID().toString(); // 生成随机state参数
        // 将state存储在session中，用于防止CSRF攻击
//        request.getSession().setAttribute("oauth_state", state);
        String loginUrl = String.format("%s?appid=%s&redirect_uri=%s&response_type=code&scope=%s&state=%s#wechat_redirect",
                WECHAT_AUTHORIZE_URL,
                appId,
                redirectUri.concat("/user/profile"),
                scope,
                state
        );
        Map<String, Object> result = new HashMap<>();
        result.put("loginUrl", loginUrl);
        result.put("state", state);
        return result;
    }

    @Transactional
    public Map<String, Object> updateCallback(String code) throws JsonProcessingException {
        JsonNode userNode = getWxJsonNode(code);
        WechatUser wechatUser = getWechatUser(userNode);

        SysUserVo userVo = SecurityUtils.getLoginUser().getUser();
        SysUser nowUser = userRepository.getById(userVo.getId());

        SysUser wxSysUser = userRepository.getBySiteId(wechatUser.getId());
        if (wxSysUser != null && !Objects.equals(wxSysUser.getId(), nowUser.getId())) {
            userRepository.delete(wxSysUser);
            List<SysUserDept> userDepts = sysUserDeptRepository.findByUserId(wxSysUser.getId());
            sysUserDeptRepository.deleteAll(userDepts);
            List<SysUserRole> userRoles = sysUserRoleRepository.findByUserId(wxSysUser.getId());
            sysUserRoleRepository.deleteAll(userRoles);
        }
        nowUser.setSiteId(wechatUser.getId());
        nowUser.setPhone(wechatUser.getPhone());
        nowUser.setUserName(wechatUser.getNickname());
        nowUser.setAvatar(wechatUser.getAvatar());
        nowUser.setDeptId(DEPT_ID);
        nowUser.setType(2);
        userRepository.save(nowUser);

        List<SysUserDept> userDepts = sysUserDeptRepository.findByUserId(nowUser.getId());
        if (CollUtil.isNotEmpty(userDepts)) {
            sysUserDeptRepository.deleteAll(userDepts);

            SysUserDept deptUser = new SysUserDept();
            deptUser.setDeptId(DEPT_ID);
            deptUser.setUserId(DEPT_ID);
            sysUserDeptRepository.save(deptUser);
        }

        List<SysUserRole> userRoles = sysUserRoleRepository.findByUserId(nowUser.getId());
        if (CollUtil.isNotEmpty(userRoles)) {
            sysUserRoleRepository.deleteAll(userRoles);
            SysUserRole role = new SysUserRole();
            role.setUserId(nowUser.getId());
            role.setRoleId(ROLE_ID);
            sysUserRoleRepository.save(role);
        }

        LoginUser loginUser = BeanUtil.toBean(userDetailsService.loadUserByUsername(userVo.getLoginName()), LoginUser.class);
        // 生成token
        String token = tokenService.createToken(loginUser);
        // 生成JWT Token
        Map<String, Object> map = new HashMap<>();
        map.put(Constants.TOKEN, token);
        return map;

    }


    public Result getMyLongAnchor() {
        Map<String, Object> returnMap = new HashMap<>(1);
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        List<AnchorDTO> anchorVoList = longAnchorRepository.taijia(userId);
        returnMap.put("result", anchorVoList);
        return Result.success(returnMap);
    }

    public Result followAnchor(FollowAnchorReq followAnchorReq) {
        Long anchorId = followAnchorReq.getAnchorId();
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        List<FollowAnchor> vipFollowAnchorList = followAnchorRepository.findByAnchorIdAndUserId(anchorId, userId);
        if (vipFollowAnchorList.size() == 0) {
            FollowAnchor vipFollowAnchor = new FollowAnchor();
            vipFollowAnchor.setAnchorId(anchorId);
            vipFollowAnchor.setUserId(userId);
            followAnchorRepository.save(vipFollowAnchor);
        } else {
            followAnchorRepository.delete(vipFollowAnchorList.get(0));
        }
        return Result.success(null);
    }

    public Result followLongAnchor(FollowAnchorReq followAnchorReq) {
        Long anchorId = followAnchorReq.getAnchorId();
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        List<LongFollowAnchor> longFollowAnchors = longFollowAnchorRepository.findByAnchorIdAndUserId(anchorId, userId);
        if (longFollowAnchors.size() == 0) {
            LongFollowAnchor longFollowAnchor = new LongFollowAnchor();
            longFollowAnchor.setAnchorId(anchorId);
            longFollowAnchor.setUserId(userId);
            longFollowAnchorRepository.save(longFollowAnchor);
        } else {
            longFollowAnchorRepository.delete(longFollowAnchors.get(0));
        }
        return Result.success(null);
    }

    public Result followBgm(FollowBgmReq followBgmReq) {
        Long bgmId = followBgmReq.getBgmId();
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        List<UserFollowBgm> userFollowBgms = userFollowBgmRepository.findByBgmIdAndUserId(bgmId, userId);
        if (userFollowBgms.size() == 0) {
            UserFollowBgm userFollowBgm = new UserFollowBgm();
            userFollowBgm.setBgmId(bgmId);
            userFollowBgm.setUserId(userId);
            userFollowBgmRepository.save(userFollowBgm);
        } else {
            userFollowBgmRepository.delete(userFollowBgms.get(0));
        }
        return Result.success(null);
    }

    public Result getFollowBgm() {
        Map<String, Object> returnMap = new HashMap<>(1);
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        List<BackGroundMusicVo> followBgms = backgroundMusicRepository.findFollowBgm(userId);
        returnMap.put("result", followBgms);
        return Result.success(returnMap);
    }

    public Result addTextTemplate(TextTemplateAddReq textTemplateAddReq) {
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        UserTextTemplate userTextTemplate = new UserTextTemplate();
        userTextTemplate.setTextContent(textTemplateAddReq.getTextContent());
        userTextTemplate.setUserId(userId);
        if (textTemplateAddReq.getId() != null) {
            userTextTemplate.setId(textTemplateAddReq.getId());
        }
        userTextTemplateRepository.save(userTextTemplate);
        return Result.success(true);
    }

    public Result getTextTemplate() {
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        List<UserTextTemplate> userTextTemplateList = userTextTemplateRepository.findByUserId(userId);
        List<TemplateVo> templateVoList = userTextTemplateList.stream().map(obj -> {
            TemplateVo templateVo = new TemplateVo();
            templateVo.setContent(obj.getTextContent());
            templateVo.setId(obj.getId());
            return templateVo;
        }).collect(Collectors.toList());
        Map<String, Object> returnMap = new HashMap<>(1);
        returnMap.put("result", templateVoList);
        return Result.success(returnMap);
    }

    public Result delTextTemplate(TextTemplateDelReq textTemplateDelReq) {
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        userTextTemplateRepository.deleteByIdAndUserId(textTemplateDelReq.getId(), userId);
        return Result.success(true);
    }

    public Result getMyVipAnchor() {
        Map<String, Object> returnMap = new HashMap<>(1);
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        List<AnchorDTO> anchorVoList = dubAnchorRepository.sailuo(userId);
        returnMap.put("result", anchorVoList);
        return Result.success(returnMap);
    }
}
