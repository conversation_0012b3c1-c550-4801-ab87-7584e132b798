package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysLoginLog is a Querydsl query type for SysLoginLog
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysLoginLog extends EntityPathBase<SysLoginLog> {

    private static final long serialVersionUID = -1090453760L;

    public static final QSysLoginLog sysLoginLog = new QSysLoginLog("sysLoginLog");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    public final StringPath browser = createString("browser");

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final StringPath ipaddr = createString("ipaddr");

    public final StringPath loginLocation = createString("loginLocation");

    public final StringPath loginName = createString("loginName");

    public final DateTimePath<java.util.Date> loginTime = createDateTime("loginTime", java.util.Date.class);

    public final StringPath msg = createString("msg");

    public final StringPath os = createString("os");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public QSysLoginLog(String variable) {
        super(SysLoginLog.class, forVariable(variable));
    }

    public QSysLoginLog(Path<? extends SysLoginLog> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysLoginLog(PathMetadata metadata) {
        super(SysLoginLog.class, metadata);
    }

}

