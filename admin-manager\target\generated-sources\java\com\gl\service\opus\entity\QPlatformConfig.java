package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QPlatformConfig is a Querydsl query type for PlatformConfig
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QPlatformConfig extends EntityPathBase<PlatformConfig> {

    private static final long serialVersionUID = 55296134L;

    public static final QPlatformConfig platformConfig = new QPlatformConfig("platformConfig");

    public final StringPath accessKey = createString("accessKey");

    public final StringPath accessToken = createString("accessToken");

    public final StringPath appId = createString("appId");

    public final StringPath appKey = createString("appKey");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath platformType = createString("platformType");

    public final StringPath secretKey = createString("secretKey");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public QPlatformConfig(String variable) {
        super(PlatformConfig.class, forVariable(variable));
    }

    public QPlatformConfig(Path<? extends PlatformConfig> path) {
        super(path.getType(), path.getMetadata());
    }

    public QPlatformConfig(PathMetadata metadata) {
        super(PlatformConfig.class, metadata);
    }

}

