package com.gl.service.opus.compound.compound;

import com.gl.commons.enums.PlatFormStatusEnum;
import com.gl.commons.enums.PlatFormTypeEnum;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.redis.RedisService;
import com.gl.service.opus.entity.PlatformConfig;
import com.gl.service.opus.repository.PlatformConfigRepository;
import com.gl.service.opus.utils.FfmpegUtil;
import com.gl.service.opus.utils.FileUtil;
import com.gl.service.opus.utils.LineInsertUtils;
import com.microsoft.cognitiveservices.speech.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-09-13:49
 */
@Service
public class AzureDubbingStrategy implements DubbingStrategy {

    private static final Logger logger = LoggerFactory.getLogger(AzureDubbingStrategy.class);
    @Autowired
    private PlatformConfigRepository platformConfigRepository;

    @Override
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {
        Double speechRate = null;
        Double pitchRate = null;
        if (dto.getPitchRate() != null) {
            //语调，范围是-500~500，可选，默认是0
            pitchRate = LineInsertUtils.mapRange(dto.getPitchRate(), -500,
                    500, -100, 100);
        }
        if (dto.getSpeechRate() != null) {
            //语速，范围是-500~500，默认是0
            speechRate = LineInsertUtils.mapRange(dto.getSpeechRate(), -500,
                    500, 0, 2);
        }
        if (dto.getIsEmotion() == 1) {
            dto.setText("<speak version=\"1.0\" xmlns=\"http://www.w3.org/2001/10/synthesis\" xmlns:mstts=\"https://www.w3.org/2001/mstts\" xml:lang=\"zh-CN\">\n" +
                    "    <voice name=\"" + dto.getVoice() + "\" effect=\"eq_car\">\n" +
                    "        <prosody volume=\"" + dto.getVolume() + "\" pitch=\"" + pitchRate + "%\" rate=\"" + speechRate + "\">\n" +
                    "        <mstts:express-as style=\"" + dto.getEmotion() + "\" styledegree=\"2\">\n" +
                    dto.getText() +
                    "        </mstts:express-as>\n" +
                    "        </prosody>\n" +
                    "    </voice>\n" +
                    "</speak>");
        } else {
            dto.setText("<speak version=\"1.0\" xmlns=\"http://www.w3.org/2001/10/synthesis\" xml:lang=\"zh-CN\">\n" +
                    "    <voice name=\"" + dto.getVoice() + "\" effect=\"eq_car\">\n" +
                    "        <prosody volume=\"" + dto.getVolume() + "\" pitch=\"" + pitchRate + "%\" rate=\"" + speechRate + "\">\n" +
                    dto.getText() +
                    "        </prosody>\n" +
                    "    </voice>\n" +
                    "</speak>");
        }
        logger.info("azure合成中");
        String url = "https://eastasia.tts.speech.microsoft.com/cognitiveservices/v1";
        List<PlatformConfig> platformConfigs = platformConfigRepository.findByPlatformTypeAndStatus(PlatFormTypeEnum.AUZRE_AIDUBBING.name(),
                PlatFormStatusEnum.ENABLED.ordinal());
        PlatformConfig platformConfig = platformConfigs.get(0);
        HttpPost request = new HttpPost(url);
        request.setHeader("Content-type", "application/ssml+xml");
        request.setHeader("Ocp-Apim-Subscription-Key", platformConfig.getSecretKey());
        request.setHeader("X-Microsoft-OutputFormat", "audio-24khz-96kbitrate-mono-mp3");
        request.setHeader("User-Agent", "curl");
        StringEntity entity = new StringEntity(dto.getText(), "UTF-8");
        request.setEntity(entity);
        CloseableHttpResponse response = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() == org.apache.http.HttpStatus.SC_OK) {
                File source = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
                byte[] audioBytes = EntityUtils.toByteArray(response.getEntity());
                try (OutputStream os = new FileOutputStream(source)) {
                    os.write(audioBytes);
                }
                File file = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
                if (!StringUtils.isEmpty(dto.getBgm())) {
                    String bgmFileName = dto.getUrl() + FileUtil.getFileNewName(".wav");
                    File bgmFile = FileUtil.taiseng(dto.getBgm(), bgmFileName, dto.getBugRate());
                    File outFile = new File(dto.getUrl() + FileUtil.getFileNewName(".wav"));
                    FfmpegUtil.mixBgm(source, bgmFile, outFile, dto.getBeforeDelay(), dto.getAfterDelay(), dto.getBgmCenterVolum());
                    if (dto.getIsHeighVoice() == 1) {
                        FileUtil.coverToMp3Heigh(outFile, file);
                    } else {
                        FileUtil.coverToMp3(outFile, file);
                    }
                } else {
                    if (dto.getIsHeighVoice() == 1) {
                        FileUtil.coverToMp3Heigh(source, file);
                    } else {
                        FileUtil.coverToMp3(source, file);
                    }
                }
                return file.getAbsolutePath();
            } else {
                throw new Exception("azure合成失败");
            }
        }
    }
}
