package com.gl.service.installationPackage.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.installationPackage.service.InstallationPackageService;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageAddDto;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/***
 * 安装包管理
 */
@Controller
@RequestMapping("/installationPackage")
public class InstallationPackageController {

    @Autowired
    private InstallationPackageService installationPackageService;

    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:installationPackage:list')")
    public Result list(InstallationPackageDto dto) {
        return installationPackageService.list(dto, 1);
    }


    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:installationPackage:add')")
    public Result addOrUpdate(@RequestBody InstallationPackageAddDto vo){
        return installationPackageService.addOrUpdate(vo);
    }

    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:installationPackage:delete')")
    public Result delete(@RequestBody InstallationPackageAddDto dto){
        return installationPackageService.delete(dto);
    }






}
