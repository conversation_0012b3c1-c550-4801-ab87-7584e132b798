package com.gl.framework.web.response;

import java.util.HashMap;
import java.util.Map;

public class Result {

    private int code = ResultCode.SUCCESS.getCode();
    private String message = "";
    private Long timestamp;
    private Object data;

    private Map<String, Object> map = null;

    private static Result result = null;

    private Result() {

    }

    public static Result success() {
        Result result = new Result();
        result.setMessage("success");
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    public static Result success(Object o) {
        Result result = new Result();
        result.setMessage("success");
        result.setTimestamp(System.currentTimeMillis());
        result.setData(o);
        return result;
    }

    public static Result fail() {
        Result result = new Result();
        result.setCode(ResultCode.FAIL.getCode());
        result.setMessage(ResultCode.FAIL.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    public static Result fail(String message) {
        Result result = new Result();
        result.setCode(ResultCode.FAIL.getCode());
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    public static Result fail(Integer errorCode, String msg) {
        Result result = new Result();
        result.setCode(errorCode == null ? ResultCode.FAIL.getCode() : errorCode);
        result.setMessage(msg);

        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    public static Result fail(Object o) {
        Result result = new Result();
        result.setCode(ResultCode.FAIL.getCode());
        result.setMessage(ResultCode.FAIL.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        result.setData(o);
        return result;
    }

    public void addData(String key, Object o) {
        if (this.map == null) {
            this.map = new HashMap<String, Object>();
            setData(map);
        }
        this.map.put(key, o);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
