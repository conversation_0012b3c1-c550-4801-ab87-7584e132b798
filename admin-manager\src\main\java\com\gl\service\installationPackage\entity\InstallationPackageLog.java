package com.gl.service.installationPackage.entity;

import lombok.Data;
import javax.persistence.*;
import java.util.Date;


@Data
@Entity
@Table(name = "dub_installation_package_log")
public class InstallationPackageLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Basic
    @Column(name = "device_id")
    private Long deviceId;

    @Basic
    @Column(name = "package_id")
    private Long packageId;

    @Basic
    @Column(name = "create_time")
    private Date createTime;

    @Basic
    @Column(name = "response_time")
    private Date responseTime;

    @Basic
    @Column(name = "status")
    private Integer status;

}
