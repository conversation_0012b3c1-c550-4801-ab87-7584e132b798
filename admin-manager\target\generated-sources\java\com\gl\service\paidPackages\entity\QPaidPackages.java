package com.gl.service.paidPackages.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QPaidPackages is a Querydsl query type for PaidPackages
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QPaidPackages extends EntityPathBase<PaidPackages> {

    private static final long serialVersionUID = 145527972L;

    public static final QPaidPackages paidPackages = new QPaidPackages("paidPackages");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> createUserId = createNumber("createUserId", Long.class);

    public final NumberPath<Integer> dataNum = createNumber("dataNum", Integer.class);

    public final NumberPath<Integer> effectDay = createNumber("effectDay", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath name = createString("name");

    public final NumberPath<java.math.BigDecimal> sellingPrice = createNumber("sellingPrice", java.math.BigDecimal.class);

    public QPaidPackages(String variable) {
        super(PaidPackages.class, forVariable(variable));
    }

    public QPaidPackages(Path<? extends PaidPackages> path) {
        super(path.getType(), path.getMetadata());
    }

    public QPaidPackages(PathMetadata metadata) {
        super(PaidPackages.class, metadata);
    }

}

