package com.gl.service.basis.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * 关于我们
 * @author: duanjinze
 * @date: 2022/11/11 15:47
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_base_about")
public class BaseAbout {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 内容
     */
    @Basic
    @Column(name = "content",columnDefinition = "text")
    private String content;
}
