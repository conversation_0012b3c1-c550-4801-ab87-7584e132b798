package com.gl.util;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

@Slf4j
public class TableDateUtils {

    public static String[] getWeekFirstDay() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");

        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setMinimalDaysInFirstWeek(7);

        String[] firstWeekDay = new String[5];
        for (int i = 0; i < 5; i++) {

            calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());

            firstWeekDay[i] = format.format(calendar.getTime());
            log.debug("生成每周第一天,日期为{}",firstWeekDay[i]);

            calendar.add(Calendar.DAY_OF_YEAR, 7);
        }

        return firstWeekDay;
    }

    public static String[] getMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");

        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        String[] month = new String[5];
        for (int i = 0; i < 5; i++) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            month[i] = format.format(calendar.getTime());
            log.debug("生成月份,月份为{}",month[i]);

            calendar.add(Calendar.MONTH, 1);
        }

        return month;
    }
}
