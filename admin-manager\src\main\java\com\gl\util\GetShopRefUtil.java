package com.gl.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.gl.commons.enums.UserTypeEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.service.shop.entity.Shop;
import com.gl.service.shop.repository.ShopRepository;
import com.gl.service.shop.repository.ShopUserRefRepository;
import com.gl.system.vo.SysUserVo;
import com.gl.wechat.entity.WechatUser;
import com.gl.wechat.repository.WechatUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetShopRefUtil {

    @Resource
    private ShopUserRefRepository shopUserRefRepository;

    @Resource
    private ShopRepository shopRepository;

    @Resource
    private WechatUserRepository wechatUserRepository;


    public static boolean isWeChatUser() {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        return ObjectUtil.equal(user.getType(), 2);
    }

    /**
     * 微信用户 且 绑定门店为空 应该不用查询数据
     *
     * @return
     */
    public boolean isNeedWxFilter() {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        if (ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType())) {
            //微信用户，需要通过关联门店过滤数据
            return CollUtil.isEmpty(getShopRef());
        }
        return false;
    }

    /**
     * 获取用户所有门店
     *
     * @return
     */
    public List<Long> getShopRef() {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        List<Long> shopIds = new ArrayList<>();
        if (ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType())) { //微信用户，需要通过关联门店过滤数据
            log.info("微信用户，需要通过关联门店过滤数据 siteId = {}", user.getSiteId());
            List<Long> shopIdList = shopUserRefRepository.getShopUserRefByUserId(user.getSiteId());
            if (CollUtil.isEmpty(shopIdList)) {
                return Collections.emptyList();
            }

            // 查询这些店铺作为"总店"时所包含的所有分店ID
            List<Long> branchIds = shopRepository.selectByParentIdIn(shopIds).stream().map(Shop::getId).collect(Collectors.toList());
            if (CollUtil.isEmpty(branchIds)) {
                return shopIdList;
            }
            // 合并并去重
            shopIds.addAll(branchIds);
            return shopIds.stream().distinct().collect(Collectors.toList());
        }
        return shopIds;
    }

    /**
     * 获取用户的绑定的门店
     *
     * @return
     */
    public Long getOneShop() {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        if (ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType())) { //微信用户，需要通过关联门店过滤数据
            List<Long> shopList = shopUserRefRepository.getShopUserRefByUserId(user.getSiteId());
            if (CollUtil.isNotEmpty(shopList)) {
                return shopList.get(0);
            }
        }
        return null;
    }


    public Long getWxUserId() {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        return user.getSiteId();
    }

    public WechatUser getWxUser() {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        if (ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType()) && user.getSiteId() != null) {
            return wechatUserRepository.getById(user.getSiteId());
        }
        return null;
    }

    public Shop getShop() {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        if (ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType()) && user.getSiteId() != null) {
            List<Long> shopId = shopUserRefRepository.getShopUserRefByUserId(user.getSiteId());
            if (shopId != null) {
                return shopRepository.getById(shopId.get(0));
            }
        }
        return null;
    }
}
