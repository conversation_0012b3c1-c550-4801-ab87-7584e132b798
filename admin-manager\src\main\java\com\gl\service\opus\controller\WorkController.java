package com.gl.service.opus.controller;

import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.opus.entity.AiClass;
import com.gl.service.opus.service.WorkService;
import com.gl.service.opus.vo.MergeVo;
import com.gl.service.opus.vo.SendWorkMusicParams;
import com.gl.service.opus.vo.VoiceWorkVo;
import com.gl.service.opus.vo.dto.WorkDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 作品管理
 *
 * @author: duanjinze
 * @date: 2022/11/10 17:37
 * @version: 1.0
 */
@Controller
@RequestMapping("/work")
public class WorkController {
    @Autowired
    private WorkService workService;

    /**
     * 作品列表
     *
     * @param dto
     * @return
     */
    @GetMapping
    @ResponseBody
    public Result list(WorkDto dto) {
        return workService.list(dto, 1);
    }

    /**
     * 新增作品
     *
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    public Result add(@RequestBody VoiceWorkVo vo) {
        vo.setUserId(SecurityUtils.getLoginUser().getUser().getSiteId());
        return workService.add(vo);
    }

    /**
     * 上传作品
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/uploadWork")
    @ResponseBody
    public Result uploadWork(@RequestBody VoiceWorkVo vo) {
        vo.setUserId(SecurityUtils.getLoginUser().getUser().getSiteId());
        return workService.uploadWork(vo);
    }

    /**
     * 发送作品到设备
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/sendWorkMusic")
    @ResponseBody
    public Result sendWorkMusic(@RequestBody SendWorkMusicParams vo) {
        return workService.sendWorkMusic(vo);
    }


    /**
     * 编辑作品
     *
     * @param vo
     * @return
     */
    @PutMapping
    @ResponseBody
    public Result update(@RequestBody VoiceWorkVo vo) {
        return workService.update(vo);
    }

    /**
     * 可批量删除
     *
     * @param dto
     * @return
     */
    @DeleteMapping
    @ResponseBody
    public Result delete(@RequestBody WorkDto dto) {
        return workService.delete(dto);
    }

    /**
     * 导出作品列表
     *
     * @param dto
     * @param response
     * @throws IOException
     */
    @PostMapping("/export")
    @ResponseBody
    public void exportList(@RequestBody WorkDto dto, HttpServletResponse response) throws IOException {
        workService.exportList(dto, response);
    }

    /**
     * 模板列表
     *
     * @return
     */
    @GetMapping("/templates")
    @ResponseBody
    public Result findTemplates() {
        return workService.findTemplates();
    }

    /**
     * 主播列表
     *
     * @return
     */
    @GetMapping("/anchors")
    @ResponseBody
    public Result findAnchors(@RequestParam(name = "searchCondition", required = false) String searchCondition) {
        return workService.findAnchors(searchCondition);
    }

    @GetMapping("/longAnchors")
    @ResponseBody
    public Result findLongAnchors(@RequestParam(name = "searchCondition", required = false) String searchCondition) {
        return workService.findLongAnchors(searchCondition);
    }
    /**
     * 背景音乐列表
     *
     * @return
     */
    @GetMapping("/backgroundMusic")
    @ResponseBody
    public Result findBackgroundMusic() {
        return workService.findBackgroundMusic();
    }

    /**
     * 微信用户列表
     *
     * @return
     */
    @GetMapping("/wechatUsers")
    @ResponseBody
    public Result findWechatUsers(@RequestParam(name = "searchCondition", required = false) String searchCondition) {
        return workService.findWechatUsers(searchCondition);
    }

    /**
     * 点击试听
     */
    @PostMapping("/audition")
    @ResponseBody
    public Result audition(@RequestBody SpeechSynthesizerDto dto) {
        Long siteId = SecurityUtils.getLoginUser().getUser().getSiteId();
        dto.setUserId(siteId);
        return workService.audition(dto);
    }

    @GetMapping(value = "/getWorkList")
    @ResponseBody
    public Result getWorkList() {
        return workService.getWorkList();
    }


    @PostMapping("longCompound")
    @ResponseBody
    private Result longCompound(@RequestBody SpeechSynthesizerDto dto) {
        Long siteId = SecurityUtils.getLoginUser().getUser().getSiteId();
        dto.setUserId(siteId);
        return workService.longCompound(dto);
    }

    @PostMapping("dialog")
    @ResponseBody
    private Result dialog(@RequestBody SpeechSynthesizerDto dto) {
        return workService.dialog(dto);
    }

    /**
     * 多人配音
     */
    @PostMapping("mergeAudio")
    @ResponseBody
    public Result mergeAudio(@RequestBody MergeVo mergeVo) {
        return workService.mergeAudio(mergeVo);
    }
    @GetMapping("aiStyleList")
    @ResponseBody
    public Result aiStyleList() {
        return workService.aiStyleList();
    }

    @GetMapping("aiLanguageList")
    @ResponseBody
    public Result aiLanguageList() {
        return workService.aiLanguageList();
    }

    @GetMapping("aiClassList")
    @ResponseBody
    public Result aiClassList() {
        return workService.aiClassList();
    }

    @PostMapping("uploadVideo")
    @ResponseBody
    public Result uploadVideo(@RequestPart("file") MultipartFile file, String name) {
        return workService.uploadVideo(file, name);
    }

    @GetMapping("myMusic")
    @ResponseBody
    public Result myMusic() {
        return workService.myMusic();
    }

    @GetMapping("delMyMusic/{musicId}")
    @ResponseBody
    public Result delMyMusic(@PathVariable("musicId") Long musicId) {
        return workService.delMyMusic(musicId);
    }
}
