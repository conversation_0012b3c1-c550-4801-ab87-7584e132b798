package com.gl.framework.config;

import com.gl.framework.exception.handler.GlobalExceptionHandler;
import com.gl.framework.filter.RepeatableFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Filter配置
 */
@Configuration
public class FilterConfig {

	@Bean
	public FilterRegistrationBean repeatableFilterRegistration() {
		FilterRegistrationBean registration = new FilterRegistrationBean();
		registration.setFilter(new RepeatableFilter()); // 重复提交过滤器
		registration.addUrlPatterns("/*");
		registration.setName("repeatableFilter");
		registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
		registration.setEnabled(false);
		return registration;
	}
}
