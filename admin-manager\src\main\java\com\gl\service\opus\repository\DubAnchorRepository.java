package com.gl.service.opus.repository;

import com.gl.service.opus.dto.AnchorDTO;
import com.gl.service.opus.entity.DubAnchor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DubAnchorRepository extends JpaRepository<DubAnchor,Long>, JpaSpecificationExecutor<DubAnchor> {
    @Query(value = "select new com.gl.service.opus.dto.AnchorDTO(v.id,v.name,v.usageScenario,v.typeName,v.url," +
            "v.voiceUrl,v.archorTag) " +
            "from DubAnchor v join FollowAnchor vf on v.id = vf.anchorId where vf.userId=:userId")
    List<AnchorDTO> sailuo(Long userId);
}
