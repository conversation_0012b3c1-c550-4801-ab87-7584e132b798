//package com.gl.wechat;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Map;
//
//@RestController
//@RequestMapping("/api/wechat")
//public class WeChatController {
//
//    @Autowired
//    private WeChatService weChatService;
//
//    // 前端请求二维码
//    @GetMapping("/qrcode")
//    public Map<String, String> getQRCode() throws Exception {
//        return weChatService.getQRCodeTicket();
//    }
//
//    // 微信回调接口
//    @GetMapping("/callback")
//    public Map<String, Object> callback(@RequestParam("code") String code, @RequestParam("state") String state) throws Exception {
//        return weChatService.handleCallback(code, state);
//    }
//
//    @GetMapping("/check-login/{uuid}")
//    public Map<String, Object> checkLoginStatus(@PathVariable String uuid) {
//
//        return weChatService.checkLoginStatus(uuid);
//    }
//}
