package com.gl.service.device.repository;

import com.gl.service.opus.entity.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface DeviceRepository extends JpaRepository<Device, Long>, JpaSpecificationExecutor<Device> {
    @Transactional
    @Modifying
    @Query(value = "update dub_device set del_status = 1 where id = ?1 ", nativeQuery = true)
    Integer updateDelStatusById(Long id);

    @Transactional
    @Modifying
    @Query(value = "update dub_device set use_status = ?2 where id = ?1 ", nativeQuery = true)
    Integer updateUseStatusById(Long id, Integer useStatus);

    @Transactional
    @Modifying
    @Query(value = "update dub_device set status = ?2,update_status_time = ?3 where id = ?1 ", nativeQuery = true)
    Integer updateStatusById(Long id, Integer status, Date updateStatusTime);

    @Transactional
    @Modifying
    @Query(value = "update dub_device set status = ?2,update_status_time = ?3 where sn = ?1 ", nativeQuery = true)
    Integer updateStatusBySn(String sn, Integer status, Date updateStatusTime);

    @Transactional
    @Modifying
    @Query(value = "delete from dub_device where shop_id in ?1", nativeQuery = true)
    void deleteByShopIdIn(List<Long> shopIds);

    Device findDeviceBySn(String deviceSn);
}
