package com.gl.service.template.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.template.service.TemplateService;
import com.gl.service.template.vo.TemplateVo;
import com.gl.service.template.vo.dto.TemplateDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 模板管理
 *
 * @author: duanjinze
 * @date: 2022/11/11 11:42
 * @version: 1.0
 */
@Controller
@RequestMapping("/template")
public class TemplateController {

    @Autowired
    private TemplateService templateService;

    /**
     * 模板列表
     *
     * @param dto
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('template:template:list')")
    public Result list(TemplateDto dto) {
        return templateService.list(dto, 1);
    }

    /**
     * 模板列表导出
     *
     * @param dto
     * @param response
     * @throws IOException
     */
    @PostMapping("/export")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('template:template:export')")
    public void exportList(@RequestBody TemplateDto dto, HttpServletResponse response) throws IOException {
        templateService.exportList(dto, response);
    }

    /**
     * 新增和修改模板
     *
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('template:template:addorupdate')")
    public Result addAndUpdate(@RequestBody TemplateVo vo) {
        return templateService.addAndUpdate(vo);
    }

    /**
     * 删除模板
     *
     * @param dto
     * @return
     */
    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('template:template:delete')")
    public Result delete(@RequestBody TemplateDto dto) {
        return templateService.delete(dto);
    }

    /**
     * 模板类型下拉框列表
     *
     * @return
     */
    @GetMapping("/type")
    @ResponseBody
    public Result findTemplateType() {
        return templateService.findTemplateType();
    }
}
