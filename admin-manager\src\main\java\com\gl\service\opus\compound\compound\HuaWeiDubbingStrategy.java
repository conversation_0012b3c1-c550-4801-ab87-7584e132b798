package com.gl.service.opus.compound.compound;


import com.gl.commons.enums.PlatFormStatusEnum;
import com.gl.commons.enums.PlatFormTypeEnum;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.redis.RedisService;
import com.gl.service.opus.entity.PlatformConfig;
import com.gl.service.opus.repository.PlatformConfigRepository;
import com.gl.service.opus.utils.FfmpegUtil;
import com.gl.service.opus.utils.FileUtil;
import com.gl.service.opus.utils.StopUtils;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.sis.v1.*;
import com.huaweicloud.sdk.sis.v1.model.*;
import com.huaweicloud.sdk.sis.v1.region.SisRegion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Base64;
import java.util.List;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-09-13:49
 */
@Service
public class HuaWeiDubbingStrategy implements DubbingStrategy {
    @Autowired
    private PlatformConfigRepository platformConfigRepository;
    @Override
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {
        List<PlatformConfig> platformConfigs = platformConfigRepository.findByPlatformTypeAndStatus(PlatFormTypeEnum.HUAWEI_DUBBING.name(),
                PlatFormStatusEnum.ENABLED.ordinal());
        PlatformConfig platformConfig = platformConfigs.get(0);
        String accessKey = platformConfig.getAccessKey();
        String secretKey = platformConfig.getSecretKey();
        if (dto.getText().indexOf("<break") != -1) {
            dto.setText(StopUtils.clearBreak(dto.getText()));
        }
        ICredential auth = new BasicCredentials()
                .withAk(accessKey)
                .withSk(secretKey);
        SisClient client = SisClient.newBuilder()
                .withCredential(auth)
                .withRegion(SisRegion.valueOf("cn-east-3"))
                .build();
        RunTtsRequest request = new RunTtsRequest();
        PostCustomTTSReq body = new PostCustomTTSReq();
        TtsConfig configbody = new TtsConfig();
        configbody.withAudioFormat(TtsConfig.AudioFormatEnum.fromValue("wav"))
                .withSampleRate(TtsConfig.SampleRateEnum.fromValue("16000"))
                .withProperty(TtsConfig.PropertyEnum.fromValue(dto.getVoice()))
                .withSpeed(dto.getSpeechRate())
                .withPitch(dto.getPitchRate())
                .withVolume(dto.getVolume());
        body.withConfig(configbody);
        body.withText(dto.getText());
        request.withBody(body);
        File source = new File(dto.getUrl() + FileUtil.getFileNewName(".wav"));
        try (FileOutputStream fos = new FileOutputStream(source)) {
            RunTtsResponse response = client.runTts(request);
            String data = response.getResult().getData();
            byte[] decodedBytes = Base64.getDecoder().decode(data);
            fos.write(decodedBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        File file = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
        if (!StringUtils.isEmpty(dto.getBgm())) {
            String bgmFileName = dto.getUrl() + FileUtil.getFileNewName(".wav");
            File bgmFile = FileUtil.taiseng(dto.getBgm(), bgmFileName, dto.getBugRate());
            File outFile = new File(dto.getUrl() + FileUtil.getFileNewName(".wav"));
            FfmpegUtil.mixBgm(source, bgmFile, outFile, dto.getBeforeDelay(), dto.getAfterDelay(), dto.getBgmCenterVolum());
            if (dto.getIsHeighVoice() == 1) {
                FileUtil.coverToMp3Heigh(outFile, file);
            } else {
                FileUtil.coverToMp3(outFile, file);
            }
        } else {
            if (dto.getIsHeighVoice() == 1) {
                FileUtil.coverToMp3Heigh(source, file);
            } else {
                FileUtil.coverToMp3(source, file);
            }
        }
        return file.getAbsolutePath();
    }
}
