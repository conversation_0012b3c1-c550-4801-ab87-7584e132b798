package com.gl.service.template.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: duanjinze
 * @date: 2022/11/11 11:00
 * @version: 1.0
 */
@Data
public class TemplateVo {
    private Long id;
    /**
     * 模板类型id
     */
    private Long templateTypeId;

    /**
     * 模板分类
     */
    private String templateTypeName;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private String shopName;

    private Long shopId;
}
