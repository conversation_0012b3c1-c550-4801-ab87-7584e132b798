package com.gl.service.user.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * @author: duanjinze
 * @date: 2022/11/11 14:58
 * @version: 1.0
 */
@Data
public class ExcelWechatUser {
    /**
     * 头像
     */
    @ExcelProperty(value = "头像",index = 0)
    @ColumnWidth(40)
    private String avatar;

    /**
     * 昵称
     */
    @ExcelProperty(value = "昵称",index = 1)
    @ColumnWidth(20)
    private String nickname;

    /**
     * 手机
     */
    @ExcelProperty(value = "手机",index = 2)
    @ColumnWidth(20)
    private String phone;

    /**
     * 0未知 1男 2女
     */
    @ExcelProperty(value = "性别",index = 3)
    @ColumnWidth(20)
    private String gender;

    /**
     * 地区
     */
    @ExcelProperty(value = "地区",index = 4)
    @ColumnWidth(20)
    private String area;

    /**
     * 授权时间
     */
    @ExcelProperty(value = "授权时间",index = 5)
    @ColumnWidth(20)
    private String authTime;
}
