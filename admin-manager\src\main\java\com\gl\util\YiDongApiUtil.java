package com.gl.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;


/**
 *<AUTHOR> JAVA请求API服务平台接口demo CMIOT_API23U07-单卡本月套餐流量用量实时查询
 * 成功：
 * {
 * 	"status": "0"，
 *     "message": "正确",
 *     "result": [
 * 		        {
 * 			"accmMarginList": [
 *                {
 * 					"offeringId": "21000032",
 * 					"offeringName": "全国通用流量8元套餐",
 * 					"totalAmount": "102400.00",
 * 					"useAmount": "15186.00",
 * 					"remainAmount": "87214.00"
 *                },...
 *             ]
 *        }
 *     ]
 * }
 * 失败：
 * {
 *     "message": "TOKEN不存在或已过期，请重新获取",
 *     "result": [],
 *     "status": "12021"
 * }
 *
 */
@Slf4j
public class YiDongApiUtil {


    // 请求URL的IP和端口，需要按实际环境修改
    public static final String ipAndPort = "https://api.iot.10086.cn";
    private static final String APPID = "C5010571019999571017066";
    private static final String PASS_WORD = "3DN@anGSYW92";


    // -- 沙箱
//    public static final String ipAndPort = "http://*************:7000";
//    private static final String APPID = "002999";
//    private static final String PASS_WORD = "@cBQ7loux@TM";

    // 请求的版本号
    public static final String version = "/v5";


    /**
     * 获取请求接口的返回信息
     *
     * @param url 请求接口时需要传入的URL
     * @return
     */
    public static YdResult sendRequest(String url) {
        InputStream inputStream = null;
        BufferedReader bufferedReader = null;
        HttpURLConnection httpURLConnection = null;
        try {
            URL requestURL = new URL(url);
            // 获取连接
            httpURLConnection = (HttpURLConnection) requestURL.openConnection();
            httpURLConnection.setConnectTimeout(10000);	//建立连接的超时时间，毫秒
            httpURLConnection.setReadTimeout(25000);	//获得返回的超时时间，毫秒
            httpURLConnection.setRequestMethod("GET");

            // 通过输入流获取请求的内容
            inputStream = httpURLConnection.getInputStream();
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String temp;
            StringBuilder stringBuffer = new StringBuilder();
            // 循环读取返回的结果
            while ((temp = bufferedReader.readLine()) != null) {
                stringBuffer.append(temp);
            }
            return JSONObject.parseObject(stringBuffer.toString(), YdResult.class);
//            return JSONObject.parseObject(stringBuffer.toString(), YiDongApiUtil.YdResult.class);
        } catch (Exception e) {
//            e.printStackTrace();
            log.error("请求失败- {}", e.getMessage());
        }finally {
            //断开连接
            if (httpURLConnection!=null) {
                httpURLConnection.disconnect();
            }
            // 关闭流
            if(bufferedReader!=null){
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }

            }
            if(inputStream!=null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;

    }

    /**
     * 构造请求地址URL
     * @param paramMap 请求参数
     * @return URL
     */
    private static String buildUrl(Map<String, String> paramMap, String apiName){
        String url = null;
        StringBuilder urlString = new StringBuilder();
        urlString.append(ipAndPort).append(version).append(apiName);
        if (!paramMap.isEmpty()) {
            // 参数列表不为空，地址尾部增加'?'
            urlString.append('?');
            // 拼接参数
            Set<Map.Entry<String, String>> entrySet = paramMap.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                try {
                    urlString.append(entry.getKey()).append('=').append(URLEncoder.encode(entry.getValue(), "UTF-8")).append('&');
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
            // 去掉最后一个字符“&”
            url = urlString.substring(0, urlString.length() - 1);
        }
        log.info("请求地址：{}", url);
        return url;
    }

    /**
     * 获取token
     * @return
     */
    private static String getToken() {
        String token = null;
        String apiName = "/ec/get/token";
        // 封装请求接口的参数，需要按实际环境修改
        HashMap<String, String> map = new HashMap<>();
        map.put("appid", APPID);
        map.put("password", PASS_WORD);
        map.put("transid", getTransid());
        // 通过参数构造请求URL和参数
        String url = buildUrl(map, apiName);
        // 获取接口返回信息
        YdResult result = sendRequest(url);
        if (result != null && result.isSuccess()) {
            Object data = result.getResult();
            if(data != null){
                JSONArray arr = JSONArray.parseArray(data.toString());
                token = arr.getJSONObject(0).getString("token");
            }
        }
        log.error("获取移动TOKEN result = {}", result);
        if(StringUtils.isEmpty(token)){
            throw new IllegalArgumentException("获取移动TOKEN失败");
        }
        return token;
    }

    /**
     * 事务编码，由物联卡集团客户按照相应规则自主生成。
     * 生成规则：APPID+YYYYMMDDHHMISS+8位数字序列（此序列由集团客户自主生成，比如从00000001开始递增等等），
     * transid样例：1000599992700000062014101615303080000001
     * @return
     */
    private static String getTransid() {
        return APPID.concat(TransIdGenerator.generateTransId());
    }

    private static YdResult getSimDataMargin(String apiName, String msisdn, String iccid, String imsi, String groupId)  {
        // 封装请求接口的参数，需要按实际环境修改
        HashMap<String, String> map = new HashMap<>();
        map.put("transid", getTransid());
        map.put("token", getToken());
        // msisdn、imsi、iccid 三个参数有且仅有一个即可
        if(StringUtils.isNotEmpty(msisdn)){
            map.put("msisdn", msisdn);
        }else if(StringUtils.isNotEmpty(imsi)){
            map.put("imsi", imsi);
        }else if(StringUtils.isNotEmpty(iccid)){
            map.put("iccid", iccid);
        }
        if(StringUtils.isNotEmpty(groupId)){
            map.put("groupId", groupId);
        }
        // 通过参数构造请求URL和参数
        String url = buildUrl(map, apiName);
        // 获取接口返回信息
        YdResult result = sendRequest(url);
        log.info(" 获取接口返回信息 result = {}", result);
        if (result != null && result.isSuccess()){
            return result;
        }else{
            throw new IllegalArgumentException("移动API请求失败");
        }
    }

    /**
     * CMIOT_API23U07-单卡本月套餐流量用量实时查询
     * msisdn、imsi、iccid 三个参数有且仅有一个即可
     * @param msisdn 所查询的物联卡号码，最长13位数字，举例：14765004176。
     *               iccid 集成电路卡识别码，IC卡的唯一识别号码，共有20位字符组成，举例：898600D6991330004146。
     *               imsi 国际移动用户识别码，其总长度不超过15位，使用0~9的数字，举例：460079650004176。
     */
    public static void U07Inpool(String msisdn, String iccid, String imsi){
        if(StringUtils.isEmpty(msisdn) && StringUtils.isEmpty(iccid) && StringUtils.isEmpty(imsi)){
            throw new IllegalArgumentException("msisdn、imsi、iccid 三个参数有且仅有一个即可");
        }
        YdResult result = getSimDataMargin("/ec/query/sim-data-margin", msisdn, iccid, imsi, null);
    }

    /**
     * CMIOT_API23U12-单卡流量池内使用量实时查询
     * @param msisdn
     * @param iccid
     * @param imsi
     */
    public static void U12Inpool(String msisdn, String iccid, String imsi){
        if(StringUtils.isEmpty(msisdn) && StringUtils.isEmpty(iccid) && StringUtils.isEmpty(imsi)){
            throw new IllegalArgumentException("msisdn、imsi、iccid 三个参数有且仅有一个即可");
        }
        YdResult result = getSimDataMargin("/ec/query/sim-data-usage-inpool", msisdn, iccid, imsi, null);
    }

    /**
     * CMIOT_API23U00-群组本月流量累计使用量实时查询
     * @param groupId
     */
    public static void U00Inpool(String groupId){
        if(StringUtils.isEmpty(groupId)){
            throw new IllegalArgumentException("groupId不能为空");
        }
        YdResult result = getSimDataMargin("/ec/query/group-data-usage", null, null, null, groupId);
    }

    /**
     * 可以用沙箱测试。
     * 测试 msisdn = 1440292201313
     * @param args
     */
    public static void main(String[] args) {
        U07Inpool("", "" ,"");
//        U12Inpool("1442113687805", "" ,"");
//        U00Inpool("460044922000866");
    }



    @Data
    static class YdResult{
        private Integer status;
        private String message;
        private Object result;

        public Boolean isSuccess(){
            return status == 0;
        }
    }
}

