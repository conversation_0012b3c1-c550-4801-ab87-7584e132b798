package com.gl.system.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 用户信息导出VO
 */
public class UserExportVo {

    /**
     * 登录账号
     */
    @ColumnWidth(15)
    @ExcelProperty("登录账号")
    private String loginName;

    /**
     * 用户姓名
     */
    @ColumnWidth(15)
    @ExcelProperty("用户姓名")
    private String userName;

    /**
     * 部门
     */
    @ColumnWidth(15)
    @ExcelProperty("部门")
    private String deptName;

    /**
     * 邮箱
     */
    @ColumnWidth(15)
    @ExcelProperty("邮箱")
    private String email;

    /**
     * 手机号码
     */
    @ColumnWidth(15)
    @ExcelProperty("手机号码")
    private String phone;

    /**
     * 用户性别（0男 1女 2未知）
     */
    @ColumnWidth(10)
    @ExcelProperty("性别")
    private String gender;

    /**
     * 帐号状态（0正常 1停用）
     */
    @ColumnWidth(10)
    @ExcelProperty("状态")
    private String status;

    /**
     * 创建人
     */
    @ColumnWidth(15)
    @ExcelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ColumnWidth(20)
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
