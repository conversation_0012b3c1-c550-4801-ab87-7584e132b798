package com.gl.service.message.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 客服信息
 * @author: duanjinze
 * @date: 2022/11/11 17:02
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseServiceMessageVo extends UserVo {
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 内容
     */
    private String content;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date messageTime;

    //===================//
    /**
     * 头像
     */
    private String avatar;
}
