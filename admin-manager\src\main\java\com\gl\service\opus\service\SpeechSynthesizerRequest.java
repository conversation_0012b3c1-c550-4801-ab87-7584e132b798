package com.gl.service.opus.service;

import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.framework.common.util.StringUtils;
import com.gl.redis.RedisService;
import com.gl.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Date;

public class SpeechSynthesizerRequest {
    private static final String speechUrl = "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1";

    private static final Logger logger = LoggerFactory.getLogger(SpeechSynthesizerRequest.class);

    private final NlsClient client;

    private final SpeechSynthesizerDto speechSynthesizerDto;

    public SpeechSynthesizerRequest(SpeechSynthesizerDto speechSynthesizerDto, RedisService redisService) {
        this.speechSynthesizerDto = speechSynthesizerDto;
        //TODO 重要提示 创建NlsClient实例,应用全局创建一个即可,生命周期可和整个应用保持一致,默认服务地址为阿里云线上服务地址
        String token = redisService.getVoiceToken();
        if(StringUtils.isEmpty(token)){
            AccessToken accessToken = new AccessToken(speechSynthesizerDto.getAccessKeyId(), speechSynthesizerDto.getSecret());
            try {
                accessToken.apply();
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
            token = accessToken.getToken();
            System.out.println((System.currentTimeMillis() / 1000) + "," + System.currentTimeMillis());
            // 有效期毫秒
            Long millisTime = accessToken.getExpireTime() - (System.currentTimeMillis() / 1000);
            // 缓存token
            redisService.putVoiceToken(token, millisTime);
        }

        logger.info("accessToken = {}", token);
        client = new NlsClient(speechUrl, token);
    }


    private static SpeechSynthesizerListener getSynthesizerListener(final File file) {
        SpeechSynthesizerListener listener = null;
        try {
            listener = new SpeechSynthesizerListener() {
                final FileOutputStream fout = new FileOutputStream(file);
                private boolean firstRecvBinary = true;

                //语音合成结束
                @Override
                public void onComplete(SpeechSynthesizerResponse response) {
                    // TODO 当onComplete时表示所有TTS数据已经接收完成，因此这个是整个合成延迟，该延迟可能较大，未必满足实时场景
                    System.out.println("name: " + response.getName() + ", status: " + response.getStatus() + ", output file :" + file.getAbsolutePath());
                }

                //语音合成的语音二进制数据
                @Override
                public void onMessage(ByteBuffer message) {
                    try {
                        if (firstRecvBinary) {
                            // TODO 此处是计算首包语音流的延迟，收到第一包语音流时，即可以进行语音播放，以提升响应速度(特别是实时交互场景下)
                            firstRecvBinary = false;
                        }
                        byte[] bytesArray = new byte[message.remaining()];
                        message.get(bytesArray, 0, bytesArray.length);
                        //System.out.println("write array:" + bytesArray.length);
                        fout.write(bytesArray);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFail(SpeechSynthesizerResponse response) {
                    // TODO 重要提示： task_id很重要，是调用方和服务端通信的唯一ID标识，当遇到问题时，需要提供此task_id以便排查
                    System.out.println(
                            "task_id: " + response.getTaskId() +
                                    //状态码 20000000 表示识别成功
                                    ", status: " + response.getStatus() +
                                    //错误信息
                                    ", status_text: " + response.getStatusText());
                }

                @Override
                public void onMetaInfo(SpeechSynthesizerResponse response) {
                    System.out.println("MetaInfo event:{}" + response.getTaskId());
                }
            };
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listener;
    }


    public String process() {
        SpeechSynthesizer synthesizer = null;
        try {
            //创建实例,建立连接
            File file = new File(  speechSynthesizerDto.getUrl() + DateUtils.datetimeToString(new Date(), "yyyyMMddHHmmssSSS") + ".mp3");
            logger.warn("file path = {}", file.getAbsolutePath());
            SpeechSynthesizerListener ls = getSynthesizerListener(file);
            logger.warn("SpeechSynthesizerListener path = {}", ls.hashCode());
            synthesizer = new SpeechSynthesizer(client, ls);
            synthesizer.setAppKey(speechSynthesizerDto.getAppKey());
            //设置返回音频的编码格式
            synthesizer.setFormat(OutputFormatEnum.MP3);
            if(speechSynthesizerDto.getSampleRate() != null){
                //设置返回音频的采样率
                synthesizer.setSampleRate(speechSynthesizerDto.getSampleRate());
            }else {
                synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
            }

            //发音人
            synthesizer.setVoice(speechSynthesizerDto.getVoice());
            if(speechSynthesizerDto.getVolume() != null){
                //音量
                synthesizer.setVolume(speechSynthesizerDto.getVolume());
            }
            if(speechSynthesizerDto.getPitchRate() != null){
                //语调，范围是-500~500，可选，默认是0
                synthesizer.setPitchRate(speechSynthesizerDto.getPitchRate());
            }
            if(speechSynthesizerDto.getSpeechRate() != null){
                //语速，范围是-500~500，默认是0
                synthesizer.setSpeechRate(speechSynthesizerDto.getSpeechRate());
            }
            //设置用于语音合成的文本
            if (StringUtils.isNotEmpty(speechSynthesizerDto.getBgm()) && speechSynthesizerDto.getBugRate() != null) {
                synthesizer.setText("<speak bgm = \"" + speechSynthesizerDto.getBgm() + "\" backgroundMusicVolume = \"" + speechSynthesizerDto.getBugRate() + "\">" +
                        speechSynthesizerDto.getText() + " </speak>");
            }
            if (StringUtils.isNotEmpty(speechSynthesizerDto.getBgm()) && speechSynthesizerDto.getBugRate() == null) {
                synthesizer.setText("<speak bgm =\"" + speechSynthesizerDto.getBgm() + "\" backgroundMusicVolume = \"50\" volume=\"100\">" +
                        speechSynthesizerDto.getText() + " </speak>");
            }
            if (StringUtils.isEmpty(speechSynthesizerDto.getBgm())) {
                synthesizer.setText("<speak>" + speechSynthesizerDto.getText()+ " </speak>");
            }
            synthesizer.addCustomedParam("enable_subtitle", true);

            //此方法将以上参数设置序列化为json发送给服务端,并等待服务端确认
            long start = System.currentTimeMillis();
            synthesizer.start();
            logger.info("tts start latency {} ms", System.currentTimeMillis() - start);

            //等待语音合成结束
            synthesizer.waitForComplete();
            logger.info("tts stop latency {} ms", System.currentTimeMillis() - start);
            logger.info("url = {}", file.getAbsolutePath());
            return file.getAbsolutePath();//返回存储的内存地址
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            //关闭连接`
            if (null != synthesizer) {
                synthesizer.close();
            }
        }
        return null;
    }


    public void shutdown() {
        client.shutdown();
    }

}
