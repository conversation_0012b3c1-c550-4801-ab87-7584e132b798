package com.gl.framework.security.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.system.service.SysMenuService;
import com.gl.system.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理服务
 */
@Component
public class SysPermissionService {

	@Autowired
	private SysRoleService roleService;

	@Autowired
	private SysMenuService menuService;

	/**
	 * 获取角色数据权限
	 *
	 * @param userId 用户ID
	 * @return 角色权限信息
	 */
	public Set<String> getRolePermission(Long userId) {
		Set<String> roles = new HashSet<>();
		// 管理员拥有所有权限
		if (SecurityUtils.isSuperAdmin(userId)) {
			roles.add("admin");
		} else {
			roles.addAll(roleService.selectRolePermissionByUserId(userId));
		}
		return roles;
	}

	/**
	 * 获取菜单数据权限
	 *
	 * @param userId 用户ID
	 * @return 菜单权限信息
	 */
	public Set<String> getMenuPermission(Long userId) {
		Set<String> perms = new HashSet<>();
		// 管理员拥有所有权限
		if (SecurityUtils.isSuperAdmin(userId)) {
			perms.add("*:*:*");
		} else {
			perms.addAll(menuService.selectMenuPermsByUserId(userId));
		}
		return perms;
	}
}
