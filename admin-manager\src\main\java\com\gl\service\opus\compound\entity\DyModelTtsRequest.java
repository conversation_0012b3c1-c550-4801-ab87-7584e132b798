package com.gl.service.opus.compound.entity;


public class DyModelTtsRequest {
    private App app = new App();
    private User user = new User();
    private Audio audio = new Audio();
    private Request request = new Request();

    public App getApp() {
        return app;
    }

    public void setApp(App app) {
        this.app = app;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Audio getAudio() {
        return audio;
    }

    public void setAudio(Audio audio) {
        this.audio = audio;
    }

    public Request getRequest() {
        return request;
    }

    public void setRequest(Request request) {
        this.request = request;
    }

    public class App {
        private String appid;
        private String token; // 目前未生效，填写默认值：access_token
        private String cluster;

        public String getAppid() {
            return appid;
        }

        public void setAppid(String appid) {
            this.appid = appid;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getCluster() {
            return cluster;
        }

        public void setCluster(String cluster) {
            this.cluster = cluster;
        }
    }

    public class User {
        private String uid; // 目前未生效，填写一个默认值就可以

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }
    }

    public class Audio {
        private String voice_type;
        private String encoding;
        private float speed_ratio;

        public String getVoice_type() {
            return voice_type;
        }

        public void setVoice_type(String voice_type) {
            this.voice_type = voice_type;
        }

        public String getEncoding() {
            return encoding;
        }

        public void setEncoding(String encoding) {
            this.encoding = encoding;
        }

        public float getSpeedRatio() {
            return speed_ratio;
        }

        public void setSpeedRatio(int speed_ratio) {
            this.speed_ratio = speed_ratio;
        }
    }

    public class Request {
        private String reqid;
        private String text;
        private String text_type;
        private String operation;

        public String getReqid() {
            return reqid;
        }

        public void setReqid(String reqid) {
            this.reqid = reqid;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getText_type() {
            return text_type;
        }

        public void setText_type(String text_type) {
            this.text_type = text_type;
        }

        public String getOperation() {
            return operation;
        }

        public void setOperation(String operation) {
            this.operation = operation;
        }
    }
}