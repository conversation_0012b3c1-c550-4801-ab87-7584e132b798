package com.gl.framework.web.domain;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页数据对象
 */
public class PageData<T> {
	/**
	 * 总记录数
	 */
	private Long total = 0L;
	/**
	 * 结果集
	 */
	private List<T> data = new ArrayList<>();

	public PageData() {
	}

	public PageData(Long total, List<T> data) {
		this.total = total;
		this.data = data;
	}

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
	}

	public List<T> getData() {
		return data;
	}

	public void setData(List<T> data) {
		this.data = data;
	}
}
