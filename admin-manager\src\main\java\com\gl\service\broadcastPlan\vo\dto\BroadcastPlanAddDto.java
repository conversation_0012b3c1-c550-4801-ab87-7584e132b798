package com.gl.service.broadcastPlan.vo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BroadcastPlanAddDto extends UserVo {

    private Long id;
    private Long shopId;
    private List<Long> deviceIds;
    private String startTime;
    private String endTime;
    private List<Long> voiceWorkList;


    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private String startDate;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private String endDate;
    private String type;
    private String intervalTime;

    private List<Long> ids;
}
