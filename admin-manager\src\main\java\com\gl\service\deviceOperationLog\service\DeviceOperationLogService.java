package com.gl.service.deviceOperationLog.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.deviceOperationLog.entity.DeviceOperationLog;
import com.gl.service.deviceOperationLog.repository.DeviceOperationLogRepository;
import com.gl.service.opus.entity.Device;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2025/4/21
 * @description:
 */
@Slf4j
@Service
public class DeviceOperationLogService {

    @Autowired
    private DeviceOperationLogRepository deviceOperationLogRepository;
    @Autowired
    private DeviceRepository deviceRepository;


    public void sendSave(String deviceSn, String content, String type,Long userId, String timeStamp) {
        Device device = deviceRepository.findDeviceBySn(deviceSn);
        if (device != null) {
            DeviceOperationLog deviceOperationLog = new DeviceOperationLog();
            deviceOperationLog.setUserId(userId);
            deviceOperationLog.setSendTime(new Date());
            deviceOperationLog.setDeviceId(device.getId());
            deviceOperationLog.setShopId(device.getShopId());
            deviceOperationLog.setType(type);
            deviceOperationLog.setContent(content);
            deviceOperationLog.setTimeStamp(timeStamp);
            deviceOperationLogRepository.save(deviceOperationLog);
        }
    }

    public void update(String timeStamp, String responseContent) {

        DeviceOperationLog entity = deviceOperationLogRepository.findByTimeStamp(timeStamp);
        if (entity == null) {
            log.error("没有找到存储的操作数据 timeStamp:{},responseContent:{}", timeStamp, responseContent);
            return;
        }

        entity.setContent(responseContent);
        entity.setResponseTime(new Date());
        deviceOperationLogRepository.save(entity);
    }

    public void callBackLog(String deviceSn, String message) {
    }
}
