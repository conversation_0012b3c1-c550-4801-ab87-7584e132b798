package com.gl.framework.web.response;

/**
 * 状态码及对应描述枚举类
 */
public enum ResultCode {
	/**
	 * 10000-SUCCESS-操作成功
	 */
	SUCCESS(10000, "SUCCESS", "操作成功"),

	/**
	 * 10001-FAIL-操作失败
	 */
	FAIL(10001, "FAIL", "操作失败"),

	/**
	 * 40000-PARAM_ERROR-参数错误
	 */
	PARAM_ERROR(40000, "PARAM_ERROR", "参数错误"),

	/**
	 * 40001-UNAUTHORIZED-认证失败，无法访问系统资源
	 */
	UNAUTHORIZED(40001, "UNAUTHORIZED", "认证失败，无法访问系统资源"),

	/**
	 * 40003-FORBIDDEN-没有权限，请联系管理员授权
	 */
	FORBIDDEN(40003, "FORBIDDEN", "没有权限，请联系管理员授权"),

	/**
	 * 40004-NOT_FOUND-访问资源不存在，请检查路径是否正确
	 */
	NOT_FOUND(40004, "NOT_FOUND", "访问资源不存在，请检查路径是否正确"),

	/**
	 * 50000-SYSTEM_ERROR-系统异常，请稍后再试
	 */
	SYSTEM_ERROR(50000, "SYSTEM_ERROR", "系统异常，请稍后再试");

	/**
	 * 状态码
	 */
	private final int code;
	/**
	 * 状态描述（一般为英文概要）
	 */
	private final String msg;
	/**
	 * 提示语
	 */
	private final String message;

	ResultCode(int code, String msg, String message) {
		this.code = code;
		this.msg = msg;
		this.message = message;
	}

	public int getCode() {
		return code;
	}

	public String getMsg() {
		return msg;
	}

	public String getMessage() {
		return message;
	}

}
