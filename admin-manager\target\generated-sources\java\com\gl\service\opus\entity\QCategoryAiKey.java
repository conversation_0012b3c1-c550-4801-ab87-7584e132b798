package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QCategoryAiKey is a Querydsl query type for CategoryAiKey
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QCategoryAiKey extends EntityPathBase<CategoryAiKey> {

    private static final long serialVersionUID = 2017303976L;

    public static final QCategoryAiKey categoryAiKey = new QCategoryAiKey("categoryAiKey");

    public final StringPath agentKey = createString("agentKey");

    public final StringPath apiKey = createString("apiKey");

    public final StringPath appId = createString("appId");

    public final NumberPath<Integer> categoryId = createNumber("categoryId", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath prompt = createString("prompt");

    public final StringPath promptId = createString("promptId");

    public QCategoryAiKey(String variable) {
        super(CategoryAiKey.class, forVariable(variable));
    }

    public QCategoryAiKey(Path<? extends CategoryAiKey> path) {
        super(path.getType(), path.getMetadata());
    }

    public QCategoryAiKey(PathMetadata metadata) {
        super(CategoryAiKey.class, metadata);
    }

}

