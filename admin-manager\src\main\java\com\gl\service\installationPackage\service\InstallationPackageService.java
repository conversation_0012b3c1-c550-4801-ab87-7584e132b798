package com.gl.service.installationPackage.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.installationPackage.entity.InstallationPackage;
import com.gl.service.installationPackage.repository.InstallationPackageRepository;
import com.gl.service.installationPackage.vo.installationPackage.InstallationPackageVo;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageAddDto;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageDto;
import com.gl.system.vo.SysUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class InstallationPackageService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private InstallationPackageRepository installationPackageRepository;

    public Result list(InstallationPackageDto dto, Integer exportType) {
        Result result = Result.success();
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and ( p.version_name like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }

        String sql = "select \n" +
                "p.id,\n" +
                "p.version_name,\n" +
                "p.remark,\n" +
                "p.package_url,\n" +
                "p.create_time,\n" +
                "count(pl.id) updateDeviceNum, \n" +
                "group_concat(pl.device_id) deviceIds " +
                "from dub_installation_package p " +
                "left join dub_installation_package_log pl on pl.package_id = p.id " +
                "where 1=1 ";

        where.append("GROUP BY p.id  ");

        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }

        where.append("order by p.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<InstallationPackageVo> deviceVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(InstallationPackageVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", deviceVos);
        return result;
    }


    public Result addOrUpdate(InstallationPackageAddDto vo) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        if (vo == null) {
            return Result.fail("数据不能为空");
        }

        if (StringUtils.isBlank(vo.getVersionName())) {
            return Result.fail("版本号不能为空");
        }
        if (StringUtils.isBlank(vo.getRemark())) {
            return Result.fail("更新说明不能为空");
        }
        if (StringUtils.isBlank(vo.getPackageUrl())) {
            return Result.fail("更新包不能为空");
        }

        Date date = new Date();
        if (vo.getId() == null) {
            //新增
            InstallationPackage device = new InstallationPackage();
            device.setVersionName(vo.getVersionName());
            device.setRemark(vo.getRemark());
            device.setPackageUrl(vo.getPackageUrl());

            device.setCreateUserId(user.getId());
            device.setCreateTime(date);
            installationPackageRepository.save(device);
        } else {
            Optional<InstallationPackage> byId = installationPackageRepository.findById(vo.getId());
            if (byId.isPresent()) {
                //修改
                InstallationPackage device = byId.get();
                device.setVersionName(vo.getVersionName());
                device.setRemark(vo.getRemark());
                device.setPackageUrl(vo.getPackageUrl());

                installationPackageRepository.save(device);
            }
        }
        return Result.success();
    }

    public Result delete(InstallationPackageAddDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getIds().isEmpty()) {
            return Result.fail("安装包id不能为空");
        }
        for (Long id : dto.getIds()) {
            installationPackageRepository.deleteById(id);
        }
        return Result.success();
    }

}
