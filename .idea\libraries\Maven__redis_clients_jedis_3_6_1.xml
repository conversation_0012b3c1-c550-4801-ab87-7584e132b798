<component name="libraryTable">
  <library name="Maven: redis.clients:jedis:3.6.1">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/redis/clients/jedis/3.6.1/jedis-3.6.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/redis/clients/jedis/3.6.1/jedis-3.6.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/redis/clients/jedis/3.6.1/jedis-3.6.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>