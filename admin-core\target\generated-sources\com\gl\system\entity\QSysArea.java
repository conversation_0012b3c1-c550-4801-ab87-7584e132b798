package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysArea is a Querydsl query type for SysArea
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysArea extends EntityPathBase<SysArea> {

    private static final long serialVersionUID = -1829768462L;

    public static final QSysArea sysArea = new QSysArea("sysArea");

    public final StringPath code = createString("code");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Double> latitude = createNumber("latitude", Double.class);

    public final NumberPath<Integer> level = createNumber("level", Integer.class);

    public final NumberPath<Double> longitude = createNumber("longitude", Double.class);

    public final StringPath name = createString("name");

    public final NumberPath<Long> parentId = createNumber("parentId", Long.class);

    public QSysArea(String variable) {
        super(SysArea.class, forVariable(variable));
    }

    public QSysArea(Path<? extends SysArea> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysArea(PathMetadata metadata) {
        super(SysArea.class, metadata);
    }

}

