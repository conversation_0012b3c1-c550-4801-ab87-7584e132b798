package com.gl.service.pay.conf;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date: 2025/4/3
 * @description:
 */
@Data
@Component
public class WechatPayV3Config {

    @Value("${wechat.pay.appid}")
    private String appid;
    @Value("${wechat.pay.mchid}")
    private String merchantId;
    @Value("${wechat.pay.mchser}")
    private String merchantSerialNumber;
    @Value("${wechat.pay.privateKeyPath}")
    private String privateKeyPath;
    @Value("${wechat.pay.privateCertPath}")
    private String privateCertPath;
    @Value("${wechat.pay.apikey}")
    private String apiV3Key;
    @Value("${wechat.pay.notifyPath}")
    private String notifyPath;

    public WxPayConfig getWxPayConfig() {
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setAppId(appid);
        payConfig.setMchId(merchantId);
        payConfig.setCertSerialNo(merchantSerialNumber);
        payConfig.setApiV3Key(apiV3Key);
        payConfig.setPrivateKeyPath(privateKeyPath);
        payConfig.setPrivateCertPath(privateCertPath);
        payConfig.setNotifyUrl(notifyPath);
        return payConfig;
    }

    public WxPayService getWxPayService() {
        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(getWxPayConfig());
        return wxPayService;
    }



}
