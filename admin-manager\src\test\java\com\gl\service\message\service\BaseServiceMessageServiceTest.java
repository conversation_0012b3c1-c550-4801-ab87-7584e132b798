package com.gl.service.message.service;

import com.gl.framework.web.response.Result;
import com.gl.service.message.repository.BaseServiceMessageRepository;
import com.gl.service.message.vo.BaseServiceMessageVo;
import com.gl.service.message.vo.dto.BaseServiceMessageDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BaseServiceMessageService单元测试类
 * 测试客服留言服务的所有业务逻辑，包括列表查询和删除功能
 * 
 * @author: Test Author
 * @date: 2025-07-12
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("BaseServiceMessageService单元测试")
class BaseServiceMessageServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private BaseServiceMessageRepository baseServiceMessageRepository;

    @InjectMocks
    private BaseServiceMessageService baseServiceMessageService;

    private BaseServiceMessageDto testDto;
    private List<BaseServiceMessageVo> testMessageList;
    private BaseServiceMessageVo testMessage;

    @BeforeEach
    @DisplayName("测试数据初始化")
    void setUp() {
        // 初始化测试DTO
        testDto = new BaseServiceMessageDto();
        testDto.setPageNumber(0);
        testDto.setPageSize(10);
        testDto.setSearchCondition("测试留言");

        // 初始化测试留言对象
        testMessage = new BaseServiceMessageVo();
        testMessage.setId(1L);
        testMessage.setUserId(100L);
        testMessage.setContent("这是一条测试留言");
        testMessage.setMessageTime(new Date());
        testMessage.setNickname("测试用户");
        testMessage.setPhone("13800138000");
        testMessage.setAvatar("http://example.com/avatar.jpg");

        // 初始化测试留言列表
        testMessageList = Arrays.asList(testMessage);
    }

    // ==================== list方法测试 ====================

    @Test
    @DisplayName("测试list方法 - 正常查询带搜索条件")
    void testList_WithSearchCondition_ShouldReturnSuccessResult() {
        // Given
        Long expectedCount = 1L;
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testMessageList);

        // When
        Result result = baseServiceMessageService.list(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        assertEquals("success", result.getMessage(), "应返回成功消息");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(expectedCount, data.get("total"), "总数应匹配");
        assertEquals(testMessageList, data.get("result"), "结果列表应匹配");

        // 验证SQL查询调用
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 正常查询无搜索条件")
    void testList_WithoutSearchCondition_ShouldReturnSuccessResult() {
        // Given
        testDto.setSearchCondition(null);
        Long expectedCount = 2L;
        
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testMessageList);

        // When
        Result result = baseServiceMessageService.list(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(expectedCount, data.get("total"), "总数应匹配");
        assertEquals(testMessageList, data.get("result"), "结果列表应匹配");
    }

    @Test
    @DisplayName("测试list方法 - 空搜索条件")
    void testList_WithEmptySearchCondition_ShouldReturnSuccessResult() {
        // Given
        testDto.setSearchCondition("");
        Long expectedCount = 1L;
        
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testMessageList);

        // When
        Result result = baseServiceMessageService.list(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
    }

    @Test
    @DisplayName("测试list方法 - 查询结果为空")
    void testList_WithZeroCount_ShouldReturnEmptyResult() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(0L);

        // When
        Result result = baseServiceMessageService.list(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"), "总数应为0");
        assertNull(data.get("result"), "结果应为null");

        // 验证不应调用第二次查询
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 查询结果count为null")
    void testList_WithNullCount_ShouldReturnEmptyResult() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(null);

        // When
        Result result = baseServiceMessageService.list(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"), "总数应为0");
        assertNull(data.get("result"), "结果应为null");
    }

    @Test
    @DisplayName("测试list方法 - DTO为null")
    void testList_WithNullDto_ShouldHandleGracefully() {
        // Given
        Long expectedCount = 1L;
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(expectedCount);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testMessageList);

        // When
        Result result = baseServiceMessageService.list(null);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
    }

    @Test
    @DisplayName("测试list方法 - 数据库查询异常")
    void testList_WithDatabaseException_ShouldThrowException() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            baseServiceMessageService.list(testDto);
        }, "应抛出数据库异常");
    }

    // ==================== delete方法测试 ====================

    @Test
    @DisplayName("测试delete方法 - 正常删除")
    void testDelete_WithValidIds_ShouldReturnSuccessResult() {
        // Given
        List<Long> idsToDelete = Arrays.asList(1L, 2L, 3L);
        testDto.setIds(idsToDelete);
        
        doNothing().when(baseServiceMessageRepository).deleteAllByIdInBatch(idsToDelete);

        // When
        Result result = baseServiceMessageService.delete(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        assertEquals("success", result.getMessage(), "应返回成功消息");
        
        verify(baseServiceMessageRepository, times(1)).deleteAllByIdInBatch(idsToDelete);
    }

    @Test
    @DisplayName("测试delete方法 - DTO为null")
    void testDelete_WithNullDto_ShouldReturnFailResult() {
        // When
        Result result = baseServiceMessageService.delete(null);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "应返回失败状态码");
        assertEquals("数据不能为空", result.getMessage(), "应返回正确的错误消息");
        
        verify(baseServiceMessageRepository, never()).deleteAllByIdInBatch(any());
    }

    @Test
    @DisplayName("测试delete方法 - IDs列表为空")
    void testDelete_WithEmptyIds_ShouldReturnFailResult() {
        // Given
        testDto.setIds(Collections.emptyList());

        // When
        Result result = baseServiceMessageService.delete(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "应返回失败状态码");
        assertEquals("留言id不能为空", result.getMessage(), "应返回正确的错误消息");
        
        verify(baseServiceMessageRepository, never()).deleteAllByIdInBatch(any());
    }

    @Test
    @DisplayName("测试delete方法 - IDs列表为null")
    void testDelete_WithNullIds_ShouldThrowException() {
        // Given
        testDto.setIds(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            baseServiceMessageService.delete(testDto);
        }, "应抛出空指针异常");
    }

    @Test
    @DisplayName("测试delete方法 - 数据库删除异常")
    void testDelete_WithDatabaseException_ShouldThrowException() {
        // Given
        List<Long> idsToDelete = Arrays.asList(1L, 2L);
        testDto.setIds(idsToDelete);
        
        doThrow(new RuntimeException("数据库删除异常"))
                .when(baseServiceMessageRepository).deleteAllByIdInBatch(idsToDelete);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            baseServiceMessageService.delete(testDto);
        }, "应抛出数据库异常");
        
        verify(baseServiceMessageRepository, times(1)).deleteAllByIdInBatch(idsToDelete);
    }

    @Test
    @DisplayName("测试delete方法 - 单个ID删除")
    void testDelete_WithSingleId_ShouldReturnSuccessResult() {
        // Given
        List<Long> singleId = Arrays.asList(1L);
        testDto.setIds(singleId);
        
        doNothing().when(baseServiceMessageRepository).deleteAllByIdInBatch(singleId);

        // When
        Result result = baseServiceMessageService.delete(testDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        
        verify(baseServiceMessageRepository, times(1)).deleteAllByIdInBatch(singleId);
    }
}
