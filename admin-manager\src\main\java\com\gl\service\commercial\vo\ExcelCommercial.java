package com.gl.service.commercial.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class ExcelCommercial {

    @ExcelProperty(value = "头像",index = 0)
    @ColumnWidth(40)
    private String avatar;

    @ExcelProperty(value = "昵称",index = 1)
    @ColumnWidth(20)
    private String nickname;

    @ExcelProperty(value = "手机号",index = 2)
    @ColumnWidth(20)
    private String phone;

    @ExcelProperty(value = "性别",index = 3)
    @ColumnWidth(20)
    private String gender;

    @ExcelProperty(value = "授权时间",index = 4)
    @ColumnWidth(20)
    private String authTime;

    @ExcelProperty(value = "关联店铺",index = 5)
    @ColumnWidth(20)
    private String shopNames;

    @ExcelProperty(value = "关联设备",index = 6)
    @ColumnWidth(20)
    private String deviceNames;



}
