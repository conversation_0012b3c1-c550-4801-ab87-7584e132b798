package com.gl.service.packet.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @author: duanjinze
 * @date: 2022/11/11 13:43
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoicePacketVo extends UserVo {
    private Long id;

    /**
     * 作品id
     */
    private Long voiceWorkId;

    /**
     * 名称
     */
    private String name;

    /**
     * 时长
     */
    private Integer voiceTime;

    /**
     * 文件url
     */
    private String fileUrl;

    //===========//
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private String shopName;
}
