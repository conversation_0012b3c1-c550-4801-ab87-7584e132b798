package com.gl.system.service;

import com.gl.framework.common.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.net.URLEncoder;

@Slf4j
public class PasswodTest {

    @Test
    public void password() {
        String password = SecurityUtils.encryptPassword("a12345678");
        log.info("password:{}", password);
    }

    @Test
    public void encode() {
        String str = URLEncoder.encode("&");
        log.info("str:{}", str);
    }
}
