package com.gl.service.opus.vo.dto;

import com.gl.framework.web.domain.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: duanjinze
 * @date: 2022/11/10 13:51
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkDto extends BaseVo {
    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 关键词 【作品标题、作品文字、背景音乐、用户昵称、用户手机】
     */
    private String searchCondition;

    private List<Long> ids;

    private Long shopId;
}
