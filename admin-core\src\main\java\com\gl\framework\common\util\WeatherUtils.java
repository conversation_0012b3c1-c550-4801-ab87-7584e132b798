package com.gl.framework.common.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.zip.GZIPInputStream;

public class WeatherUtils {
	private static String weatherUrl = "http://wthrcdn.etouch.cn/weather_mini?city=";

	/**
	 * 通过城市名称获取该城市的天气信息
	 */
	public static String GetWeatherData(String cityname) {
		StringBuilder sb = new StringBuilder();
		BufferedReader reader = null;
		try {
			URL url = new URL(weatherUrl + cityname);
			URLConnection conn = url.openConnection();
			InputStream is = conn.getInputStream();
			GZIPInputStream gzin = new GZIPInputStream(is);
			// 设置读取流的编码格式，自定义编码
			InputStreamReader isr = new InputStreamReader(gzin, "utf-8");
			reader = new BufferedReader(isr);
			String line = null;
			while ((line = reader.readLine()) != null) {
				sb.append(line + " ");
			}
			reader.close();

		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (MalformedURLException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}

		String str = sb.toString();
		str = str.replace("<![CDATA[", "");
		str = str.replace("]]>", "");
		return str;
	}

//	public static void main(String[] args) {
//		String str = GetWeatherData("广州");
//		JSONObject JSON = JSONObject.parseObject(str);
//		System.out.println(JSON);
//	}



}
