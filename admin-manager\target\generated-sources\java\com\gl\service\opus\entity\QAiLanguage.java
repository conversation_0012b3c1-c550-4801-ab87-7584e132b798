package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAiLanguage is a Querydsl query type for AiLanguage
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAiLanguage extends EntityPathBase<AiLanguage> {

    private static final long serialVersionUID = -88946927L;

    public static final QAiLanguage aiLanguage = new QAiLanguage("aiLanguage");

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> orderIndex = createNumber("orderIndex", Integer.class);

    public final StringPath title = createString("title");

    public QAiLanguage(String variable) {
        super(AiLanguage.class, forVariable(variable));
    }

    public QAiLanguage(Path<? extends AiLanguage> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAiLanguage(PathMetadata metadata) {
        super(AiLanguage.class, metadata);
    }

}

