package com.gl.framework.common.util;

import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.ResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 安全服务工具类
 */
public class SecurityUtils {
	private static final Logger log = LoggerFactory.getLogger(SecurityUtils.class);

	/**
	 * 系统超级管理员ID
	 */
	public static final long SUPER_ADMIN_ID = 1L;

	/**
	 * 获取用户账号
	 **/
	public static String getLoginName() {
		try {
			return getLoginUser().getUsername();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new CustomException("获取用户账号异常", ResultCode.UNAUTHORIZED.getCode());
		}
	}

	/**
	 * 获取用户
	 **/
	public static LoginUser getLoginUser() {
		try {
			return (LoginUser) getAuthentication().getPrincipal();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new CustomException("获取用户信息异常", ResultCode.UNAUTHORIZED.getCode());
		}
	}

	/**
	 * 获取Authentication
	 */
	public static Authentication getAuthentication() {
		return SecurityContextHolder.getContext().getAuthentication();
	}

	/**
	 * 生成BCryptPasswordEncoder密码
	 *
	 * @param password 密码
	 * @return 加密字符串
	 */
	public static String encryptPassword(String password) {
		BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
		return passwordEncoder.encode(password);
	}

	/**
	 * 判断密码是否相同
	 *
	 * @param rawPassword     真实密码
	 * @param encodedPassword 加密后字符
	 * @return 结果
	 */
	public static boolean matchesPassword(String rawPassword, String encodedPassword) {
		BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
		return passwordEncoder.matches(rawPassword, encodedPassword);
	}

	/**
	 * 是否为系统超级管理员
	 *
	 * @param userId 用户ID
	 * @return 结果
	 */
	public static boolean isSuperAdmin(Long userId) {
		return userId != null && SUPER_ADMIN_ID == userId;
	}
}
