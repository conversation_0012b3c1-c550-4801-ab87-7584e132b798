package com.gl.aspectj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.gl.aspectj.annotation.ShopPermission;
import com.gl.commons.enums.StoreUserTypeEnum;
import com.gl.commons.enums.UserTypeEnum;
import com.gl.framework.exception.CustomException;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.service.broadcastPlan.entity.BroadcastPlan;
import com.gl.service.broadcastPlan.repository.BroadcastPlanRepository;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Around;
import org.springframework.stereotype.Component;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.opus.entity.Device;
import com.gl.service.shop.entity.Shop;
import org.aspectj.lang.reflect.MethodSignature;
import com.gl.service.shop.repository.ShopUserRefRepository;

import javax.annotation.Resource;
import java.util.*;

/**
 * 门店数据权限切面。
 * <p>
 * 目前仅针对播报计划模块做校验，可根据业务需要继续扩展。
 * </p>
 */
@Aspect
@Component
@Slf4j
public class ShopPermissionAspect {

    @Resource
    private GetShopRefUtil shopRefUtil;

    @Resource
    private BroadcastPlanRepository broadcastPlanRepository;

    /**
     * 设备仓库，用于校验 deviceIds 是否属于允许门店。
     */
    @Resource
    private DeviceRepository deviceRepository;

    /**
     * 用户与门店关系仓库，用于判断当前微信用户在门店中的角色
     */
    @Resource
    private ShopUserRefRepository shopUserRefRepository;

    /**
     * 根据注解给出的 entity 类型，对方法参数进行统一的数据权限校验。
     * 仅保留三种核心场景：Shop、Device、BroadcastPlan。
     * 其它历史上的自动推断、反射写回等功能全部删除，避免复杂度。
     */
    @Around("@annotation(shopPermission)")
    public Object around(ProceedingJoinPoint joinPoint, ShopPermission shopPermission) throws Throwable {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();

        // 非站点账号（例如 SaaS 总后台等）无需门店维度校验
        if (!ObjectUtil.equal(user.getType(), UserTypeEnum.SITE.getType())) {
            return joinPoint.proceed();
        }

        // 先获取当前用户允许访问的门店集合
        List<Long> allowShops = shopRefUtil.getShopRef();
        if (CollUtil.isEmpty(allowShops)) {
            throw new CustomException("当前账号未绑定门店，无权访问数据");
        }

        // 判断当前登录用户是否为门店管理员：
        // 微信端用户（type = 2）使用 siteId 作为用户标识，通过 ShopUserRef 判断其在允许门店中的角色。
        boolean isAdmin = false;
        if (user.getSiteId() != null) {
            isAdmin = shopUserRefRepository.existsByUserIdAndRoleAndShopIdIn(
                    user.getSiteId(),
                    StoreUserTypeEnum.ADMIN.getCode(),
                    allowShops);
        }

        boolean operate = shopPermission.operate();

        // 非管理员做写操作直接拒绝
        if (operate && !isAdmin) {
            throw new CustomException("非门店管理员，无权执行该操作");
        }

        Class<?> entityClazz = shopPermission.entity();
        if (entityClazz == null || Void.class.equals(entityClazz)) {
            // 未指定实体类型时，直接放行
            return joinPoint.proceed();
        }

        // 收集待校验的主键 ID
        Set<Long> ids = new HashSet<>();

        MethodSignature methodSig = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = methodSig.getParameterNames();
        Object[] args = joinPoint.getArgs();

        // 若显式指定了 idParam，则仅从该参数中提取 ID
        String idParam = shopPermission.idParam();

        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg == null) {
                continue;
            }

            String paramName = (paramNames != null && i < paramNames.length) ? paramNames[i] : "";

            boolean paramExplicitMatch = StringUtils.isNotBlank(idParam) && idParam.equals(paramName);
            if (!paramExplicitMatch) {
                continue;
            }

            // 2. 直接是 Long 主键
            if (arg instanceof Long) {
                ids.add((Long) arg);
                continue;
            }

            // 可能是 List<Long>
            if (arg instanceof Collection) {
                for (Object o : (Collection<?>) arg) {
                    if (o instanceof Long) {
                        ids.add((Long) o);
                    }
                }
            }
        }

        if (ids.isEmpty()) {
            return joinPoint.proceed(); // 无主键可校验，直接放行
        }

        // 根据不同实体类型执行对应校验逻辑
        if (Shop.class.equals(entityClazz)) {
            for (Long sid : ids) {
                verifyShopBelongs(sid, allowShops);
            }
        } else if (Device.class.equals(entityClazz)) {
            verifyDeviceBelongs(ids, allowShops);
        } else if (BroadcastPlan.class.equals(entityClazz)) {
            verifyPlanBelongs(ids, allowShops);
        }

        // 校验通过后继续执行业务逻辑
        return joinPoint.proceed();
    }

    private void verifyPlanBelongs(Set<Long> planIds, List<Long> allowShops) {
        if (CollUtil.isEmpty(planIds)) {
            return;
        }
        List<BroadcastPlan> plans = broadcastPlanRepository.findAllById(planIds);
        if (plans.size() != planIds.size()) {
            throw new CustomException("存在无效的播报计划ID");
        }
        for (BroadcastPlan plan : plans) {
            verifyShopBelongs(plan.getShopId(), allowShops);
        }
    }

    private void verifyDeviceBelongs(Set<Long> deviceIds, List<Long> allowShops) {
        if (CollUtil.isEmpty(deviceIds)) {
            return;
        }
        List<Device> devices = deviceRepository.findAllById(deviceIds);
        if (devices.size() != deviceIds.size()) {
            throw new CustomException("存在无效的设备ID");
        }
        for (Device dev : devices) {
            verifyShopBelongs(dev.getShopId(), allowShops);
        }
    }

    private void verifyShopBelongs(Long shopId, List<Long> allowShops) {
        if (shopId == null) {
            return;
        }
        if (!allowShops.contains(shopId)) {
            throw new CustomException("无权操作其他门店的数据");
        }
    }
}