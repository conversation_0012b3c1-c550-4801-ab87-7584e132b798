package com.gl.service.opus.entity;


import lombok.Data;

import javax.persistence.*;


@Entity
@Table(name = "long_anchor")
@Data
public class LongAnchor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 名称
     */
    private String name;

    /**
     * voice参数
     */
    private String voiceName;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 使用场景
     */
    private String usageScenario;

    /**
     * 1中文及英文混合 2纯中文场景 3标准粤文
     */
    @Column(name = "support_voice_type", columnDefinition = "smallint")
    private Integer supportVoiceType;

    /**
     * 支持采样率
     */
    private Integer sampleRate;

    /**
     * 支持时间戳
     */
    @Column(name = "enable_subtitle", columnDefinition = "smallint")
    private Integer enableSubtitle;

    /**
     * 支持儿化音
     */
    @Column(name = "rhotic_accent", columnDefinition = "smallint")
    private Integer rhoticAccent;
    @Column(name = "url")
    private String url;
    @Column(name = "type")
    private String type;
    @Column(name = "voice_url")
    private String voiceUrl;

    @Column(name = "vip", columnDefinition = "smallint")
    private Integer vip;
}
