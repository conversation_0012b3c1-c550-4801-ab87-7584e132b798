package com.gl.service.job.service;

import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.opus.entity.Device;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @author: duanjinze
 * @date: 2022/11/15 13:39
 * @version: 1.0
 */
@Service
public class DeviceJobService {
    private static final Logger log = LoggerFactory.getLogger(DeviceJobService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DeviceRepository deviceRepository;

    /**
     * job定时处理，超过10分钟未更新状态的时，job修改状态为：离线
     * 查询出所有未删除的且最后一次更新status的时间，如果最后一次更新为十分钟前，则修改为离线，
     * 此处设置为 每一分钟跑一次
     */
    @Transactional(rollbackFor = Exception.class)
    public void work() {
        log.info("设备更新状态 开始");

        String sql = "select id,status,update_status_time from dub_device where del_status != 1 ";
        List<Device> devices = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(Device.class));
        if (!devices.isEmpty()) {
            for (Device device : devices) {
                if (device.getUpdateStatusTime() == null) {
                    //等于空的时候也修改状态
                    deviceRepository.updateStatusById(device.getId(), 0, new Date());
                } else {
                    long start = device.getUpdateStatusTime().getTime();
                    //获取当前时间毫秒值
                    long current = System.currentTimeMillis();// 返回一个毫秒数
                    log.debug("start={},current={}", start, current);
                    if ((current - start) / 1000 / 60 > 10) {
                        //超过十分钟，修改为离线
                        deviceRepository.updateStatusById(device.getId(), 0, new Date());
                    }
                }
            }
        }
        log.info("设备更新状态 结束");
    }

}
