package com.gl.framework.config.http;
//package com.mzj.framework.config.http;
//
//import java.io.IOException;
//
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpRequest;
//import org.springframework.http.client.ClientHttpRequestExecution;
//import org.springframework.http.client.ClientHttpRequestInterceptor;
//import org.springframework.http.client.ClientHttpResponse;
//import org.springframework.stereotype.Service;
//
///**
// * 请求拦截器
// */
//@Service("restTemplateInterceptor")
//public class RestTemplateInterceptor implements ClientHttpRequestInterceptor {
//
//	@Override
//	public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
//		HttpHeaders headers = request.getHeaders();
//		headers.add("access-token", "token"); // eg：可添加token
//		return execution.execute(request, body);
//	}
//
//}
