package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QTemplateType is a Querydsl query type for TemplateType
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QTemplateType extends EntityPathBase<TemplateType> {

    private static final long serialVersionUID = 1988580869L;

    public static final QTemplateType templateType = new QTemplateType("templateType");

    public final NumberPath<Long> createId = createNumber("createId", Long.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath name = createString("name");

    public QTemplateType(String variable) {
        super(TemplateType.class, forVariable(variable));
    }

    public QTemplateType(Path<? extends TemplateType> path) {
        super(path.getType(), path.getMetadata());
    }

    public QTemplateType(PathMetadata metadata) {
        super(TemplateType.class, metadata);
    }

}

