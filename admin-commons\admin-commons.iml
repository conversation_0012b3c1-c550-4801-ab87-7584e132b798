<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="web" name="Web">
      <configuration>
        <webroots />
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="jpa" name="JPA">
      <configuration>
        <setting name="validation-enabled" value="true" />
        <setting name="provider-name" value="Hibernate" />
        <datasource-mapping>
          <factory-entry name="entityManagerFactory" />
        </datasource-mapping>
        <naming-strategy-map />
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-jpa:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: jakarta.transaction:jakarta.transaction-api:1.3.3" level="project" />
    <orderEntry type="library" name="Maven: jakarta.persistence:jakarta.persistence-api:2.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate:hibernate-core:5.4.32.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.4.2.Final" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.27.0-GA" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy:1.10.22" level="project" />
    <orderEntry type="library" name="Maven: antlr:antlr:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.jboss:jandex:2.2.3.Final" level="project" />
    <orderEntry type="library" name="Maven: org.dom4j:dom4j:2.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.common:hibernate-commons-annotations:5.1.2.Final" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jaxb:jaxb-runtime:2.3.4" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jaxb:txw2:2.3.4" level="project" />
    <orderEntry type="library" name="Maven: com.sun.istack:istack-commons-runtime:3.0.12" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.activation:jakarta.activation:1.2.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-jpa:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-orm:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aspects:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.14.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.14.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.31" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.28" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.12.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.12.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.12.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.12.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.12.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.12.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.48" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.48" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.48" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.3.8" level="project" />
    <orderEntry type="module" module-name="admin-core" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-validation:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.2.0.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.validation:jakarta.validation-api:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-security:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-config:5.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-core:5.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-web:5.5.1" level="project" />
    <orderEntry type="library" name="Maven: io.jsonwebtoken:jjwt:0.9.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-actuator:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator-autoconfigure:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-core:1.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.12" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.latencyutils:LatencyUtils:2.0.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: mysql:mysql-connector-java:8.0.25" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid-spring-boot-starter:1.1.22" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid:1.1.22" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: redis.clients:jedis:3.6.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.7" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.4.3" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib:3.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.ow2.asm:asm:7.1" level="project" />
    <orderEntry type="library" name="Maven: eu.bitwalker:UserAgentUtils:1.21" level="project" />
    <orderEntry type="library" name="Maven: com.github.penggle:kaptcha:2.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.jhlabs:filters:2.0.235-1" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel:2.2.10" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi:3.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml:3.17" level="project" />
    <orderEntry type="library" name="Maven: com.github.virtuald:curvesapi:1.04" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml-schemas:3.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xmlbeans:xmlbeans:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.ehcache:ehcache:3.9.4" level="project" />
    <orderEntry type="library" name="Maven: com.aliyun.oss:aliyun-sdk-oss:3.11.1" level="project" />
    <orderEntry type="library" name="Maven: org.jdom:jdom2:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jettison:jettison:1.1" level="project" />
    <orderEntry type="library" name="Maven: stax:stax-api:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.aliyun:aliyun-java-sdk-core:4.5.10" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.7" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.2" level="project" />
    <orderEntry type="library" name="Maven: javax.xml.bind:jaxb-api:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:javax.activation-api:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.jacoco:org.jacoco.agent:runtime:0.8.5" level="project" />
    <orderEntry type="library" name="Maven: org.ini4j:ini4j:0.5.4" level="project" />
    <orderEntry type="library" name="Maven: io.opentracing:opentracing-api:0.33.0" level="project" />
    <orderEntry type="library" name="Maven: io.opentracing:opentracing-util:0.33.0" level="project" />
    <orderEntry type="library" name="Maven: io.opentracing:opentracing-noop:0.33.0" level="project" />
    <orderEntry type="library" name="Maven: com.aliyun:aliyun-java-sdk-ram:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: com.aliyun:aliyun-java-sdk-kms:2.11.0" level="project" />
    <orderEntry type="library" name="Maven: org.freemarker:freemarker:2.3.31" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: com.querydsl:querydsl-apt:4.4.0" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: com.querydsl:querydsl-codegen:4.4.0" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: com.mysema.codegen:codegen:0.6.8" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: org.eclipse.jdt.core.compiler:ecj:4.3.1" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: org.reflections:reflections:0.9.9" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: com.google.code.findbugs:annotations:2.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.querydsl:querydsl-jpa:4.4.0" level="project" />
    <orderEntry type="library" name="Maven: com.querydsl:querydsl-core:4.4.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:18.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:jsr305:1.3.9" level="project" />
    <orderEntry type="library" name="Maven: com.mysema.commons:mysema-commons-lang:0.2.4" level="project" />
    <orderEntry type="library" name="Maven: com.infradna.tool:bridge-method-annotation:1.13" level="project" />
    <orderEntry type="library" name="Maven: javax.inject:javax.inject:1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.31" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.13" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.14" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.15" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okhttp3:okhttp:3.14.9" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okio:okio:1.17.5" level="project" />
    <orderEntry type="library" name="Maven: com.jamesmurty.utils:java-xmlbuilder:1.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.73" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.12.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-boot-starter:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-oas:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-annotations:2.1.2" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-models:2.1.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spi:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-schema:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-core:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-web:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.classgraph:classgraph:4.8.83" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-webmvc:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-webflux:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-common:3.0.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.mapstruct:mapstruct:1.3.1.Final" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-data-rest:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-bean-validators:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger2:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.5.20" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-models:1.5.20" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-ui:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-metadata:2.0.0.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.5.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.5.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.5.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.5.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.4.7" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:2.4.7" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: jakarta.activation:jakarta.activation-api:1.2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.19.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest:2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter:5.7.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-api:5.7.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.apiguardian:apiguardian-api:1.1.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.opentest4j:opentest4j:1.2.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-commons:1.7.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-params:5.7.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-engine:5.7.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-engine:1.7.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:3.9.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.10.22" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:3.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-junit-jupiter:3.9.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.3.8" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.3.8" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.8.2" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.20" level="project" />
    <orderEntry type="library" name="Maven: org.jdom:jdom:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nls:nls-sdk-tts:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nls:nls-sdk-common:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.1.65.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver:4.1.65.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.1.65.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.1.65.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec-http:4.1.65.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.1.65.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.1.65.Final" level="project" />
  </component>
</module>