package com.gl.service.order.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.order.repository.OrderRepository;
import com.gl.service.order.vo.ExcelOrder;
import com.gl.service.order.vo.OrderVo;
import com.gl.service.order.vo.dto.OrderDto;
import com.gl.util.GetShopRefUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("ALL")
@Service
@Slf4j
public class OrderService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderPayRecordService orderPayRecordService;

    @Autowired
    private GetShopRefUtil shopRefUtil;

    public Result list(OrderDto dto, Integer exportType) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (dto.getStatus() != null) {
                where.append(" and o.`status` = ? ");
                args.add(dto.getStatus());
            }
            if (dto.getShopId() != null) {
                where.append(" and o.`shop_id` = ? ");
                args.add(dto.getShopId());
            }
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and (o.out_trade_no like ?) ");
                args.add("%" + dto.getSearchCondition() + "%");
            }
            if (com.gl.framework.common.util.StringUtils.isNotBlank(dto.getBeginTime())) {
                where.append(" and o.time_expire >= ? ");
                args.add(dto.getBeginTime());
            }
            if (com.gl.framework.common.util.StringUtils.isNotBlank(dto.getEndTime())) {
                where.append(" and o.time_expire <= ? ");
                args.add(dto.getEndTime());
            }
        }

        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            where.append(String.format(" and ( o.user_id = %s or s.id in %s ) ",
                    shopRefUtil.getWxUserId(), SqlUtils.foreach("(", ")", ",", shopRef)));
        }

        String sql = "SELECT\n" +
                " o.id, \n " +
                "o.out_trade_no ,\n" +
                "  p.`name` packagesName,\n" +
                "  o.amount,\n" +
                "  u.nickname,\n" +
                "  u.phone,\n" +
                "  s.shop_name,\n" +
                "  d.`name` deviceName,\n" +
                "  d.sn deviceSn,\n" +
                "  o.`status`,\n" +
                "  o.create_time,\n" +
                "  o.time_expire,\n" +
                "  0 invoiceStatus \n" +
                "FROM\n" +
                "  dub_order o\n" +
                "  LEFT JOIN dub_device d ON d.id = o.device_id\n" +
                "  LEFT JOIN dub_shop s ON s.id = o.shop_id\n" +
                "  LEFT JOIN dub_paid_packages p ON p.id = o.package_id\n" +
                "  LEFT JOIN dub_wechat_user u ON u.id = o.user_id " +
                " WHERE 1=1 ";


        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }

        where.append("order by o.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<OrderVo> deviceVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(OrderVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", deviceVos);
        return result;
    }

    public void exportList(OrderDto dto, HttpServletResponse response) throws IOException {
        Result list = list(dto, 2);
        HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(list.getData()), HashMap.class);
        Object result = hashMap.get("result");
        List<ExcelOrder> excelOrders = null;
        if (result != null) {
            List<OrderVo> deviceVos = JSON.parseArray(JSON.toJSONString(result), OrderVo.class);
            excelOrders = deviceVos.stream().map(item -> {
                ExcelOrder excelOrder = new ExcelOrder();
                excelOrder.setOutTradeNo(item.getOutTradeNo());
                excelOrder.setPackagesName(item.getPackagesName());
                excelOrder.setAmount(item.getAmount());
                excelOrder.setNickname(StringUtils.isBlank(item.getNickname()) ? "" : item.getNickname());
                excelOrder.setPhone(StringUtils.isBlank(item.getPhone()) ? "" : item.getPhone());
                excelOrder.setDeviceName(item.getDeviceName());
                excelOrder.setShopName(item.getShopName());
                excelOrder.setStatus(item.getStatus() == 0 ? "支付失败" : item.getStatus() == 1 ? "已支付" : item.getStatus() == 2 ? "待支付" : item.getStatus() == 3 ? "取消支付" : "");
                excelOrder.setCreateTime(item.getCreateTime() == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(item.getCreateTime()));
                excelOrder.setTimeExpire(item.getTimeExpire());


                return excelOrder;
            }).collect(Collectors.toList());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = "订单管理_" + System.currentTimeMillis();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExcelOrder.class).sheet("订单管理").doWrite(excelOrders);
        //导出excel
        log.info("订单管理导出end");
    }

}
