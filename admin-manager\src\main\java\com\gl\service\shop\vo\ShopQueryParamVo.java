package com.gl.service.shop.vo;

import com.gl.framework.web.domain.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/2
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShopQueryParamVo extends BaseVo {

    /**
     * 店铺类型 1-总店 2-分店
     */
    private Integer type;

    /**
     * 绑定审核状态 0-审核不通过 1-审核通过\n2-待审核
     */
    private Integer status;

    /**
     * 店铺名称
     */
    private String shopName;

    private List<Long> shopIds;

}
