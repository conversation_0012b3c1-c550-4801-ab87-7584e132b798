package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QUserTextTemplate is a Querydsl query type for UserTextTemplate
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QUserTextTemplate extends EntityPathBase<UserTextTemplate> {

    private static final long serialVersionUID = 648164067L;

    public static final QUserTextTemplate userTextTemplate = new QUserTextTemplate("userTextTemplate");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath textContent = createString("textContent");

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QUserTextTemplate(String variable) {
        super(UserTextTemplate.class, forVariable(variable));
    }

    public QUserTextTemplate(Path<? extends UserTextTemplate> path) {
        super(path.getType(), path.getMetadata());
    }

    public QUserTextTemplate(PathMetadata metadata) {
        super(UserTextTemplate.class, metadata);
    }

}

