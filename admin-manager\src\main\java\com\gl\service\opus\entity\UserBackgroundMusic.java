package com.gl.service.opus.entity;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

/**
 * 背景音乐
 * @author: duanjinze
 * @date: 2022/11/10 12:00
 * @version: 1.0
 */
@Entity
@Table(name = "user_background_music")
@SQLDelete(sql = "update user_background_music set del_status = 1 where id = ? and user_id = ?")
@Where(clause = "del_status = 0")
public class UserBackgroundMusic {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 名称
     */
    @Basic
    @Column(name = "name")
    private String name;

    /**
     * 文件url
     */
    @Basic
    @Column(name = "music_url")
    private String musicUrl;

    /**
     * 0未删除 1已删除
     */
    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;

    /**
     * 创建人id
     */
    @Basic
    @Column(name = "user_id")
    private Long userId;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMusicUrl() {
        return musicUrl;
    }

    public void setMusicUrl(String musicUrl) {
        this.musicUrl = musicUrl;
    }

    public Integer getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(Integer delStatus) {
        this.delStatus = delStatus;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
