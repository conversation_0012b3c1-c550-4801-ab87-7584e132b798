package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAnchor is a Querydsl query type for Anchor
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAnchor extends EntityPathBase<Anchor> {

    private static final long serialVersionUID = -310604122L;

    public static final QAnchor anchor = new QAnchor("anchor");

    public final StringPath archorTag = createString("archorTag");

    public final StringPath emotion = createString("emotion");

    public final NumberPath<Integer> enableSubtitle = createNumber("enableSubtitle", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isEmotion = createNumber("isEmotion", Integer.class);

    public final StringPath name = createString("name");

    public final NumberPath<Integer> orderIndex = createNumber("orderIndex", Integer.class);

    public final NumberPath<Integer> rhoticAccent = createNumber("rhoticAccent", Integer.class);

    public final NumberPath<Integer> sampleRate = createNumber("sampleRate", Integer.class);

    public final NumberPath<Integer> supportVoiceType = createNumber("supportVoiceType", Integer.class);

    public final StringPath type = createString("type");

    public final StringPath typeName = createString("typeName");

    public final StringPath url = createString("url");

    public final StringPath usageScenario = createString("usageScenario");

    public final StringPath voiceName = createString("voiceName");

    public final StringPath voiceUrl = createString("voiceUrl");

    public QAnchor(String variable) {
        super(Anchor.class, forVariable(variable));
    }

    public QAnchor(Path<? extends Anchor> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAnchor(PathMetadata metadata) {
        super(Anchor.class, metadata);
    }

}

