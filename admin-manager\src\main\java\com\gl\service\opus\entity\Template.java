package com.gl.service.opus.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 模板表
 * @author: duanjinze
 * @date: 2022/11/10 16:13
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_template")
public class Template {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 模板类型id
     */
    @Basic
    @Column(name = "template_type_id")
    private Long templateTypeId;

    /**
     * 模板标题
     */
    @Basic
    @Column(name = "title")
    private String title;

    /**
     * 模板内容
     */
    @Basic
    @Column(name = "content",columnDefinition = "text")
    private String content;


    /**
     * 0未删除 1已删除
     */
    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;

    /**
     * 创建人
     */
    @Basic
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @Basic
    @Column(name = "shop_id")
    private Long shopId;
}
