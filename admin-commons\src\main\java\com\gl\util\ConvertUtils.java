package com.gl.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 转换工具类（继承自hutool中的Convert，可做拓展）
 *
 * <AUTHOR>
 */
public class ConvertUtils extends cn.hutool.core.convert.Convert {
	
	private static final BigDecimal ONE_HUNDRED = new BigDecimal("100");
	
	/**
	 * 金额分转元
	 * 
	 * @param fen
	 * @return
	 */
	public static BigDecimal fen2yuan(Integer fen) {
		return fen == null ? BigDecimal.ZERO : new BigDecimal(fen).divide(ONE_HUNDRED, 2, RoundingMode.HALF_UP);
	}
	
	/**
	 * 金额分转元
	 * 
	 * @param fen
	 * @return
	 */
	public static BigDecimal fen2yuan(Long fen) {
		return fen == null ? BigDecimal.ZERO : new BigDecimal(fen).divide(ONE_HUNDRED, 2, RoundingMode.HALF_UP);
	}
	
	/**
	 * 金额元转分
	 * 
	 * @param yuan
	 * @return
	 */
	public static int yuan2fen(BigDecimal yuan) {
		return yuan == null ? 0 : yuan.multiply(ONE_HUNDRED).intValue();
	}
	
}
