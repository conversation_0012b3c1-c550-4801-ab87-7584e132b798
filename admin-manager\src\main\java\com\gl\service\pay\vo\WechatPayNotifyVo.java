package com.gl.service.pay.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date: 2025/4/2
 * @description:
 *
 * {
 *     "id": "EV-2018022511223320873",
 *     "create_time": "2015-05-20T13:29:35+08:00",
 *     "resource_type": "encrypt-resource",
 *     "event_type": "TRANSACTION.SUCCESS",
 *     "summary": "支付成功",
 *     "resource": {
 *         "original_type": "transaction",
 *         "algorithm": "AEAD_AES_256_GCM",
 *         "ciphertext": "",
 *         "associated_data": "",
 *         "nonce": ""
 *     }
 * }
 *
 */
@Data
public class WechatPayNotifyVo {
    private String id;
    private String create_time;
    private String resource_type;
    private String event_type;
    private String summary;
    private Resource resource;
}

@Data
class Resource {
    private String original_type;
    private String algorithm;
    private String ciphertext;
    private String associated_data;
    private String nonce;
}
