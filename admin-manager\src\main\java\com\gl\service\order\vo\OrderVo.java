package com.gl.service.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderVo extends UserVo {

    private Long id;
    private String outTradeNo;
    private String packagesName;
    private Integer amount;
    private String nickname;
    private String phone;
    private String shopName;
    private String deviceName;
    private String deviceSn;
    private Integer status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date timeExpire;
    private Integer invoiceStatus;
}
