package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * 设备与语音包表
 * @author: duanjinze
 * @date: 2022/11/10 13:40
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_device_voice")
public class DeviceVoice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 类型 1作品 2设备自带
     */
    @Basic
    @Column(name = "type",columnDefinition = "smallint")
    private Integer type;

    /**
     *设备id
     */
    @Basic
    @Column(name = "device_id")
    private Long deviceId;

    /**
     *原作品id
     */
    @Basic
    @Column(name = "voice_work_id")
    private Long voiceWorkId;

    /**
     *设备音频id
     */
    @Basic
    @Column(name = "device_voice_id")
    private Long deviceVoiceId;

    /**
     * 标题
     */
    @Basic
    @Column(name = "title")
    private String title;

    /**
     * 作品文字
     */
    @Basic
    @Column(name = "content",columnDefinition = "text")
    private String content;

    /**
     * 语音包url
     */
    @Basic
    @Column(name = "voice_url")
    private String voiceUrl;

    /**
     *发音id
     */
    @Basic
    @Column(name = "voice_id")
    private Long voiceId;

    /**
     * 作品时长(秒)
     */
    @Basic
    @Column(name = "voice_time")
    private Integer voiceTime;

    /**
     * 语速
     */
    @Basic
    @Column(name = "speed")
    private Integer speed;

    /**
     * 语调
     */
    @Basic
    @Column(name = "volume")
    private Integer volume;


    /**
     * 音量
     */
    @Basic
    @Column(name = "pitch")
    private Integer pitch;

    /**
     * 背景音乐音量
     */
    @Basic
    @Column(name = "background_music_volume")
    private Integer backgroundMusicVolume;

    /**
     * 背景音乐id
     */
    @Basic
    @Column(name = "background_music_id")
    private Long backgroundMusicId;

    /**
     * 音频采样率
     */
    @Basic
    @Column(name = "sample_rate")
    private Integer sampleRate;

    /**
     * 顺序
     */
    @Basic
    @Column(name = "sortby")
    private Integer sortby;

    /**
     * 删除状态 0未删除 1已删除
     */
    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;
}
