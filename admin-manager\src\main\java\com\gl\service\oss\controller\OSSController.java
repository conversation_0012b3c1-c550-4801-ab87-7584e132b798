package com.gl.service.oss.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.web.response.Result;
import com.gl.service.oss.service.OSSService;

/**
 * 阿里云OSS对象存储
 * 
 * <AUTHOR>
 * @createDate: 2021年7月23日 下午2:08:20
 * @version: 1.0
 *
 */
@Controller
@RequestMapping("/oss")
public class OSSController {
	@Autowired
	private OSSService OSSService;

	/**
	 * 文件上传
	 * 
	 * @param file
	 * @param bucketName
	 * @param type
	 * 
	 * @return
	 * <AUTHOR>
	 * @date 2021年7月23日
	 */
	@PostMapping(value = "/file/{type}")
	@ResponseBody
	@Log(title = "OSS对象存储", businessType = BusinessType.UPDATE, businessTypeName = "上传")
	public Result putFile(@RequestParam("file") MultipartFile file, String bucketName, @PathVariable String type) {
		String url = OSSService.putFileToName(bucketName, file, type);
		return Result.success(url);
	}

	/**
	 * 文件删除
	 * 
	 * @param fileKey
	 * @param bucketName
	 * @retur
	 * <AUTHOR>
	 * @date 2021年7月23日
	 */
	@DeleteMapping(value = "/file")
	@ResponseBody
	@Log(title = "OSS对象存储", businessType = BusinessType.DELETE, businessTypeName = "删除")
	public Result deleteObject(@RequestParam(required = true) String fileKey,
			@RequestParam(required = false) String bucketName) {
		return OSSService.deleteObject(bucketName, fileKey);
	}
}
