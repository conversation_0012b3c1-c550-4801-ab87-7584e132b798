package com.gl.service.user.vo.dto;

import com.gl.framework.web.domain.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: duanjinze
 * @date: 2022/11/11 14:44
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WechatUserDto extends BaseVo {
    /**
     * 性别 0未知 1男 2女
     */
    private Integer gender;
    /**
     * 地区
     */
    private String address;

    /**
     * 搜索框 【昵称/手机】
     */
    private String searchCondition;

}
