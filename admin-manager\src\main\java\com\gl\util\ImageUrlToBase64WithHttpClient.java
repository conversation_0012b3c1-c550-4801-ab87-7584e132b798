package com.gl.util;

import java.io.IOException;
import java.util.Base64;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

/**
 * <AUTHOR>
 * @date: 2025/3/22
 * @description:
 */
public class ImageUrlToBase64WithHttpClient {

    public static String urlToBase64(String imageUrl) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(imageUrl);
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                throw new IOException("Response does not contain an entity.");
            }
            byte[] imageBytes = org.apache.commons.io.IOUtils.toByteArray(entity.getContent());
            return Base64.getEncoder().encodeToString(imageBytes);
        } finally {
            httpClient.close();
        }
    }

    public static void main(String[] args) {
        String imageUrl = "https://example.com/path/to/image.jpg"; // 替换为实际的图片 URL
        try {
            String base64 = urlToBase64(imageUrl);
            System.out.println("Base64 编码如下：");
            System.out.println(base64);
        } catch (IOException e) {
            System.err.println("转换过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
