<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="admin-core" />
        <module name="admin-commons" />
        <module name="py-manager" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="admin" target="1.8" />
      <module name="admin-commons" target="1.8" />
      <module name="admin-core" target="1.8" />
      <module name="py-manager" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="admin" options="-parameters" />
      <module name="admin-commons" options="-parameters" />
      <module name="admin-core" options="-parameters" />
      <module name="py-manager" options="-parameters" />
    </option>
  </component>
</project>