package com.gl.framework.web.response;

import java.util.HashMap;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import com.gl.framework.exception.CustomException;
import com.gl.framework.util.ResultUtils;

@RestControllerAdvice(basePackages = "com.gl")
public class ResponseAdvice implements ResponseBodyAdvice {

    private static final Logger log = LoggerFactory.getLogger(ResponseAdvice.class);

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        if (Objects.isNull(o)) {
            return Result.success();
        }

        if (o instanceof Result) {
            return o;
        }
        
        // 特殊返回数据处理
        if (o instanceof HashMap) {
        	HashMap map = (HashMap) o;
        	if("ok".equals(map.get("code"))){
        		return o;
        	}
        }
        return Result.success(o);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
    public Result exceptionHandler(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        return Result.fail(e.getMessage());
    }

    /**
     * 针对业务异常统一处理
     *
     * @param request
     * @param bizException
     * @return
     */
    @ExceptionHandler(CustomException.class)
    @ResponseStatus(code = HttpStatus.EXPECTATION_FAILED)
    public Result bizExceptionHandler(HttpServletRequest request, CustomException bizException) {
        int errorCode = bizException.getCode();
        log.error("catch bizException {}", errorCode);
        return Result.fail(errorCode, bizException.getMessage());
    }


    /**
     * 针对Validate校验异常统一处理
     *
     * @param request
     * @param methodArgumentNotValidException
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public Result methodArgumentNotValidExceptionExceptionHandler(HttpServletRequest request, MethodArgumentNotValidException methodArgumentNotValidException) {
        log.error("catch methodArgumentNotValidException :" + methodArgumentNotValidException.getMessage(), methodArgumentNotValidException);
        return ResultUtils.getFailResult(methodArgumentNotValidException.getBindingResult(), Result.fail());
    }

    /**
     * 针对Assert断言异常统一处理
     *
     * @param request
     * @param illegalArgumentExceptionException
     * @return
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(code = HttpStatus.EXPECTATION_FAILED)
    public Result illegalArgumentExceptionHandler(HttpServletRequest request, IllegalArgumentException illegalArgumentExceptionException) {
        log.error("illegalArgumentExceptionException:" + illegalArgumentExceptionException.getMessage(), illegalArgumentExceptionException);
        return  Result.fail(illegalArgumentExceptionException.getMessage());
    }
}
