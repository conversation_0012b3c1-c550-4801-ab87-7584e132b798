package com.gl.service.installationPackage.vo.installationPackage.dto;

import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class InstallationPackageAddDto extends UserVo {

    private Long id;

    private String versionName;

    private String remark;

    private String packageUrl;

    private List<Long> ids;
}
