package com.gl.service.packet.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.config.oss.OssConfig;
import com.gl.framework.web.response.Result;
import com.gl.service.device.vo.DeviceVo;
import com.gl.service.opus.repository.VoicePacketRepository;
import com.gl.service.opus.repository.VoiceWorkRepository;
import com.gl.service.oss.service.OSSService;
import com.gl.service.packet.vo.VoicePacketVo;
import com.gl.service.packet.vo.dto.VoicePacketDto;
import com.gl.util.GetShopRefUtil;
import com.gl.util.ZipUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 语音包管理
 * 1.导出
 *
 * @author: duanjinze
 * @date: 2022/11/11 11:51
 * @version: 1.0
 */
@Service
@Slf4j
public class VoicePacketService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private VoicePacketRepository voicePacketRepository;
    @Autowired
    private VoiceWorkRepository voiceWorkRepository;
    @Autowired
    private OSSService ossService;
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private GetShopRefUtil shopRefUtil;

    private static final int BUFFER_SIZE = 4096;

    /**
     * 1.列表查询
     * 2.删除
     * 3.应用设备
     * 4.导出（语音zip包）
     */
    public Result list(VoicePacketDto dto, Integer exportType) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {

            if (dto.getShopId() != null) {
                where.append(" and s.id = ? ");
                args.add(dto.getShopId());
            }

            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and (vp.name like ? or wu.nickname like ? or wu.phone like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }
        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            where.append(String.format(" AND s.id in %s", SqlUtils.foreach("(", ")", ",", shopRef)));
        }


        String sql = "SELECT vp.*,wu.nickname ,wu.phone,vw.create_time,s.shop_name \n" +
                " FROM dub_voice_packet vp\n" +
                "LEFT JOIN dub_voice_work vw\n" +
                "ON vp.id = vw.voice_id\n" +
                "LEFT JOIN dub_wechat_user wu\n" +
                "ON wu.id = vw.user_id\n" +
                "LEFT JOIN dub_shop s ON s.id = vp.shop_id \n" +
                "WHERE vw.del_status != 1\n";
        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        where.append(" order by vw.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<VoicePacketVo> voicePacketVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(VoicePacketVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", voicePacketVos);
        return result;
    }

    public Result delete(VoicePacketDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getId() == null) {
            return Result.fail("语音包id不能为空");
        }
        //作品语音包，在语音包管理列表，可以删除(记得删除oss中的数据)，删除后同步删除作品
        //删除语音包
        voicePacketRepository.deleteById(dto.getId());
        //删除oss

        //删除作品
        voiceWorkRepository.updateDelStatusByVoiceId(dto.getId());
        return Result.success();
    }

    public Result detail(Long id) {
        if (id == null) {
            return Result.fail("语音包id不能为空");
        }
        String sql = "SELECT vp.id,dd.name,dd.sn FROM dub_voice_packet vp\n" +
                "LEFT JOIN dub_device_voice ddv\n" +
                "ON vp.id = ddv.voice_id \n" +
                "LEFT JOIN dub_device dd\n" +
                "ON ddv.device_id = dd.id\n" +
                "WHERE ddv.del_status != 1 and dd.del_status != 1 and vp.id = ? ";
        List<DeviceVo> deviceVos = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DeviceVo.class), id);
        return Result.success(deviceVos);
    }

   /* public void plistDownLoad(String urls,HttpServletResponse response){

        String[] split = urls.split(",");
        List<String> paths = Arrays.asList(split);
        //拿到文件下载url
        if (paths.size() != 0) {
            for (String path : paths) {
                //1.将oss路径传过来，再下载在一个包下
               ossService.getObjectFilePacket(null, path);
            }
            //2.压缩，压缩完把zip原来包里的东西全部删除
            FileUtil.fileToZip(ossConfig.getDownFilePacketdir(),"C:/lw/11111/","语音包_"+System.currentTimeMillis());

            delAllFile(ossConfig.getDownFilePacketdir());
        }
    }*/

    /**
     * 删除指定文件夹下的所有文件
     *
     * @param path
     * @return
     */
    public static boolean delAllFile(String path) {
        boolean flag = false;
        File file = new File(path);
        if (!file.exists()) {
            return flag;
        }
        if (!file.isDirectory()) {
            return flag;
        }
        String[] tempList = file.list();
        File temp;
        if (tempList != null) {
            for (String s : tempList) {
                if (path.endsWith(File.separator)) {
                    temp = new File(path + s);
                } else {
                    temp = new File(path + File.separator + s);
                }
                if (temp.isFile()) {
                    temp.delete();
                }
            }
        }
        return flag;
    }


    /**
     * 压缩文件
     *
     * @param
     * @param
     * @param
     * @throws IOException
     */
    public void plistDownLoad(VoicePacketDto dto, HttpServletResponse response) throws IOException {
        log.info("plistDownLoad, dto={}", JSON.toJSONString(dto));
        long time = System.currentTimeMillis();

        // 验证临时保存目录是否存在
        File dir = new File(ossConfig.getDownFilePacketdir() + time);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        Result list = list(dto, 2);

        HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(list.getData()), HashMap.class);
        Object result = hashMap.get("result");
        if (result != null) {
            //所有语音包
            List<VoicePacketVo> deviceVos = JSON.parseArray(JSON.toJSONString(result), VoicePacketVo.class);
            List<String> paths = deviceVos.stream().map(VoicePacketVo::getFileUrl).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment; filename=" +
                    URLEncoder.encode("语音包_" + System.currentTimeMillis() + ".zip", "UTF-8"));

            FileInputStream fis = null;
            BufferedInputStream bis = null;
            List<File> list1 = new ArrayList<>();
            //拿到文件下载url
            if (!paths.isEmpty()) {
                deviceVos.stream().filter(e -> e.getFileUrl() != null).forEach(e -> {
                    //1.将oss路径传过来，再下载在一个包下
                    File upFile = ossService.getObjectFilePacket(null, e.getFileUrl(), time, e.getName());
                    if (upFile != null) {
                        list1.add(upFile);
                    }
                });
            }

            // 压缩并写入响应流
            ZipUtils.toZip(list1, response.getOutputStream());

            try {
                // 删除临时文件
                deleteDir(dir);

            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        }
    }

    /**
     * 删除临时文件与目录
     *
     * @param directory
     */
    public static void deleteDir(File directory) {
        //获取目录下所有文件和目录
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDir(file);
                } else {
                    file.delete();
                    log.info("{}：：文件已删除", file.getName());
                }
            }
        }
        //最终把该目录也删除
        directory.delete();
        log.info("{}：：目录已删除", directory.getName());
    }
}

