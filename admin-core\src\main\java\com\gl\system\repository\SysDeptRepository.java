package com.gl.system.repository;

import com.gl.system.entity.SysDept;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface SysDeptRepository extends JpaRepository<SysDept, Long>, JpaSpecificationExecutor<SysDept> {

	List<SysDept> findByIdIn(List<Long> ids);

	/**
	 * 根据角色ID查询部门树信息
	 *
	 * @param roleId
	 * @return
	 */
	@Query(value = "SELECT d.id, d.parent_id \n" + //
				   "FROM sys_dept d \n" + //
				   "    LEFT JOIN sys_role_dept rd ON d.id = rd.dept_id \n" + //
				   "WHERE rd.role_id = ?1 \n" + //
				   //" 	AND d.id NOT IN (SELECT d.parent_id FROM sys_dept d INNER JOIN sys_role_dept rd ON d.id = rd.dept_id AND rd.role_id = ?1) \n" + //
				   "ORDER BY d.parent_id, d.sort_num", nativeQuery = true)
	List<SysDept> selectDeptListByRoleId(Long roleId);
	
	/**
	 * 根据用户ID查询部门树信息
	 *
	 * @param userId
	 * @return
	 */
	@Query(value = "SELECT d.id, d.parent_id "
			+ "FROM sys_dept d "
			+ "LEFT JOIN sys_user_dept ud ON d.id = ud.dept_id "
			+ "WHERE ud.user_id = ?1 "
			/*+ "AND d.id NOT IN (SELECT d.parent_id FROM sys_dept d "
			+ "INNER JOIN sys_user_dept ud ON d.id = ud.dept_id AND ud.user_id = ?1) "*/
			+ "ORDER BY d.parent_id, d.sort_num", nativeQuery = true)
	List<SysDept> selectDeptListByUserId(Long userId);

	List<SysDept> findByParentIdIn(List<Long> parentIds);

	/**
	 * 查询子节点数
	 *
	 * @param parentId
	 * @return
	 */
	int countByParentId(Long parentId);

	SysDept findByDeptNameAndParentId(String deptName, Long parentId);

	SysDept findByDeptNameAndParentIdAndDeleted(String deptName, Long parentId,Integer deleted);

	/**
	 * 查询子部门集合
	 *
	 * @param id
	 * @return
	 */
	@Query(value = "SELECT * FROM sys_dept WHERE FIND_IN_SET(?1, ancestors)", nativeQuery = true)
	List<SysDept> selectChildrenDeptById(Long id);


	@Query(value = "select d.ancestors from SysDept d where d.id = ?1")
	String findAncestorsById(Long id);

	@Query(value = "select d.id from SysDept d where d.ancestors like ?1")
	List<Long> findByAncestorsLike(String ancestros);

}
