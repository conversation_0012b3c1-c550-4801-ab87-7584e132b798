package com.gl.service.webhook.controller;

import com.gl.service.webhook.service.WebhookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Slf4j
@Controller
@RequestMapping("/emqx")
public class WebhookController {

    @Autowired
    private WebhookService webhookService;

    @PostMapping("/webhook")
    @ResponseBody
    public void webhook(@RequestBody Map<String, Object> params) {
        log.info("EMQX Webhook 参数列表 {}", params);
        String action = (String) params.get("event");
        String clientid = (String) params.get("clientid");

        if (action == null || action.isEmpty() || clientid == null || clientid.isEmpty()) {
            return;
        }
        Integer status = null;
        if ("client.connected".equals(action)) {
            status = 1;
            log.info("client:{} 上线", clientid);
        }
        if ("client.disconnected".equals(action)) {
            status = 0;
            log.info("client:{} 下线", clientid);
        }

        if (status == null) {
            return;
        }

        webhookService.updateStatus(clientid, status);
        log.info("更新{} 上下线状态: {}", clientid, status);
    }
}
