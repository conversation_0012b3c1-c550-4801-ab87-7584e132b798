# 项目相关配置
project:
  # 名称
  name: py-manager
  # 版本
  version: 1.0
  # 验证码类型: math 数组计算; char 字符验证
  captchaType: math
  # 获取地理地址开关
  addressEnabled: true

# 环境配置
server:
  # 服务器的HTTP端口
  port: 8770
  servlet:
    # 应用的访问路径
    context-path: /

#  日志配置：TRACE < DEBUG < INFO < WARN < ERROR < FATAL
logging:
  level:
    # root级别：指项目所有日志级别
    root: INFO
    # package级别：指定包下的日志级别
    com.gladmin: INFO
    org.springframework.web: INFO
    org.springframework.jdbc.core.JdbcTemplate: DEBUG

# Spring配置
spring:
  application:
    # 项目名称
    name: py-manager
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    # 激活哪个配置文件dev
    active: dev
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  main:
    allow-bean-definition-overriding: true
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  data:
    redis:
      repositories:
        # 标识repository类不是操作redis而是操作数据库的
        enabled: false



http_max_total: 800
http_default_max_perRoute: 80
http_validate_after_inactivity: 1000
http_connection_request_timeout: 5000
http_connection_timeout: 10000
http_socket_timeout: 20000
waitTime: 30000
idleConTime: 3
retryCount: 3
