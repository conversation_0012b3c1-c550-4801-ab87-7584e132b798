package com.gl.service.shop.repository;

import com.gl.service.shop.entity.Shop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ShopRepository extends JpaRepository<Shop,Long>, JpaSpecificationExecutor<Shop> {


    @Transactional
    @Modifying
    @Query(value = "update dub_shop set status = ?2 where id = ?1 ",nativeQuery = true)
    Integer updateStatusById(Long id, Integer status);

    @Modifying
    @Query("delete from Shop where id in ?1")
    void deleteByIds(List<Long> shopIds);

    @Query("select s from Shop s where s.parentId in ?1")
    List<Shop> selectByParentIdIn(List<Long> shopIds);

    @Query("select s from Shop s where s.parentId = ?1")
    List<Shop> findShopsByParentId(Long shopId);

    List<Shop> findByIdInOrCreateUserId(List<Long> shopRef, Long wxUserId);
}
