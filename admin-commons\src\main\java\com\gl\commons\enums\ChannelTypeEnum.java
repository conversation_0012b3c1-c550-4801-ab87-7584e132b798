package com.gl.commons.enums;

public enum ChannelTypeEnum {
    JU_LIANG(1, "巨量"),
    GUANG_DIAN_TONG(2, "广点通"),
    AI_FAN_FAN(3, "爱番番");

    public final int value;
    public final String name;

    ChannelTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getName(int value) {
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (channelTypeEnum.getValue() == value) {
                return channelTypeEnum.name;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
