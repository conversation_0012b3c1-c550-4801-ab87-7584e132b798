package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 模板类型
 * @author: duanjinze
 * @date: 2022/11/10 18:56
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_template_type")
public class TemplateType {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 类型名称
     */
    @Basic
    @Column(name = "name")
    private String name;

    /**
     * 0未删除 1已删除
     */
    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;

    /**
     * 创建人
     */
    @Basic
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

}
