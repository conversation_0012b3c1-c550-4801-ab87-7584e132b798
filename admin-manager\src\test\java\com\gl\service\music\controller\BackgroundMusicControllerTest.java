package com.gl.service.music.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.framework.web.response.ResultCode;
import com.gl.service.music.service.BackgroundMusicService;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.music.vo.dto.BackgroundMusicDto;
import com.gl.service.opus.entity.BackgroundMusicType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * BackgroundMusicController单元测试类
 * 
 * 测试覆盖范围：
 * 1. list方法 - GET /music - 背景音乐列表查询
 * 2. delete方法 - DELETE /music - 背景音乐删除
 * 3. add方法 - POST /music - 背景音乐添加
 * 4. findBackgroundMusicType方法 - GET /music/type - 背景音乐类型查询
 * 
 * <AUTHOR> Generator
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = com.gl.ManagerApplication.class)
@AutoConfigureMockMvc
@DisplayName("BackgroundMusicController单元测试")
class BackgroundMusicControllerTest {

        @Autowired
        private MockMvc mockMvc;

        @MockBean
        private BackgroundMusicService backgroundMusicService;

        @Autowired
        private ObjectMapper objectMapper;

        private BackgroundMusicDto testDto;
        private BackGroundMusicVo testVo;
        private BackgroundMusicType testType;
        private Result successResult;
        private Result failResult;

        @BeforeEach
        void setUp() {
                setupTestData();
        }

        /**
         * 初始化测试数据
         */
        private void setupTestData() {
                // 初始化DTO
                testDto = new BackgroundMusicDto();
                testDto.setPageNumber(0);
                testDto.setPageSize(10);
                testDto.setSearchCondition("测试音乐");
                testDto.setShopId(1L);
                testDto.setBackgroundMusicId(1L);

                // 初始化VO
                testVo = new BackGroundMusicVo();
                testVo.setId(1L);
                testVo.setTypeId(1L);
                testVo.setName("测试背景音乐");
                testVo.setMusicUrl("test/music.wav");
                testVo.setShopId(1L);
                testVo.setMusicTime(120);
                testVo.setCreateTime(new Date());

                // 初始化类型
                testType = new BackgroundMusicType();
                testType.setId(1L);
                testType.setName("测试类型");

                // 初始化结果
                successResult = Result.success();
                Map<String, Object> data = new HashMap<>();
                data.put("total", 1);
                data.put("result", Arrays.asList(testVo));
                successResult.addData("total", 1);
                successResult.addData("result", Arrays.asList(testVo));

                failResult = Result.fail("操作失败");
        }

        // ==================== list方法测试 ====================

        @Test
        @DisplayName("测试list方法 - 正常查询返回数据")
        @WithMockUser(authorities = "music:music:list")
        void testList_Success() throws Exception {
                // Given - 准备测试数据
                when(backgroundMusicService.list(any(BackgroundMusicDto.class))).thenReturn(successResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music")
                                .param("pageNumber", "0")
                                .param("pageSize", "10")
                                .param("searchCondition", "测试音乐")
                                .param("shopId", "1")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()))
                                .andExpect(jsonPath("$.message").value("success"));
        }

        @Test
        @DisplayName("测试list方法 - 无权限访问")
        @WithMockUser(authorities = "other:permission")
        void testList_NoPermission() throws Exception {
                // When & Then - 执行测试并验证权限拒绝
                mockMvc.perform(get("/music")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isForbidden());
        }

        @Test
        @DisplayName("测试list方法 - 未认证用户")
        void testList_Unauthenticated() throws Exception {
                // When & Then - 执行测试并验证未认证
                mockMvc.perform(get("/music")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isUnauthorized());
        }

        @Test
        @DisplayName("测试list方法 - 服务层返回失败")
        @WithMockUser(authorities = "music:music:list")
        void testList_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.list(any(BackgroundMusicDto.class))).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        // ==================== delete方法测试 ====================

        @Test
        @DisplayName("测试delete方法 - 正常删除成功")
        @WithMockUser(authorities = "music:music:delete")
        void testDelete_Success() throws Exception {
                // Given - 准备测试数据
                when(backgroundMusicService.delete(any(BackgroundMusicDto.class))).thenReturn(successResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(delete("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
        }

        @Test
        @DisplayName("测试delete方法 - 无权限访问")
        @WithMockUser(authorities = "other:permission")
        void testDelete_NoPermission() throws Exception {
                // When & Then - 执行测试并验证权限拒绝
                mockMvc.perform(delete("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isForbidden());
        }

        @Test
        @DisplayName("测试delete方法 - 请求体为空")
        @WithMockUser(authorities = "music:music:delete")
        void testDelete_EmptyBody() throws Exception {
                // When & Then - 执行测试并验证结果
                mockMvc.perform(delete("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{}"))
                                .andDo(print())
                                .andExpect(status().isOk());
        }

        @Test
        @DisplayName("测试delete方法 - 服务层返回失败")
        @WithMockUser(authorities = "music:music:delete")
        void testDelete_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.delete(any(BackgroundMusicDto.class))).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(delete("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        // ==================== add方法测试 ====================

        @Test
        @DisplayName("测试add方法 - 正常添加成功")
        @WithMockUser(authorities = "music:music:add")
        void testAdd_Success() throws Exception {
                // Given - 准备测试数据
                when(backgroundMusicService.add(any(BackGroundMusicVo.class))).thenReturn(successResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
        }

        @Test
        @DisplayName("测试add方法 - 无权限访问")
        @WithMockUser(authorities = "other:permission")
        void testAdd_NoPermission() throws Exception {
                // When & Then - 执行测试并验证权限拒绝
                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andDo(print())
                                .andExpect(status().isForbidden());
        }

        @Test
        @DisplayName("测试add方法 - 未认证用户")
        void testAdd_Unauthenticated() throws Exception {
                // When & Then - 执行测试并验证未认证
                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andDo(print())
                                .andExpect(status().isUnauthorized());
        }

        @Test
        @DisplayName("测试add方法 - 请求体为空")
        @WithMockUser(authorities = "music:music:add")
        void testAdd_EmptyBody() throws Exception {
                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{}"))
                                .andDo(print())
                                .andExpect(status().isOk());
        }

        @Test
        @DisplayName("测试add方法 - 服务层返回失败")
        @WithMockUser(authorities = "music:music:add")
        void testAdd_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.add(any(BackGroundMusicVo.class))).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        @Test
        @DisplayName("测试add方法 - 无效JSON格式")
        @WithMockUser(authorities = "music:music:add")
        void testAdd_InvalidJson() throws Exception {
                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("invalid json"))
                                .andDo(print())
                                .andExpect(status().isBadRequest());
        }

        // ==================== findBackgroundMusicType方法测试 ====================

        @Test
        @DisplayName("测试findBackgroundMusicType方法 - 正常获取类型列表")
        void testFindBackgroundMusicType_Success() throws Exception {
                // Given - 准备测试数据
                Result typeResult = Result.success(Arrays.asList(testType));
                when(backgroundMusicService.findBackgroundMusicType()).thenReturn(typeResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music/type")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()))
                                .andExpect(jsonPath("$.data").isArray())
                                .andExpect(jsonPath("$.data[0].id").value(1))
                                .andExpect(jsonPath("$.data[0].name").value("测试类型"));
        }

        @Test
        @DisplayName("测试findBackgroundMusicType方法 - 服务层返回失败")
        void testFindBackgroundMusicType_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.findBackgroundMusicType()).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music/type")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        @Test
        @DisplayName("测试findBackgroundMusicType方法 - 服务层抛出异常")
        void testFindBackgroundMusicType_ServiceException() throws Exception {
                // Given - 模拟服务层抛出异常
                when(backgroundMusicService.findBackgroundMusicType())
                                .thenThrow(new RuntimeException("数据库连接异常"));

                // When & Then - 执行测试并验证异常处理
                mockMvc.perform(get("/music/type")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isInternalServerError());
        }

        // ==================== 集成测试 ====================

        @Test
        @DisplayName("测试完整的CRUD流程")
        @WithMockUser(authorities = { "music:music:list", "music:music:add", "music:music:delete" })
        void testFullCrudFlow() throws Exception {
                // 1. 获取类型列表
                Result typeResult = Result.success(Arrays.asList(testType));
                when(backgroundMusicService.findBackgroundMusicType()).thenReturn(typeResult);

                mockMvc.perform(get("/music/type"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

                // 2. 添加背景音乐
                when(backgroundMusicService.add(any(BackGroundMusicVo.class))).thenReturn(successResult);

                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

                // 3. 查询背景音乐列表
                when(backgroundMusicService.list(any(BackgroundMusicDto.class))).thenReturn(successResult);

                mockMvc.perform(get("/music"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

                // 4. 删除背景音乐
                when(backgroundMusicService.delete(any(BackgroundMusicDto.class))).thenReturn(successResult);

                mockMvc.perform(delete("/music")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
        }

        // ==================== 边界条件测试 ====================

        @Test
        @DisplayName("测试HTTP方法不匹配")
        @WithMockUser(authorities = "music:music:list")
        void testWrongHttpMethod() throws Exception {
                // When & Then - 使用错误的HTTP方法
                mockMvc.perform(post("/music/type")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isMethodNotAllowed());
        }

        @Test
        @DisplayName("测试不支持的Content-Type")
        @WithMockUser(authorities = "music:music:add")
        void testUnsupportedContentType() throws Exception {
                // When & Then - 使用不支持的Content-Type
                mockMvc.perform(post("/music")
                                .with(csrf())
                                .contentType(MediaType.TEXT_PLAIN)
                                .content("plain text content"))
                                .andDo(print())
                                .andExpect(status().isUnsupportedMediaType());
        }
}
