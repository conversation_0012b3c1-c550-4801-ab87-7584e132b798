package com.gl.service.order.repository;

import com.gl.service.order.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface OrderRepository extends JpaRepository<Order,Long>, JpaSpecificationExecutor<Order> {

    @Transactional
    @Modifying
    @Query(value = "update dub_order set  time_expire = null , status = 1 where id =  ?1 ",nativeQuery = true)
    Integer paySuccess(Long orderId);

    List<Order> findByOutTradeNoAndStatus(String outOrderNo, Integer status);
}
