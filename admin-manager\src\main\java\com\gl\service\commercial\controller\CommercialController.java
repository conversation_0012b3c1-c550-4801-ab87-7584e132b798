package com.gl.service.commercial.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.commercial.service.CommercialService;
import com.gl.service.commercial.vo.dto.CommercialDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/*
* 商户管理
* */
@Controller
@RequestMapping("/commercial")
public class CommercialController {

    @Autowired
    private CommercialService commercialService;

    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:commercial:list')")
    public Result list(CommercialDto dto) {
        return commercialService.list(dto, 1);
    }

    @PostMapping("/export")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:commercial:export')")
    public void exportList(@RequestBody CommercialDto dto, HttpServletResponse response) throws IOException {
        commercialService.exportList(dto,response);
    }



}
