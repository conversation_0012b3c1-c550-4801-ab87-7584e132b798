package com.gl.service.shop.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.opus.vo.MergeVo;
import com.gl.service.shop.controller.request.TextTemplateDelReq;
import com.gl.service.shop.service.WeChatService;
import com.gl.service.shop.controller.request.FollowAnchorReq;
import com.gl.service.shop.controller.request.FollowBgmReq;
import com.gl.service.shop.controller.request.TextTemplateAddReq;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/api/wechat")
public class WeChatController {

    @Resource
    private WeChatService weChatService;

    @GetMapping("/login")
    public Map<String, Object> webLogon(HttpServletRequest request) {
        return weChatService.webLogin(request);
    }

    // 微信回调接口
    @GetMapping("/callback")
    public Map<String, Object> callback(@RequestParam("code") String code, @RequestParam("state") String state,
            HttpServletRequest request) throws Exception {
        return weChatService.handleCallback(code, state);
    }

    @GetMapping("/check-login/{uuid}")
    public Map<String, Object> checkLoginStatus(@PathVariable String uuid) {
        return weChatService.checkLoginStatus(uuid);
    }

    @GetMapping("/updateWx")
    public Map<String, Object> updateWx(HttpServletRequest request) {
        return weChatService.updateWx(request);
    }

    @GetMapping("/update/callback")
    public Map<String, Object> updateCallback(@RequestParam("code") String code, @RequestParam("state") String state)
            throws Exception {
        return weChatService.updateCallback(code);
    }

    @GetMapping("getMyLongAnchor")
    public Result getMyLongAnchor() {
        return weChatService.getMyLongAnchor();
    }

    @GetMapping("getMyAnchor")
    public Result getMyVipAnchor() {
        return weChatService.getMyVipAnchor();
    }

    @PostMapping("followAnchor")
    public Result followAnchor(@RequestBody FollowAnchorReq followAnchorReq) {
        return weChatService.followAnchor(followAnchorReq);
    }

    @PostMapping("followLong")
    public Result followLongAnchor(@RequestBody FollowAnchorReq followAnchorReq) {
        return weChatService.followLongAnchor(followAnchorReq);
    }

    @PostMapping("followBgm")
    public Result followBgm(@RequestBody FollowBgmReq followBgmReq) {
        return weChatService.followBgm(followBgmReq);
    }

    @GetMapping("getFollowBgm")
    public Result getFollowBgm() {
        return weChatService.getFollowBgm();
    }

    @PostMapping("addTextTemplate")
    public Result addTextTemplate(@RequestBody TextTemplateAddReq textTemplateAddReq) {
        return weChatService.addTextTemplate(textTemplateAddReq);
    }

    @GetMapping("getTextTemplate")
    public Result getTextTemplate() {
        return weChatService.getTextTemplate();
    }

    @DeleteMapping("delTextTemplate")
    public Result delTextTemplate(@RequestBody TextTemplateDelReq textTemplateDelReq) {
        return weChatService.delTextTemplate(textTemplateDelReq);
    }

}
