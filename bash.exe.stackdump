Stack trace:
Frame         Function      Args
0007FFFFB6C0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5C0) msys-2.0.dll+0x2118E
0007FFFFB6C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6C0  0002100469F2 (00021028DF99, 0007FFFFB578, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6C0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6C0  00021006A545 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF2BDF0000 ntdll.dll
7FFF2B5A0000 KERNEL32.DLL
7FFF29480000 KERNELBASE.dll
000210040000 msys-2.0.dll
7FFF2BBE0000 USER32.dll
7FFF29450000 win32u.dll
7FFF2BBB0000 GDI32.dll
7FFF298E0000 gdi32full.dll
7FFF29840000 msvcp_win.dll
7FFF29A00000 ucrtbase.dll
7FFF2A280000 advapi32.dll
7FFF2A630000 msvcrt.dll
7FFF2AC80000 sechost.dll
7FFF292B0000 bcrypt.dll
7FFF2BA90000 RPCRT4.dll
7FFF287C0000 CRYPTBASE.DLL
7FFF29230000 bcryptPrimitives.dll
7FFF2A240000 IMM32.DLL
