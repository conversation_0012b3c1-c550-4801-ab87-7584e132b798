-------------------------------------------------------------------------------
Test set: com.gl.service.commercial.service.CommercialServiceTest
-------------------------------------------------------------------------------
Tests run: 13, Failures: 1, Errors: 10, Skipped: 0, Time elapsed: 2.867 s <<< FAILURE! - in com.gl.service.commercial.service.CommercialServiceTest
testList_QueryResultNull_ShouldReturnEmptyResult  Time elapsed: 1.594 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultNull_ShouldReturnEmptyResult(CommercialServiceTest.java:192)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultNull_ShouldReturnEmptyResult(CommercialServiceTest.java:196)

testExportList_DataWithNullFields_ShouldHandleNullValues  Time elapsed: 0.051 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_DataWithNullFields_ShouldHandleNullValues(CommercialServiceTest.java:443)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_DataWithNullFields_ShouldHandleNullValues(CommercialServiceTest.java:450)

testList_NullDto_ShouldQueryWithoutFilters  Time elapsed: 0.003 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_NullDto_ShouldQueryWithoutFilters(CommercialServiceTest.java:283)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_NullDto_ShouldQueryWithoutFilters(CommercialServiceTest.java:289)

testList_ExportType_ShouldReturnAllResults  Time elapsed: 0.007 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_ExportType_ShouldReturnAllResults(CommercialServiceTest.java:221)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_ExportType_ShouldReturnAllResults(CommercialServiceTest.java:227)

testExportList_IOExceptionThrown_ShouldPropagateException  Time elapsed: 0.032 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 应该抛出IOException ==> Unexpected exception type thrown ==> expected: <java.io.IOException> but was: <org.mockito.exceptions.misusing.PotentialStubbingProblem>
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_IOExceptionThrown_ShouldPropagateException(CommercialServiceTest.java:478)
Caused by: org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_IOExceptionThrown_ShouldPropagateException(CommercialServiceTest.java:471)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.lambda$testExportList_IOExceptionThrown_ShouldPropagateException$0(CommercialServiceTest.java:479)
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_IOExceptionThrown_ShouldPropagateException(CommercialServiceTest.java:478)

testExportList_NormalExport_ShouldGenerateExcelFile  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_NormalExport_ShouldGenerateExcelFile(CommercialServiceTest.java:354)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_NormalExport_ShouldGenerateExcelFile(CommercialServiceTest.java:361)

testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile(CommercialServiceTest.java:382)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile(CommercialServiceTest.java:387)

testList_VariousQueryConditions_ShouldBuildCorrectSql  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    100L,
    "%搜索条件%",
    "%搜索条件%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_VariousQueryConditions_ShouldBuildCorrectSql(CommercialServiceTest.java:323)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_VariousQueryConditions_ShouldBuildCorrectSql(CommercialServiceTest.java:329)

testList_EmptyShopRefList_ShouldQueryWithoutShopFilter  Time elapsed: 0.012 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ? GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59"
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_EmptyShopRefList_ShouldQueryWithoutShopFilter(CommercialServiceTest.java:252)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_EmptyShopRefList_ShouldQueryWithoutShopFilter(CommercialServiceTest.java:258)

testList_QueryResultEmpty_ShouldReturnEmptyResult  Time elapsed: 0.053 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultEmpty_ShouldReturnEmptyResult(CommercialServiceTest.java:163)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultEmpty_ShouldReturnEmptyResult(CommercialServiceTest.java:167)

testList_NormalQuery_ShouldReturnSuccessResult  Time elapsed: 0.005 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_NormalQuery_ShouldReturnSuccessResult(CommercialServiceTest.java:106)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_NormalQuery_ShouldReturnSuccessResult(CommercialServiceTest.java:112)

