package com.gl.framework.common;

import com.gl.framework.common.util.redis.RedisUtils;
import com.gl.framework.common.util.sign.Base64;
import com.gl.framework.common.util.uuid.IdUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.constant.RedisConstants;
import com.gl.framework.exception.CustomException;
import com.gl.framework.properties.ProjectProperties;
import com.gl.framework.web.response.ResultCode;
import com.google.code.kaptcha.Producer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 验证码控制器
 */
@RestController
public class CaptchaController {
    private static final Logger log = LoggerFactory.getLogger(CaptchaController.class);

    @Autowired
    private ProjectProperties projectProperties;

    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 生成验证码
     */
    @GetMapping("/captcha_image")
    public Map<String, Object> getCode(HttpServletResponse response) throws IOException {
        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = RedisConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 验证码类型
        String captchaType = projectProperties.getCaptchaType();

        // 生成验证码
        if ("math".equals(captchaType)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(captchaType)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisUtils.set(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new CustomException(e.getMessage(), ResultCode.FAIL.getCode());
        }

        Map<String, Object> map = new HashMap<String, Object>();

        map.put("uuid", uuid);
        map.put("img", Base64.encode(os.toByteArray()));

//		ResultEntity result = ResultEntity.success();
//		result.put("uuid", uuid);
//		result.put("img", Base64.encode(os.toByteArray()));
        return map;
    }

}
