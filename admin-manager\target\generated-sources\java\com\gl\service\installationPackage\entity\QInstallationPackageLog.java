package com.gl.service.installationPackage.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QInstallationPackageLog is a Querydsl query type for InstallationPackageLog
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QInstallationPackageLog extends EntityPathBase<InstallationPackageLog> {

    private static final long serialVersionUID = -1957488052L;

    public static final QInstallationPackageLog installationPackageLog = new QInstallationPackageLog("installationPackageLog");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> deviceId = createNumber("deviceId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> packageId = createNumber("packageId", Long.class);

    public final DateTimePath<java.util.Date> responseTime = createDateTime("responseTime", java.util.Date.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public QInstallationPackageLog(String variable) {
        super(InstallationPackageLog.class, forVariable(variable));
    }

    public QInstallationPackageLog(Path<? extends InstallationPackageLog> path) {
        super(path.getType(), path.getMetadata());
    }

    public QInstallationPackageLog(PathMetadata metadata) {
        super(InstallationPackageLog.class, metadata);
    }

}

