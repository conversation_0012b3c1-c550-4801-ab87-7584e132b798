package com.gl.framework.common.util.random;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 随机数工具类（继承自hutool中的RandomUtil，可做拓展）
 */
public class RandomUtils extends cn.hutool.core.util.RandomUtil {

	/**
	 * 生成指定格式的随机码（yyyyMMdd{firstLength位数字}HHmmss{secondLength位数字}）
	 *
	 * @param firstLength  随机数字位数
	 * @param secondLength 随机数字位数
	 * @return
	 */
	public static String getRandomNo(Integer firstLength, Integer secondLength) {
		StringBuilder stringBuilder = new StringBuilder();
		String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
		String time = new SimpleDateFormat("HHmmss").format(new Date());
		stringBuilder.append(date);
		stringBuilder.append(getNumRandom(firstLength));
		stringBuilder.append(time);
		stringBuilder.append(getNumRandom(secondLength));
		return stringBuilder.toString();
	}

	/**
	 * 获取指定位数的随机数（纯数字）
	 *
	 * @param length
	 * @return
	 */
	public static String getNumRandom(int length) {
		String source = "0123456789";
		Random r = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(source.charAt(r.nextInt(source.length())));
		}
		return sb.toString();
	}

	/**
	 * 获取指定位数的随机数（含数字/大小写字母）
	 *
	 * @param length
	 * @return
	 */
	public static String getCharAndNumRandom(int length) {
		String source = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz";
		Random r = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(source.charAt(r.nextInt(source.length())));
		}
		return sb.toString();
	}

}
