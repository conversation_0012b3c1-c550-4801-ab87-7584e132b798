package com.gl.service.music.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: duanjinze
 * @date: 2022/11/11 14:19
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BackGroundMusicVo {
    private Long id;

    /**
     * 类型id
     */
    private Long typeId;

    /**
     * 名称
     */
    private String name;

    /**
     * 文件url
     */
    private String musicUrl;

    /**
     * 0未删除 1已删除
     */
    private Integer delStatus;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 背景音乐时长(秒)
     */
    private Integer musicTime;

    //=====//
    /**
     * 背景音乐分类
     */
    private String musicTypeName;

    private String shopName;
    private Long shopId;

    public BackGroundMusicVo(Long id, String name, String musicUrl, Integer musicTime) {
        this.id = id;
        this.name = name;
        this.musicUrl = musicUrl;
        this.musicTime = musicTime;
    }
}
