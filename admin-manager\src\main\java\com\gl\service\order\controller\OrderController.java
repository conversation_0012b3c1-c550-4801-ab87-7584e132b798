package com.gl.service.order.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.order.service.OrderService;
import com.gl.service.order.vo.dto.OrderDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 订单管理
 */
@Controller
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('order:order:list')")
    public Result list(OrderDto dto) {
        return orderService.list(dto, 1);
    }

    @PostMapping("/export")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('order:order:export')")
    public void exportList(@RequestBody OrderDto dto, HttpServletResponse response) throws IOException {
        orderService.exportList(dto,response);
    }


}
