package com.gl.service.deviceOperationLog.repository;

import com.gl.service.deviceOperationLog.entity.DeviceOperationLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface DeviceOperationLogRepository extends JpaRepository<DeviceOperationLog, Long>, JpaSpecificationExecutor<DeviceOperationLog> {
    DeviceOperationLog findByTimeStamp(String timeStamp);
}
