package com.gl.service.opus.vo;

import java.util.List;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 *   @ Date：2024-03-21-16:49
 */
public class MergeVo {
    private List<String> audio;
    private String text;

    private String bgm;

    private Integer bugRate;

    private Integer beforeDelay = 0;

    private Integer afterDelay = 0;

    private Double bgmCenterVolum = 1.0;

    public List<String> getAudio() {
        return audio;
    }

    public void setAudio(List<String> audio) {
        this.audio = audio;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getBgm() {
        return bgm;
    }

    public void setBgm(String bgm) {
        this.bgm = bgm;
    }

    public Integer getBugRate() {
        return bugRate;
    }

    public void setBugRate(Integer bugRate) {
        this.bugRate = bugRate;
    }

    public Integer getBeforeDelay() {
        return beforeDelay;
    }

    public void setBeforeDelay(Integer beforeDelay) {
        this.beforeDelay = beforeDelay;
    }

    public Integer getAfterDelay() {
        return afterDelay;
    }

    public void setAfterDelay(Integer afterDelay) {
        this.afterDelay = afterDelay;
    }

    public Double getBgmCenterVolum() {
        return bgmCenterVolum;
    }

    public void setBgmCenterVolum(Double bgmCenterVolum) {
        this.bgmCenterVolum = bgmCenterVolum;
    }
}
