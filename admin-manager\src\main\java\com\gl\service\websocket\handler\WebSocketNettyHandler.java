package com.gl.service.websocket.handler;


import com.alibaba.fastjson.JSON;
import com.gl.service.websocket.component.AiSocketComponent;
import com.gl.service.websocket.entity.AiMessage;
import com.gl.service.websocket.entity.AiResult;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 服务端自定义处理入站消息
 *
 * <AUTHOR>
 */
@ChannelHandler.Sharable
@Component
@Slf4j
public class WebSocketNettyHandler extends SimpleChannelInboundHandler<TextWebSocketFrame> {
    @Autowired
    private AiSocketComponent aiSocketComponent;

    /**
     * 通道连接事件
     *
     * @param ctx
     * @throws Exception
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
    }

    /**
     * 通道消息事件
     *
     * @param channelHandlerContext
     * @param textWebSocketFrame
     * @throws Exception
     */
    @Override
    protected void channelRead0(ChannelHandlerContext channelHandlerContext, TextWebSocketFrame textWebSocketFrame) throws Exception {
        AiMessage message = JSON.parseObject(textWebSocketFrame.text(), AiMessage.class);
        if (message.getCategoryId() != -1) {
            aiSocketComponent.deepSeekAi(message, channelHandlerContext);
        } else {
            aiSocketComponent.ai(message, channelHandlerContext);
        }
    }


    /**
     * 通达关闭事件
     *
     * @param ctx
     * @throws Exception
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.info("ai合成异常{}", cause.getMessage());
        AiResult aiResult = new AiResult();
        aiResult.setMessage("服务器太火爆，请稍后尝试");
        ctx.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
    }
}
