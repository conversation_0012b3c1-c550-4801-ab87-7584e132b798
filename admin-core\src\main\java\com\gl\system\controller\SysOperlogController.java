package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.system.service.SysOperLogService;
import com.gl.system.vo.SysOperLogVo;
import com.gl.framework.web.domain.PageData;
import com.gl.framework.web.response.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作日志控制器
 */
@RestController
@RequestMapping("/system/operlog")
public class SysOperlogController {

    @Autowired
    private SysOperLogService operLogService;

    /**
     * 操作日志分页列表
     *
     * @param filter
     * @return
     */
    @GetMapping("/list")
    @PreAuthorize("@ps.hasPermi('system:operlog:list')")
    public PageData<SysOperLogVo> list(SysOperLogVo filter) {
        return operLogService.selectOperLogList(filter);
    }

    /**
     * 删除操作日志
     *
     * @param operIds
     * @return
     */
    @DeleteMapping("/{operIds}")
    @PreAuthorize("@ps.hasPermi('system:operlog:remove')")
    public Result remove(@PathVariable List<Long> operIds) {
        operLogService.deleteOperLogByIds(operIds);
        return Result.success();
    }

    /**
     * 清空操作日志
     *
     * @return
     */
    @DeleteMapping("/clean")
    @Log(title = "操作日志", businessType = BusinessType.CLEAN, businessTypeName = "清空操作日志")
    @PreAuthorize("@ps.hasPermi('system:operlog:remove')")
    public Result clean() {
        operLogService.cleanOperLog();
        return Result.success();
    }

}
