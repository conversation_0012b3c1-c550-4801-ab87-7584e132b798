package com.gl.framework.swagger;

import com.gl.framework.common.util.StringUtils;
import com.gl.framework.web.response.Result;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * swagger 用户测试方法
 */
@Api("用户信息管理")
@RestController
@RequestMapping("/swagger/test/user")
public class SwaggerTestController {

	private final static Map<Integer, UserEntity> users = new LinkedHashMap<>(2);
	{
		users.put(1, new UserEntity(1, "admin", "123456", "15888888888"));
		users.put(2, new UserEntity(2, "test", "123456", "15666666666"));
	}

	@GetMapping("/list")
	@ApiOperation("获取用户列表")
	public Result userList() {
		List<UserEntity> userList = new ArrayList<UserEntity>(users.values());
		return Result.success(userList);
	}

	@GetMapping("/{userId}")
	@ApiOperation("获取用户详细")
	@ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "path")
	public Result getUser(@PathVariable Integer userId) {
		if (!users.isEmpty() && users.containsKey(userId)) {
			return Result.success(users.get(userId));
		} else {
			return Result.fail("User Not Exist");
		}
	}

	@PostMapping("/save")
	@ApiOperation("新增用户")
	@ApiImplicitParam(name = "userEntity", value = "新增用户信息", dataType = "UserEntity")
	public Result save(UserEntity user) {
		if (StringUtils.isNull(user) || StringUtils.isNull(user.getUserId())) {
			return Result.fail("UserId Can Not Be Null");
		}
		return Result.success(users.put(user.getUserId(), user));
	}

	@PutMapping("/update")
	@ApiOperation("更新用户")
	@ApiImplicitParam(name = "userEntity", value = "新增用户信息", dataType = "UserEntity")
	public Result update(UserEntity user) {
		if (StringUtils.isNull(user) || StringUtils.isNull(user.getUserId())) {
			return Result.fail("UserId Can Not Be Null");
		}
		if (users.isEmpty() || !users.containsKey(user.getUserId())) {
			return Result.fail("User Not Exist");
		}
		users.remove(user.getUserId());
		return Result.success(users.put(user.getUserId(), user));
	}

	@DeleteMapping("/{userId}")
	@ApiOperation("删除用户信息")
	@ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "path")
	public Result delete(@PathVariable Integer userId) {
		if (!users.isEmpty() && users.containsKey(userId)) {
			users.remove(userId);
			return Result.success();
		} else {
			return Result.fail("User Not Exist");
		}
	}
}

@ApiModel("用户实体")
class UserEntity {
	@ApiModelProperty("用户ID")
	private Integer userId;

	@ApiModelProperty("用户名称")
	private String username;

	@ApiModelProperty("用户密码")
	private String password;

	@ApiModelProperty("用户手机")
	private String mobile;

	public UserEntity() {

	}

	public UserEntity(Integer userId, String username, String password, String mobile) {
		this.userId = userId;
		this.username = username;
		this.password = password;
		this.mobile = mobile;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
}
