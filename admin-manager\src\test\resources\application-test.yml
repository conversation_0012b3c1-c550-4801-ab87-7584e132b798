ali:
  oss:
    endpoint: dummy
    accessKeyId: dummy
    accessKeySecret: dummy
    bucket:
      name: dummy
      url: dummy
      path: dummy
    downFile:
      tempdir: /tmp
      packetdir: /tmp
huawei:
  sms:
    url: dummy
    appKey: dummy
    appSecret: dummy
    signature: dummy
    sender: dummy
mqtt:
  broker: tcp://dummy:1883
  username: dummy
  password: dummy
  qos: 1
  clientId: dummy-client
netty:
  path: /ai
  port: 9000
ty:
  accessKeyId: dummyKeyId
  accessKeySecret: dummyKeySecret
wechat:
  mp:
    appId: dummy
    secret: dummy
    redirectUri: http://localhost
  pay:
    appid: dummy
    mchid: dummy
    mchser: dummy
    privateKeyPath: /tmp/key.pem
    privateCertPath: /tmp/cert.pem
    apikey: dummy
    notifyPath: http://localhost/notify
project:
  name: test
  version: 1.0
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:
  # 禁用Redis自动配置，避免连接真实Redis服务器
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
  main:
    allow-bean-definition-overriding: true
token:
  header: Authorization
  secret: dummy
  expireTime: 1800
swagger:
  enabled: false
  pathMapping: /api
http_max_total: 100
http_default_max_perRoute: 20
http_validate_after_inactivity: 1000
http_connection_request_timeout: 5000
http_connection_timeout: 10000
http_socket_timeout: 20000
waitTime: 30000
idleConTime: 3
retryCount: 3
