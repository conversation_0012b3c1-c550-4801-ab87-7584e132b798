package com.gl.system.service;

import com.gl.framework.common.enums.MenuTypeEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.system.entity.*;
import com.gl.system.repository.SysMenuRepository;
import com.gl.system.repository.SysRoleMenuRepository;
import com.gl.system.vo.MetaVo;
import com.gl.system.vo.RouterVo;
import com.gl.system.vo.SysMenuVo;
import com.gl.system.vo.SysUserVo;
import com.gl.framework.web.domain.TreeSelect;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单服务实现类
 */
@Service
public class SysMenuService {
    public static final String PREMISSION_STRING = "perms[\"{0}\"]";

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SysMenuRepository sysMenuRepository;

    @Autowired
    private SysRoleMenuRepository sysRoleMenuRepository;

    @Autowired
    JPAQueryFactory queryFactory;

    /**
     * 根据用户查询系统菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenuVo> selectMenuList(Long userId) {
        return selectMenuList(null, userId);
    }

    /**
     * 查询系统菜单列表
     *
     * @param vo, userId 菜单信息
     * @return 菜单列表
     */
    public List<SysMenuVo> selectMenuList(SysMenuVo vo, Long userId) {
        List<SysMenuVo> menuList = null;
        // 管理员显示所有菜单信息
        if (SecurityUtils.isSuperAdmin(userId)) {
            menuList = this.findMenuList(vo, null);
        } else {
        	// 1招商 2小程序 3APP
            menuList = this.findMenuListByUserId(vo, userId, null);
        }
        return menuList;
    }
    
    /**
     * 角色-查询系统菜单列表
     *
     * @param vo, userId 菜单信息
     * @return 菜单列表
     */
    public List<SysMenuVo> roleSelectMenuList(SysMenuVo vo, Long userId) {
        List<SysMenuVo> menuList = null;
        // 管理员显示所有菜单信息
        if (SecurityUtils.isSuperAdmin(userId)) {
            menuList = this.findMenuList(vo, 1);
        } else {
        	// 1招商 2小程序 3APP
            menuList = this.findMenuListByUserId(vo, userId, 1);
        }
        return menuList;
    }

    /**
     * 查询系统菜单列表
     *
     * @param vo 菜单VO
     * @return 菜单列表
     */
    private List<SysMenuVo> findMenuList(SysMenuVo vo, Integer businessType) {
        return sysMenuRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.and();

            if (vo != null) {
                if (StringUtils.isNotBlank(vo.getName())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("menuName"), "%" + vo.getName() + "%"));
                }
                if (vo.getStatus() != null) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), vo.getStatus()));
               }
            }else {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), 1));
            }
            
            // 后管只查询菜单类型为1的菜单(1招商 2小程序 3APP)
            if(businessType != null){
            	predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("businessType"), businessType));
            }
            

            return predicate;
        }, Sort.by("serial")).stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * 查询用户系统菜单列表
     *
     * @param vo     菜单VO
     * @param userId 用户ID
     * @return 菜单列表
     */
    private List<SysMenuVo> findMenuListByUserId(SysMenuVo vo, Long userId, Integer businessType) {
        final String SQL = "SELECT DISTINCT m.id, m.parent_id, m.name, m.path, m.component, m.status, IFNULL(m.perms,'') AS perms, m.type, m.icon, m.serial, m.create_time \n" +
                "FROM sys_menu m \n" +
                "LEFT JOIN sys_role_menu rm ON m.id = rm.menu_id \n" +
                "LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id \n" +
                "LEFT JOIN sys_role r ON ur.role_id = r.id \n";
        final String ORDERBY_SQL = "ORDER BY m.parent_id, m.serial";

        StringBuilder where = new StringBuilder("WHERE ur.user_id = ?  \n");
        List<Object> args = new ArrayList<>();
        args.add(userId);
        
        if(businessType != null){
        	where.append("AND business_type = ? \n");
            args.add(businessType);
        }else{
        	where.append("AND business_type = ? \n");
        	args.add(businessType == null?1:businessType);
        }
        
        if (vo != null) {
            if (StringUtils.isNotEmpty(vo.getName())) {
                where.append("AND m.menu_name LIKE CONCAT('%', ?, '%') \n");
                args.add(vo.getName());
            }

            if (vo.getStatus() != null) {
                where.append("AND m.`status` = ? \n");
                args.add(vo.getStatus());
            }
        }else {
            where.append("AND m.`status` = 1 \n");

        }

        return jdbcTemplate.query(SQL + where + ORDERBY_SQL, new BeanPropertyRowMapper<>(SysMenuVo.class), args.toArray());
    }

    /**
     * 数据转换
     *
     * @param entity 菜单实体
     * @return 菜单VO
     */
    private SysMenuVo convert(SysMenu entity) {
        BeanCopier beanCopier = BeanCopier.create(SysMenu.class, SysMenuVo.class, false);
        SysMenuVo vo = new SysMenuVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> selectMenuPermsByUserId(Long userId) {
        List<SysMenuRepository.UserMenuPermsVo> perms = sysMenuRepository.findMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysMenuRepository.UserMenuPermsVo menu : perms) {
            if (menu == null) {
                continue;
            }

            String perm = menu.getPerms();
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户名称
     * @return 菜单列表
     */
    public List<SysMenuVo> selectMenuTreeByUserId(Long userId) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Boolean isSuperAdmin = SecurityUtils.isSuperAdmin(user.getId());

        JPAQuery<SysMenu> qr = null;
        BooleanBuilder builder = new BooleanBuilder();
        if (isSuperAdmin) {
            qr = queryFactory.select(QSysMenu.sysMenu).from(QSysMenu.sysMenu);
        } else {
            qr = queryFactory.selectDistinct(QSysMenu.sysMenu).from(QSysMenu.sysMenu)
                    .leftJoin(QSysRoleMenu.sysRoleMenu).on(QSysRoleMenu.sysRoleMenu.menuId.eq(QSysMenu.sysMenu.id))
                    .leftJoin(QSysRole.sysRole).on(QSysRole.sysRole.id.eq(QSysRoleMenu.sysRoleMenu.roleId))
                    .leftJoin(QSysUserRole.sysUserRole).on(QSysUserRole.sysUserRole.roleId.eq(QSysRole.sysRole.id))
                    .leftJoin(QSysUser.sysUser).on(QSysUser.sysUser.id.eq(QSysUserRole.sysUserRole.userId));

            builder.and(QSysUser.sysUser.id.longValue().eq(userId));
        }

        builder.and(QSysMenu.sysMenu.status.intValue().eq(1));
        qr.where(builder).orderBy(QSysMenu.sysMenu.serial.asc());

        List<SysMenuVo> menus = new ArrayList<>();
        QueryResults<SysMenu> qs = qr.fetchResults();
        if (qs != null) {
            menus = qr.fetchResults().getResults().stream().map(this::convert).collect(Collectors.toList());
        }

        return this.getChildPerms(menus, 0);
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    public List<Long> selectMenuListByRoleId(Long roleId) {
        return sysMenuRepository.selectMenuListByRoleId(roleId).stream().map(SysMenuRepository.SelectedMenuIdVo::getId).collect(Collectors.toList());
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVo> buildMenus(List<SysMenuVo> menus) {
        List<RouterVo> routers = new LinkedList<>();
        for (SysMenuVo menu : menus) {
            RouterVo router = new RouterVo();
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setMeta(new MetaVo(menu.getName(), menu.getIcon()));
            router.setHidden(menu.getHidden() == 1 ? true : false);
            List<SysMenuVo> cMenus = menu.getChildren();
            if (!cMenus.isEmpty() && menu.getType() != null && MenuTypeEnum.DIR.value() == menu.getType()) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMeunFrame(menu)) {
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getName(), menu.getIcon()));
                children.setHidden(menu.getHidden() == 1 ? true : false);
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    public List<SysMenuVo> buildMenuTree(List<SysMenuVo> menus) {
        List<SysMenuVo> returnList = new ArrayList<>();
        for (Iterator<SysMenuVo> iterator = menus.iterator(); iterator.hasNext(); ) {
            SysMenuVo t = (SysMenuVo) iterator.next();
            // 根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == 0) {
                recursionFn(menus, t);
                returnList.add(t);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenuVo> menus) {
        List<SysMenuVo> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    public SysMenuVo selectMenuById(Long menuId) {
        Optional<SysMenu> optional = sysMenuRepository.findById(menuId);
        if (!optional.isPresent()) {
            return null;
        }

        return convert(optional.get());
    }

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public boolean hasChildByMenuId(Long menuId) {
        int result = sysMenuRepository.countByParentId(menuId);
        return result > 0 ? true : false;
    }

    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public boolean checkMenuExistRole(Long menuId) {
        int result = sysRoleMenuRepository.countByMenuId(menuId);
        return result > 0 ? true : false;
    }

    /**
     * 新增菜单信息
     *
     * @param menu 菜单信息
     */
    public void saveMenu(SysMenuVo menu) {

        Integer count = countMenu(menu);
        if (count != null && count > 1) {
            throw new CustomException("存在重复属性");
        }

        BeanCopier beanCopier = BeanCopier.create(SysMenuVo.class, SysMenu.class, false);
        SysMenu entity = new SysMenu();
        beanCopier.copy(menu, entity, null);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        if (entity.getHidden() == null) {
            entity.setHidden(0);
        }
        // 后管默认为1
        if(entity.getBusinessType() == null){
        	entity.setBusinessType(1);
        }
        
        if(entity.getType() != null && (entity.getType() == 2 || entity.getType() == 3)){
        	// 小程序/APP菜单 后管默认不展示
        	entity.setHidden(0);
        }

        entity.setCreateUserId(user.getId());
        entity.setCreateTime(new Date());
        sysMenuRepository.save(entity);
    }

    private Integer countMenu(SysMenuVo menu) {

        if (menu.getType() == 1) {
            return sysMenuRepository.countByPath(menu.getPath());
        } else if (menu.getType() == 3) {
            return sysMenuRepository.countByPerms(menu.getPerms());
        } else if (menu.getType() == 2) {
            return sysMenuRepository.countByComponentOrPermsOrPath(menu.getComponent(), menu.getPerms(), menu.getPath());
        }
        return 0;
    }

    /**
     * 修改菜单信息
     *
     * @param menu 菜单信息
     */
    public void updateMenu(SysMenuVo menu) {
        Optional<SysMenu> optional = sysMenuRepository.findById(menu.getId());
        if (!optional.isPresent()) {
            throw new CustomException("菜单数据不存在");
        }

        Integer count = countMenu(menu);
        if (count > 1) {
            throw new CustomException("存在重复属性");
        }

        SysMenu entity = optional.get();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        BeanCopier beanCopier = BeanCopier.create(SysMenuVo.class, SysMenu.class, false);
        beanCopier.copy(menu, entity, null);
        entity.setUpdateUserId(user.getId());
        entity.setUpdateTime(new Date());
        if (entity.getHidden() == null) {
            entity.setHidden(0);
        }

        sysMenuRepository.save(entity);
    }

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     */
    public void deleteMenuById(Long menuId) {
        sysMenuRepository.deleteById(menuId);
        sysRoleMenuRepository.deleteByMenuId(menuId);
    }

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public String checkMenuNameUnique(SysMenuVo menu) {
        Long menuId = StringUtils.isNull(menu.getId()) ? -1L : menu.getId();
        SysMenu info = sysMenuRepository.findByNameAndParentId(menu.getName(), menu.getParentId());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != menuId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenuVo menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 一级目录（类型为目录）
        if (isMeunFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenuVo menu) {
        String routerPath = menu.getPath();
        // 一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && menu.getType() != null && MenuTypeEnum.DIR.value() == menu.getType()) {
            routerPath = "/" + menu.getPath();
        }
        // 一级目录（类型为菜单）
        else if (isMeunFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenuVo menu) {
        String component = Constants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMeunFrame(menu)) {
            component = menu.getComponent();
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMeunFrame(SysMenuVo menu) {
        return menu.getParentId().intValue() == 0 && menu.getType() != null && MenuTypeEnum.MENU.value() == menu.getType();
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return
     */
    public List<SysMenuVo> getChildPerms(List<SysMenuVo> list, int parentId) {
        List<SysMenuVo> returnList = new ArrayList<>();
        for (Iterator<SysMenuVo> iterator = list.iterator(); iterator.hasNext(); ) {
            SysMenuVo t = (SysMenuVo) iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId().longValue() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<SysMenuVo> list, SysMenuVo t) {
        // 得到子节点列表
        List<SysMenuVo> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenuVo tChild : childList) {
            if (hasChild(list, tChild)) {
                // 判断是否有子节点
                Iterator<SysMenuVo> it = childList.iterator();
                while (it.hasNext()) {
                    SysMenuVo n = (SysMenuVo) it.next();
                    recursionFn(list, n);
                }
            }
        }
    }

    /**
     * 得到子节点列表
     *
     * @param list
     * @param t
     * @return
     */
    private List<SysMenuVo> getChildList(List<SysMenuVo> list, SysMenuVo t) {
        List<SysMenuVo> tlist = new ArrayList<>();
        Iterator<SysMenuVo> it = list.iterator();
        while (it.hasNext()) {
            SysMenuVo n = (SysMenuVo) it.next();
            if (n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     *
     * @param list
     * @param t
     * @return
     */
    private boolean hasChild(List<SysMenuVo> list, SysMenuVo t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
