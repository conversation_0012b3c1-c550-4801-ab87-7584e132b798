package com.gl.service.paidPackages.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.paidPackages.service.PaidPackagesService;
import com.gl.service.paidPackages.vo.dto.PaidPackagesAddDto;
import com.gl.service.paidPackages.vo.dto.PaidPackagesDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/***
 * 流量管理
 */

@Controller
@RequestMapping("/paidPackages")
public class PaidPackagesController {

    @Autowired
    private PaidPackagesService paidPackagesService;

    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:paidPackages:list')")
    public Result list(PaidPackagesDto dto) {
        return paidPackagesService.list(dto, 1);
    }


    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:paidPackages:add')")
    public Result addOrUpdate(@RequestBody PaidPackagesAddDto vo){
        return paidPackagesService.addOrUpdate(vo);
    }

    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:paidPackages:delete')")
    public Result delete(@RequestBody PaidPackagesAddDto dto){
        return paidPackagesService.delete(dto);
    }

}
