package com.gl.service.broadcastPlan.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBroadcastPlan is a Querydsl query type for BroadcastPlan
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBroadcastPlan extends EntityPathBase<BroadcastPlan> {

    private static final long serialVersionUID = 1750220120L;

    public static final QBroadcastPlan broadcastPlan = new QBroadcastPlan("broadcastPlan");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> createUserId = createNumber("createUserId", Long.class);

    public final StringPath deviceIds = createString("deviceIds");

    public final StringPath endDate = createString("endDate");

    public final StringPath endTime = createString("endTime");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath intervalTime = createString("intervalTime");

    public final NumberPath<Long> shopId = createNumber("shopId", Long.class);

    public final StringPath startDate = createString("startDate");

    public final StringPath startTime = createString("startTime");

    public final StringPath type = createString("type");

    public QBroadcastPlan(String variable) {
        super(BroadcastPlan.class, forVariable(variable));
    }

    public QBroadcastPlan(Path<? extends BroadcastPlan> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBroadcastPlan(PathMetadata metadata) {
        super(BroadcastPlan.class, metadata);
    }

}

