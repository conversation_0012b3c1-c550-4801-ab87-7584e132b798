package com.gl.service.order.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QOrder is a Querydsl query type for Order
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QOrder extends EntityPathBase<Order> {

    private static final long serialVersionUID = 510873304L;

    public static final QOrder order = new QOrder("order1");

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final StringPath appid = createString("appid");

    public final NumberPath<Integer> clientType = createNumber("clientType", Integer.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final StringPath description = createString("description");

    public final NumberPath<Long> deviceId = createNumber("deviceId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath mchid = createString("mchid");

    public final StringPath openid = createString("openid");

    public final StringPath outTradeNo = createString("outTradeNo");

    public final NumberPath<Long> packageId = createNumber("packageId", Long.class);

    public final StringPath payerClientIp = createString("payerClientIp");

    public final DateTimePath<java.util.Date> payTime = createDateTime("payTime", java.util.Date.class);

    public final NumberPath<Integer> payType = createNumber("payType", Integer.class);

    public final NumberPath<Long> shopId = createNumber("shopId", Long.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final StringPath timeExpire = createString("timeExpire");

    public final StringPath tradeState = createString("tradeState");

    public final StringPath transactionId = createString("transactionId");

    public final StringPath unionid = createString("unionid");

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QOrder(String variable) {
        super(Order.class, forVariable(variable));
    }

    public QOrder(Path<? extends Order> path) {
        super(path.getType(), path.getMetadata());
    }

    public QOrder(PathMetadata metadata) {
        super(Order.class, metadata);
    }

}

