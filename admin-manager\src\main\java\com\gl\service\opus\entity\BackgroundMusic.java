package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 背景音乐
 * @author: duanjinze
 * @date: 2022/11/10 12:00
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_background_music")
public class BackgroundMusic {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 类型id
     */
    @Basic
    @Column(name = "type_id")
    private Long typeId;

    /**
     * 名称
     */
    @Basic
    @Column(name = "name")
    private String name;

    /**
     * 文件url
     */
    @Basic
    @Column(name = "music_url")
    private String musicUrl;

    /**
     * 0未删除 1已删除
     */
    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;

    /**
     * 创建人id
     */
    @Basic
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 背景音乐时长(秒)
     */
    @Basic
    @Column(name = "music_time")
    private Integer musicTime;

    @Basic
    @Column(name = "shop_id")
    private Long shopId;
}
