package com.gl.service.template.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 *
 * @author: duanjinze
 * @date: 2022/11/11 11:07
 * @version: 1.0
 */
@Data
public class ExcelTemplate {

    /**
     * 模板分类
     */
    @ExcelProperty(value = "模板分类",index = 0)
    @ColumnWidth(20)
    private String templateTypeName;

    /**
     * 模板标题
     */
    @ExcelProperty(value = "模板标题",index = 1)
    @ColumnWidth(20)
    private String title;

    /**
     * 模板内容
     */
    @ExcelProperty(value = "模板内容",index = 2)
    @ColumnWidth(40)
    private String content;

    /**
     * 创建人名称
     */
    @ExcelProperty(value = "创建人名称",index = 3)
    @ColumnWidth(20)
    private String createUserName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",index = 4)
    @ColumnWidth(20)
    private String createTime;
}
