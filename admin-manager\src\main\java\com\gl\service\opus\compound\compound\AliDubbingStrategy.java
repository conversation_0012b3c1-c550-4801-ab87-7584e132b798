package com.gl.service.opus.compound.compound;


import com.gl.commons.enums.PlatFormStatusEnum;
import com.gl.commons.enums.PlatFormTypeEnum;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.redis.RedisService;
import com.gl.service.opus.entity.PlatformConfig;
import com.gl.service.opus.repository.PlatformConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-09-13:49
 */
@Service
public class AliDubbingStrategy implements DubbingStrategy {
    @Autowired
    private PlatformConfigRepository platformConfigRepository;
    @Override
    public String process(SpeechSynthesizerDto dto, RedisService redisService) {
        List<PlatformConfig> platformConfigs = platformConfigRepository.findByPlatformTypeAndStatus(PlatFormTypeEnum.ALI_DUBBING.name(),
                PlatFormStatusEnum.ENABLED.ordinal());
        PlatformConfig platformConfig = platformConfigs.get(0);
        String appKey = platformConfig.getAppKey();
        String accessKeyId = platformConfig.getAccessKey();
        String accessKeySecret = platformConfig.getSecretKey();
        dto.setAppKey(appKey);
        dto.setAccessKeyId(accessKeyId);
        dto.setSecret(accessKeySecret);
        SpeechSynthesizerRequest request = new SpeechSynthesizerRequest(dto, redisService);
        String url = request.process(redisService);
        request.shutdown();
        return url;
    }
}
