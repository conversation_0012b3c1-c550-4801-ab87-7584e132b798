package com.gl.service.broadcastPlan.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBroadcastPlanVoiceWorkRef is a Querydsl query type for BroadcastPlanVoiceWorkRef
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBroadcastPlanVoiceWorkRef extends EntityPathBase<BroadcastPlanVoiceWorkRef> {

    private static final long serialVersionUID = 441285544L;

    public static final QBroadcastPlanVoiceWorkRef broadcastPlanVoiceWorkRef = new QBroadcastPlanVoiceWorkRef("broadcastPlanVoiceWorkRef");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> planId = createNumber("planId", Long.class);

    public final NumberPath<Integer> sort = createNumber("sort", Integer.class);

    public final NumberPath<Long> voiceWorkId = createNumber("voiceWorkId", Long.class);

    public QBroadcastPlanVoiceWorkRef(String variable) {
        super(BroadcastPlanVoiceWorkRef.class, forVariable(variable));
    }

    public QBroadcastPlanVoiceWorkRef(Path<? extends BroadcastPlanVoiceWorkRef> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBroadcastPlanVoiceWorkRef(PathMetadata metadata) {
        super(BroadcastPlanVoiceWorkRef.class, metadata);
    }

}

