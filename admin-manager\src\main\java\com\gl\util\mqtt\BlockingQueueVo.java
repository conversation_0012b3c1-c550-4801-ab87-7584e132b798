package com.gl.util.mqtt;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date: 2025/3/26
 * @description:
 */
public class BlockingQueueVo {

    // 用于存储消息的队列
    public static final BlockingQueue<MqttResultVo> messageQueue = new LinkedBlockingQueue<>();


    // 向队列中添加消息
    public static boolean addMessage(MqttResultVo message) {
       return BlockingQueueVo.messageQueue.offer(message);
    }


    // 从队列中获取消息（阻塞方式）
    public static MqttResultVo getMessage(int i, TimeUnit timeUnit) throws InterruptedException {
        // 阻塞直到有消息到达，或者超时（例如 1 秒）
        return messageQueue.poll(i, timeUnit);
    }

}
