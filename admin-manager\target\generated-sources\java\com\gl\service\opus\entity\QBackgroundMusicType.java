package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBackgroundMusicType is a Querydsl query type for BackgroundMusicType
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBackgroundMusicType extends EntityPathBase<BackgroundMusicType> {

    private static final long serialVersionUID = -462423776L;

    public static final QBackgroundMusicType backgroundMusicType = new QBackgroundMusicType("backgroundMusicType");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath name = createString("name");

    public QBackgroundMusicType(String variable) {
        super(BackgroundMusicType.class, forVariable(variable));
    }

    public QBackgroundMusicType(Path<? extends BackgroundMusicType> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBackgroundMusicType(PathMetadata metadata) {
        super(BackgroundMusicType.class, metadata);
    }

}

