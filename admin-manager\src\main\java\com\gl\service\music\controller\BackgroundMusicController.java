package com.gl.service.music.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.music.service.BackgroundMusicService;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.music.vo.dto.BackgroundMusicDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 背景音乐管理
 *
 * @author: duanjinze
 * @date: 2022/11/11 14:37
 * @version: 1.0
 */
@Controller
@RequestMapping("/music")
public class BackgroundMusicController {
    @Autowired
    private BackgroundMusicService backgroundMusicService;

    /**
     * 背景音乐列表
     *
     * @param dto
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('music:music:list')")
    public Result list(BackgroundMusicDto dto) {
        return backgroundMusicService.list(dto);
    }

    /**
     * 删除
     *
     * @param dto
     * @return
     */
    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('music:music:delete')")
    public Result delete(@RequestBody BackgroundMusicDto dto) {
        return backgroundMusicService.delete(dto);
    }

    /**
     * 新增
     *
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('music:music:add')")
    public Result add(@RequestBody BackGroundMusicVo vo) {
        return backgroundMusicService.add(vo);
    }

    /**
     * 背景音乐分类下拉框
     *
     * @return
     */
    @GetMapping("/type")
    @ResponseBody
    public Result findBackgroundMusicType() {
        return backgroundMusicService.findBackgroundMusicType();
    }
}
