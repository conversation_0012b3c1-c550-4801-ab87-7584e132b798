package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QDubAnchor is a Querydsl query type for DubAnchor
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QDubAnchor extends EntityPathBase<DubAnchor> {

    private static final long serialVersionUID = -1370808715L;

    public static final QDubAnchor dubAnchor = new QDubAnchor("dubAnchor");

    public final StringPath archorTag = createString("archorTag");

    public final StringPath emotion = createString("emotion");

    public final NumberPath<Integer> enableSubtitle = createNumber("enableSubtitle", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isEmotion = createNumber("isEmotion", Integer.class);

    public final StringPath name = createString("name");

    public final NumberPath<Integer> orderIndex = createNumber("orderIndex", Integer.class);

    public final NumberPath<Integer> rhoticAccent = createNumber("rhoticAccent", Integer.class);

    public final NumberPath<Integer> sampleRate = createNumber("sampleRate", Integer.class);

    public final NumberPath<Integer> supportVoiceType = createNumber("supportVoiceType", Integer.class);

    public final StringPath type = createString("type");

    public final StringPath typeName = createString("typeName");

    public final StringPath url = createString("url");

    public final StringPath usageScenario = createString("usageScenario");

    public final StringPath voiceName = createString("voiceName");

    public final StringPath voiceUrl = createString("voiceUrl");

    public QDubAnchor(String variable) {
        super(DubAnchor.class, forVariable(variable));
    }

    public QDubAnchor(Path<? extends DubAnchor> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDubAnchor(PathMetadata metadata) {
        super(DubAnchor.class, metadata);
    }

}

