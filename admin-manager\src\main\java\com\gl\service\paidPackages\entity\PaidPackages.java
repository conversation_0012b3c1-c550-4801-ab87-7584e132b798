package com.gl.service.paidPackages.entity;

import lombok.Data;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


@Data
@Entity
@Table(name = "dub_paid_packages")
public class PaidPackages {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @Basic
    @Column(name = "name")
    private String name;

    @Basic
    @Column(name = "data_num")
    private Integer dataNum;

    @Basic
    @Column(name = "effect_day")
    private Integer effectDay;

    @Basic
    @Column(name = "selling_price")
    private BigDecimal sellingPrice;

    @Basic
    @Column(name = "create_time")
    private Date createTime;

    @Basic
    @Column(name = "create_user_id")
    private Long createUserId;

}
