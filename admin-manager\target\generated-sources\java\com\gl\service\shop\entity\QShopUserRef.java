package com.gl.service.shop.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QShopUserRef is a Querydsl query type for ShopUserRef
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QShopUserRef extends EntityPathBase<ShopUserRef> {

    private static final long serialVersionUID = 373270410L;

    public static final QShopUserRef shopUserRef = new QShopUserRef("shopUserRef");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> role = createNumber("role", Integer.class);

    public final NumberPath<Long> shopId = createNumber("shopId", Long.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QShopUserRef(String variable) {
        super(ShopUserRef.class, forVariable(variable));
    }

    public QShopUserRef(Path<? extends ShopUserRef> path) {
        super(path.getType(), path.getMetadata());
    }

    public QShopUserRef(PathMetadata metadata) {
        super(ShopUserRef.class, metadata);
    }

}

