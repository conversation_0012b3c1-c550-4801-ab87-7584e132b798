package com.gl.service.shop.repository;


import com.gl.service.opus.entity.UserTextTemplate;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface UserTextTemplateRepository extends PagingAndSortingRepository<UserTextTemplate,Long>, JpaSpecificationExecutor<UserTextTemplate> {

	List<UserTextTemplate> findByUserId(Long userId);

	void deleteByIdAndUserId(Long id, Long userId);
}
