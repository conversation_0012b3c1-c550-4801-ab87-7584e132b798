package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.constant.Constants;
import com.gl.framework.interceptor.annotation.RepeatSubmit;
import com.gl.system.service.SysConfigService;
import com.gl.system.vo.SysConfigVo;
import com.gl.framework.web.domain.PageData;
import com.gl.framework.web.response.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数配置控制器
 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController {

    @Autowired
    private SysConfigService configService;

    /**
     * 参数配置分页列表
     *
     * @param filter
     * @return
     */
    @GetMapping("/list")
    @PreAuthorize("@ps.hasPermi('system:config:list')")
    public PageData<SysConfigVo> list(SysConfigVo filter) {
        return configService.selectConfigList(filter);
    }

    /**
     * 获取参数详细信息
     *
     * @param configId
     * @return
     */
    @GetMapping("/{configId}")
    @PreAuthorize("@ps.hasPermi('system:config:query')")
    public SysConfigVo getInfo(@PathVariable Long configId) {
        return configService.selectConfigById(configId);
    }

    /**
     * 根据参数键名查询参数值
     *
     * @param key
     * @return
     */
    @GetMapping("/key/{key}")
    public String getConfigKey(@PathVariable String key) {
        return configService.selectConfigByKey(key);
    }

    /**
     * 新增参数配置
     *
     * @param vo
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @PreAuthorize("@ps.hasPermi('system:config:add')")
    @Log(title = "参数管理", businessType = BusinessType.INSERT, businessTypeName = "新增")
    public Result add(@Validated @RequestBody SysConfigVo vo) {
        if (Constants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(vo))) {
            return Result.fail("新增参数'" + vo.getName() + "'失败，参数键名已存在");
        }

        configService.saveConfig(vo);
        return Result.success();
    }

    /**
     * 修改参数配置
     *
     * @param vo
     * @return
     */
    @PutMapping
    @PreAuthorize("@ps.hasPermi('system:config:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE, businessTypeName = "修改")
    public Result edit(@Validated @RequestBody SysConfigVo vo) {
        if (Constants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(vo))) {
            return Result.fail("修改参数'" + vo.getName() + "'失败，参数键名已存在");
        }

        configService.updateConfig(vo);
        return Result.success();
    }

    /**
     * 删除参数配置
     *
     * @param configIds
     * @return
     */
    @DeleteMapping("/{configIds}")
    @PreAuthorize("@ps.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.DELETE, businessTypeName = "删除")
    public Result delete(@PathVariable List<Long> configIds) {
        configService.deleteConfigByIds(configIds);
        return Result.success();
    }

    /**
     * 清空缓存
     *
     * @return
     */
    @DeleteMapping("/clearCache")
    @PreAuthorize("@ps.hasPermi('system:config:clean')")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN, businessTypeName = "清空缓存")
    public Result clearCache() {
        configService.clearCache();
        return Result.success();
    }

    /**
     * 同步缓存
     *
     * @return
     */
    @PostMapping("/copyCache")
    @PreAuthorize("@ps.hasPermi('system:config:copy')")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN, businessTypeName = "同步缓存")
    public Result copyCache() {
        configService.copyCache();
        return Result.success();
    }
}
