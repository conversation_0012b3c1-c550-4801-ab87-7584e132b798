package com.gl.service.order.repository;

import com.gl.service.order.entity.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface OrderItemRepository extends JpaRepository<OrderItem,Long>, JpaSpecificationExecutor<OrderItem> {



    @Transactional
    @Modifying
    @Query(value = "UPDATE dub_order_pay_record set status = ?4, transaction_id = ?2 , amount = 1 ,  end_time = ?3 \n" +
            "where order_id = ?1  order by id desc limit 1  ",nativeQuery = true)
    Integer paySuccess(Long orderId,String transactionId,String successTime,Integer status);

    List<OrderItem> findByOrderId(Long id);
}