package com.gl.service.opus.compound.compound;

import com.gl.commons.enums.PlatFormStatusEnum;
import com.gl.commons.enums.PlatFormTypeEnum;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.redis.RedisService;
import com.gl.service.opus.entity.PlatformConfig;
import com.gl.service.opus.repository.PlatformConfigRepository;
import com.gl.service.opus.utils.FfmpegUtil;
import com.gl.service.opus.utils.FileUtil;
import com.gl.service.opus.utils.HttpUtil;
import com.gl.service.opus.utils.LineInsertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-04-29-15:45
 */
@Service
public class YdDubbingStrategy implements DubbingStrategy {
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RedisService redisService;

    private static String url = "https://openapi.youdao.com/ttsapi";

    @Autowired
    private PlatformConfigRepository platformConfigRepository;

    @Autowired
    private ThreadPoolTaskExecutor longYdVoiceTaskExecutor;

    @Override
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {
        List<PlatformConfig> platformConfigs = platformConfigRepository.findByPlatformTypeAndStatus(PlatFormTypeEnum.YD_DUBBING.name(),
                PlatFormStatusEnum.ENABLED.ordinal());
        PlatformConfig platformConfig = platformConfigs.get(0);
        String appId = platformConfig.getAppId();
        String secretKey = platformConfig.getSecretKey();
        Double volume = LineInsertUtils.mapRange(dto.getVolume(), 0,
                100, 0.5, 5.0);
        Double speechRate = LineInsertUtils.mapRange(dto.getSpeechRate() - 200, -500,
                500, 0.5, 2.0);
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        volume = Double.parseDouble(decimalFormat.format(volume));
        speechRate = Double.parseDouble(decimalFormat.format(speechRate));
        // 设定每次循环需要获取的字符数
        int chunkSize = 500;
        // 开始位置
        int startIndex = 0;
        String originalString = dto.getText();
        int i = 0;
        List<CompletableFuture<File>> futures = new ArrayList();
        while (startIndex < originalString.length()) {
            File tempFile = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
            // 计算本次循环需要截取的结束位置
            int endIndex = Math.min(startIndex + chunkSize, originalString.length());
            // 使用substring获取子串
            String substring = originalString.substring(startIndex, endIndex);
            // 更新开始位置为本次循环的结束位置加1
            startIndex = endIndex;
            Double finalSpeechRate = speechRate;
            Double finalVolume = volume;
            CompletableFuture<File> future = CompletableFuture.supplyAsync(
                    () -> {
                        try {
                            Map<String, String[]> formData = new HashMap<>();
                            String input = getInput(substring);
                            String salt = UUID.randomUUID().toString();
                            String curtime = String.valueOf(System.currentTimeMillis() / 1000);
                            formData.put("q", new String[]{substring});
                            formData.put("curtime", new String[]{curtime});
                            formData.put("speed", new String[]{String.valueOf(finalSpeechRate)});
                            formData.put("volume", new String[]{String.valueOf(finalVolume)});
                            formData.put("appKey", new String[]{appId});
                            formData.put("voiceName", new String[]{dto.getVoice()});
                            formData.put("salt", new String[]{salt});
                            formData.put("format", new String[]{"mp3"});
                            formData.put("signType", new String[]{"v3"});
                            String strSrc = appId + input + salt + curtime + secretKey;
                            formData.put("sign", new String[]{calculateSHA256(strSrc)});
                            byte[] result = HttpUtil.doPost("https://openapi.youdao.com/ttsapi", null, formData, "audio");
                            FileUtil.saveFile(tempFile.getPath(), result, false);
                            return tempFile;
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    }, longYdVoiceTaskExecutor);
            futures.add(future);
            i++;
        }
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allFutures.join(); // 等待所有任务完成
        File templateFile = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
        futures.forEach(future -> {
            try {
                File file = future.get();
                FileUtil.combine(templateFile, file);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        File file = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
        if (!StringUtils.isEmpty(dto.getBgm())) {
            String bgmFileName = dto.getUrl() + FileUtil.getFileNewName(".wav");
            File bgmFile = FileUtil.taiseng(dto.getBgm(), bgmFileName, dto.getBugRate());
            File outFile = new File(dto.getUrl() + FileUtil.getFileNewName(".wav"));
            FfmpegUtil.mixBgm(templateFile, bgmFile, outFile, dto.getBeforeDelay(), dto.getAfterDelay(), dto.getBgmCenterVolum());
            FileUtil.coverToMp3(outFile, file);
            return file.getAbsolutePath();
        } else {
            FileUtil.coverToMp3(templateFile, file);
            return file.getAbsolutePath();
        }
    }

    public String calculateSHA256(String input) {

        byte[] bt = input.getBytes();
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("SHA-256");
            md.update(bt);
            byte[] bts = md.digest();
            StringBuilder des = new StringBuilder();
            for (byte b : bts) {
                String tmp = (Integer.toHexString(b & 0xFF));
                if (tmp.length() == 1) {
                    des.append("0");
                }
                des.append(tmp);
            }
            return des.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    private String getInput(String input) {
        if (input == null) {
            return null;
        }
        String result;
        int len = input.length();
        if (len <= 20) {
            result = input;
        } else {
            String startStr = input.substring(0, 10);
            String endStr = input.substring(len - 10, len);
            result = startStr + len + endStr;
        }
        return result;
    }

}
