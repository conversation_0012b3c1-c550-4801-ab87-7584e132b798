package com.gl.system.repository;

import com.gl.system.entity.SysArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface SysAreaRepository extends JpaRepository<SysArea, Long>, JpaSpecificationExecutor<SysArea> {

    Optional<SysArea> findById(Long id);

    int countByParentId(Long parentId);

    List<SysArea> findAllByParentId(Long id);
}
