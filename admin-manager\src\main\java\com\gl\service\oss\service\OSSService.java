package com.gl.service.oss.service;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.lang.UUID;
import com.aliyun.oss.internal.Mimetypes;
import com.aliyun.oss.model.*;
import com.gl.util.DownloadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.OSSClient;
import com.gl.framework.config.oss.OssConfig;
import com.gl.framework.web.response.Result;


/**
 * 阿里云OSS对象存储
 *
 * <AUTHOR>
 * @createDate: 2021年3月23日 下午2:49:08
 * @version: 1.0
 */
@Slf4j
@Service
public class OSSService {
    private final Logger LOG = LoggerFactory.getLogger(OSSService.class);
    @Autowired
    private OSSClient ossClient;
    @Autowired
    private OssConfig ossConfig;

    /**
     * 上传文件
     *
     * @param bucketName oss桶名称
     * @param mfile      文件
     * @param type       文件业务类型
     * @return 文件key
     * <AUTHOR>
     * @date 2021年3月24日
     */
    public String putFileToName(String bucketName, MultipartFile mfile, String type) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }

        File temp = new File("temp_" + getNewName(Objects.requireNonNull(mfile.getOriginalFilename())));
        StringBuilder fileKey = new StringBuilder();
        fileKey.append(ossConfig.getFristFilePath()).append(type);
        fileKey.append("/").append(mfile.getOriginalFilename());
        try (FileOutputStream fos = new FileOutputStream(temp);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            bos.write(mfile.getBytes());
            bos.flush();

            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), temp);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (temp.exists()) {
                temp.delete();
            }
        }
        return null;
    }

    /**
     * 不改变文件名称上传
     *
     * @param bucketName
     * @param file
     * @param type
     * @return
     */
    public String putFileOld(String bucketName, File file, String type) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(ossConfig.getFristFilePath()).append(type);
            fileKey.append("/").append(file.getName());
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }

    public String putFile2(String bucketName, MultipartFile mfile, String type) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }

        File temp = new File("temp_" + getNewName(Objects.requireNonNull(mfile.getOriginalFilename())));
        StringBuilder fileKey = new StringBuilder();
        fileKey.append(ossConfig.getFristFilePath()).append(type);
        fileKey.append("/").append(getNewName(mfile.getOriginalFilename()));
        try (FileOutputStream fos = new FileOutputStream(temp);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            bos.write(mfile.getBytes());
            bos.flush();

            InputStream inputStream = new URL("").openStream();
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), inputStream);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (temp.exists()) {
                temp.delete();
            }
        }
        return null;
    }

    /**
     * 上传文件
     *
     * @param bucketName oss桶名称
     * @param file       文件
     * @param type       文件业务类型
     * @return 文件key
     * <AUTHOR>
     * @date 2021年3月24日
     */
    public String putFileToName(String bucketName, File file, String type) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(ossConfig.getFristFilePath()).append(type);
            fileKey.append("/").append(file.getName());
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }

    public String putFileNewName(String bucketName, File file, String type) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(ossConfig.getFristFilePath()).append(type);
            fileKey.append("/").append(getNewName(file.getName()));
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 上传文件
     *
     * @param bucketName oss桶名称
     * @param file       文件
     * @param type       文件业务类型
     * @return 文件key
     * <AUTHOR>
     * @date 2021年3月24日
     */
    public String putFileToName(String bucketName, File file, String type, String fileName) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(ossConfig.getFristFilePath()).append(type);
            fileKey.append("/").append(getName(file.getName(), fileName));
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 返回文件访问全路径
     *
     * @param fileKey
     * @return
     * <AUTHOR>
     * @date 2021年3月24日
     */
    public String getOssFilePath(String fileKey) {
        return ossConfig.getBucketUrl() + fileKey;
    }

    private String getNewName(String filename) {
        int indx = filename.lastIndexOf(".");
        String suffix = filename.substring(indx);
        return UUID.randomUUID() + suffix;
    }

    private String getName(String filename, String newFileName) {
        int indx = filename.lastIndexOf(".");
        String suffix = filename.substring(indx);
        return newFileName + suffix;
    }

    private String getDate() {
        return DateFormatUtils.format(new Date(), "yyyyMMdd");
    }

    /**
     * 递归获取目录所有文件
     *
     * @param dir
     * @param list
     * @return
     * <AUTHOR>
     * @date 2021年11月19日
     */
    public List<File> getAllFile(File dir, List<File> list) {
        File[] files = dir.listFiles();
        for (File obj : Objects.requireNonNull(files)) {
            if (obj.isDirectory()) {
                getAllFile(obj, list);
            }
            list.add(obj);
        }

        return list;
    }

    /**
     * 语音包下载文件
     *
     * @param bucketName 桶名称，可为空
     * @param path       图片路径(不带HTTP与域名)
     * @return
     * <AUTHOR>
     * @date 2021年10月27日
     */
    public File getObjectFilePacket(String bucketName, String path, Long time, String name) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }
        if (StringUtils.isEmpty(path)) {
            return null;
        }

        int indx = path.lastIndexOf(".");
        String suffix = path.substring(indx);

        File file = new File(ossConfig.getDownFilePacketdir() + time + "/temp_" + name + System.currentTimeMillis() + suffix);
        try {
            // 下载
            ossClient.getObject(new GetObjectRequest(bucketName, path), file);

            return file;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 下载文件
     *
     * @param bucketName 桶名称，可为空
     * @param path       图片路径(不带HTTP与域名)
     * @return
     * <AUTHOR>
     * @date 2021年10月27日
     */
    public File getObjectFile(String bucketName, String path) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }

        File file = new File(ossConfig.getDownFileTempdir() + "temp_" + getNewName(path));
        try {
            // 下载
            ossClient.getObject(new GetObjectRequest(bucketName, path), file);

            return file;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 下载, 响应页面文件流
     *
     * @param file
     * @param response
     * <AUTHOR>
     * @date 2021年5月31日
     */
    public void excelDownload(File file, HttpServletResponse response) {
        try {
            // 读到流中
            InputStream in = Files.newInputStream(file.toPath());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 导出文件下载
            DownloadUtil downloadUtil = new DownloadUtil();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int len;
            try {
                while ((len = in.read(b)) > 0) {
                    bos.write(b, 0, len);
                }

                in.close();
            } catch (IOException e) {
                LOG.error("Download the document is failed,message:{}", e.getMessage());
            }

            downloadUtil.download(bos, response, file.getName());
        } catch (IOException e) {
            LOG.error(e.getMessage(), e);
        } finally {
            // 删除原文件
            if (file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * 单文件删除
     *
     * @param bucketName 对象存储桶名称
     * <AUTHOR>
     * @date 2020年12月30日
     */
    public Result deleteObject(String bucketName, String fileKey) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }
        try {
            ossClient.deleteObject(bucketName, fileKey);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return Result.fail(e.getMessage());
        }

        return Result.success();
    }

    /**
     * 批量删除桶内所有对象
     *
     * @param bucketName
     * @param keys       删除文件。key等同于ObjectName，表示删除OSS文件时需要指定包含文件后缀在内的完整路径，例如abc/efg/
     *                   123.jpg
     * @return
     * <AUTHOR>
     * @date 2021年3月23日
     */
    public List<String> batchDeleteObject(String bucketName, List<String> keys) {
        DeleteObjectsResult deleteObjectsResult = ossClient
                .deleteObjects(new DeleteObjectsRequest(bucketName).withKeys(keys));
        return deleteObjectsResult.getDeletedObjects();
    }

    public Boolean doesObjectExist(String remoteFileName) {
        String bucketName = ossConfig.getOssBucketName();
        boolean result = true;
        try {
            result = ossClient.doesObjectExist(bucketName, remoteFileName);
        } catch (Exception e) {
            log.warn("文件不存在, SSO删除文件发生异常");
        } finally {
            // 关闭OSSClient。
            ossClient.shutdown();
        }
        return result;
    }


    public String dijia(String bucketName, File file, String type, String fileName) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }
        StringBuilder fileKey = new StringBuilder();
        fileKey.append(type);
        fileKey.append("/").append(getName(file.getName(), fileName));
        String dijia = fileKey.toString();
        try {
            InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucketName, dijia);
            ObjectMetadata metadata = new ObjectMetadata();
            if (metadata.getContentType() == null) {
                metadata.setContentType(Mimetypes.getInstance().getMimetype(file, dijia));
            }
            InitiateMultipartUploadResult upresult = ossClient.initiateMultipartUpload(request);
            String uploadId = upresult.getUploadId();
            List<PartETag> partETags = new ArrayList<PartETag>();
            final long partSize = 1 * 1024 * 1024;   //1 MB。
            long fileLength = file.length();
            int partCount = (int) (fileLength / partSize);
            if (fileLength % partSize != 0) {
                partCount++;
            }
            for (int i = 0; i < partCount; i++) {
                long startPos = i * partSize;
                long curPartSize = (i + 1 == partCount) ? (fileLength - startPos) : partSize;
                UploadPartRequest uploadPartRequest = new UploadPartRequest();
                uploadPartRequest.setBucketName(bucketName);
                uploadPartRequest.setKey(dijia);
                uploadPartRequest.setUploadId(uploadId);
                InputStream instream = null;
                instream = new FileInputStream(file);
                instream.skip(startPos);
                uploadPartRequest.setInputStream(instream);
                uploadPartRequest.setPartSize(curPartSize);
                uploadPartRequest.setPartNumber(i + 1);
                UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
                partETags.add(uploadPartResult.getPartETag());
                LOG.info("dijia上传第{}次，上传大小{}", i + 1, curPartSize);
            }
            CompleteMultipartUploadRequest completeMultipartUploadRequest =
                    new CompleteMultipartUploadRequest(bucketName, dijia, uploadId, partETags);
            CompleteMultipartUploadResult completeMultipartUploadResult = ossClient.completeMultipartUpload(completeMultipartUploadRequest);
            LOG.info("dijia上传完成{}", completeMultipartUploadResult.getETag());
            return dijia;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
