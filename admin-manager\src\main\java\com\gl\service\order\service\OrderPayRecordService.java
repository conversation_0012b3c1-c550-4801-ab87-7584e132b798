package com.gl.service.order.service;

import com.gl.framework.web.response.Result;
import com.gl.service.order.entity.OrderPayRecord;
import com.gl.service.order.repository.OrderPayRecordRepository;
import com.gl.service.order.vo.dto.OrderPayRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class OrderPayRecordService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private OrderPayRecordRepository orderPayRecordRepository;


    public Result getByOrderId(OrderPayRecordDto dto){
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (dto.getOrderId() != null){
                where.append(" and r.order_id = ? ");
                args.add(dto.getOrderId());
            }
        }

        String sql = "select * from dub_order_pay_record r where 1=1";
        Result result = Result.success();
        Long count = jdbcTemplate.queryForObject("select count(1) from ("+ sql + where+") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result",null);
            return result;
        }

        where.append("order by r.id ASC ");
        List<OrderPayRecord> deviceVos = jdbcTemplate.query(  sql + where, new BeanPropertyRowMapper<>(OrderPayRecord.class), args.toArray());
        result.addData("total", count);
        result.addData("result",deviceVos);
        return result;
    }



    public void createOrderPayRecord(OrderPayRecord entity) {
        orderPayRecordRepository.save(entity);
    }


    public void paySuccess(Long orderId,String transactionId,String successTime) {
        orderPayRecordRepository.paySuccess(orderId,transactionId,successTime);
    }


}
