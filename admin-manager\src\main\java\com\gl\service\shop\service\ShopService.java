package com.gl.service.shop.service;

import cn.hutool.core.collection.CollUtil;
import com.gl.aspectj.annotation.ShopPermission;
import com.gl.commons.enums.PlatFormTypeEnum;
import com.gl.commons.enums.StoreTypeEnum;
import com.gl.commons.enums.StoreUserTypeEnum;
import com.gl.commons.enums.UserTypeEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.opus.entity.Device;
import com.gl.service.shop.entity.Shop;
import com.gl.service.shop.entity.ShopUserRef;
import com.gl.service.shop.repository.ShopRepository;
import com.gl.service.shop.repository.ShopUserRefRepository;
import com.gl.service.shop.vo.*;
import com.gl.service.user.vo.WechatUserVo;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import com.gl.wechat.entity.WechatUser;
import com.gl.wechat.repository.WechatUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class ShopService {

    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private ShopUserRefRepository shopUserRefRepository;
    @Resource
    private WechatUserRepository wechatUserRepository;
    @Resource
    private DeviceRepository deviceRepository;
    @Resource
    private GetShopRefUtil shopRefUtil;

    public Result list(ShopQueryParamVo queryParamVo) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        List<String> query = new ArrayList<>();
        List<Object> args = new ArrayList<>();
        if (StringUtils.isNotBlank(queryParamVo.getShopName())) {
            query.add(" shop.shop_name like ? ");
            args.add("%" + queryParamVo.getShopName() + "%");
        }
        if (queryParamVo.getStatus() != null) {
            query.add(" shop.status = ? ");
            args.add(queryParamVo.getStatus());
        }
        if (queryParamVo.getType() != null) {
            query.add(" shop.type = ? ");
            args.add(queryParamVo.getType());
        }
        StringBuilder where = new StringBuilder();
        if (CollUtil.isNotEmpty(query)) {
            where.append(" WHERE ");
            where.append(String.join(" AND ", query));
        }
        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            if (StringUtils.isEmpty(where)) {
                where.append(" WHERE ");
            } else {
                where.append(" AND ");
            }
            where.append(String.format(" shop.id in %s", SqlUtils.foreach("(", ")", ",", shopRef)));
        }

        String sql = "SELECT " + " shop.id, " + " shop.create_time, " + " shop.shop_name, " + " shop.type, "
                + " shop.parent_id, " + " parentShop.shop_name as parentName, "
                + " GROUP_CONCAT(itemShop.shop_name, ',') as itemNames, " + " shop.status, " + " ref2.user_id, "
                + " u.nickname, " + " COUNT(DISTINCT ref.user_id ) AS userCount, "
                + " COUNT(DISTINCT device.id ) AS deviceCount  " + "FROM dub_shop shop "
                + "LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role = 1 "
                + "LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id "
                + "LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id "
                + "LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id "
                + "LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id "
                + "LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0";

        where.append(" GROUP BY shop.id, ref2.user_id");
        Long count = jdbcTemplate.queryForObject(String.format("SELECT COUNT(*) FROM (%s%s) t", sql, where), Long.class,
                args.toArray());
        if (count == null || 0 == count.intValue()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        where.append(" ORDER BY shop.create_time DESC ");
        where.append(" LIMIT ? OFFSET ? ");
        args.add(queryParamVo.getPageSize());
        args.add(queryParamVo.getPageNumber() * queryParamVo.getPageSize());
        List<ShopPageResVo> pageResVos = jdbcTemplate.query(String.format("%s%s", sql, where),
                new BeanPropertyRowMapper<>(ShopPageResVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", pageResVos);

        Shop userShop = shopRefUtil.getShop();
        if (userShop != null && userShop.getType().equals(StoreUserTypeEnum.ADMIN.getCode())) {
            result.addData("isSup", true);
        } else {
            result.addData("isSup", false);
        }
        result.addData("wxId", SecurityUtils.getLoginUser().getUser().getSiteId());
        return result;
    }

    /**
     * 1、数据保存在 dub_shop 表，一级店铺的 parent_id 为 0
     * 2、新增门店的门店类型默认都是总店
     * 3、门店被绑定后，门店类型变为分店
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result addOrUpdate(ShopAddVo vo) {
        // 如果用户已有绑定关系，则不允许新增门店
        List<ShopUserRef> refsList = jdbcTemplate.query("SELECT * FROM dub_user_shop_ref WHERE user_id = ?",
                new BeanPropertyRowMapper<>(ShopUserRef.class), vo.getUserId());
        if (CollUtil.isNotEmpty(refsList)) {
            if (vo.getId() == null) {
                return Result.fail("该用户已有绑定关系，不允许新增门店");
            }
        }
        Shop shop = new Shop();
        if (vo.getId() == null) {
            shop.setCreateUserId(SecurityUtils.getLoginUser().getUser().getSiteId());
            shop.setCreateTime(new Date());
            shop.setStatus(1);
            shop.setType(StoreTypeEnum.PARENT.getCode());
            shop.setParentId(0L);
        } else {
            shop = shopRepository.getById(vo.getId());
        }
        shop.setShopName(vo.getShopName());
        if (shop.getParentId() != null && shop.getParentId() != 0) {
            shop.setType(StoreTypeEnum.BRANCH.getCode());
        }
        Shop s = shopRepository.save(shop);
        if (vo.getId() == null) {
            ShopUserRef ref = new ShopUserRef();
            ref.setShopId(s.getId());
            ref.setRole(StoreUserTypeEnum.ADMIN.getCode());
            ref.setUserId(vo.getUserId());
            shopUserRefRepository.save(ref);
        }
        return Result.success(true);
    }

    /**
     * 新增门店
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result add(ShopAddVo vo) {
        // 保证新增时 id 为空
        vo.setId(null);
        return this.addOrUpdate(vo);
    }

    /**
     * 修改门店
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @ShopPermission(operate = true, entity = Shop.class, idParam = "id")
    public Result update(ShopAddVo vo) {
        if (vo.getId() == null) {
            return Result.fail("修改门店时id不能为空");
        }
        return this.addOrUpdate(vo);
    }

    /**
     * 2.5.4 删除
     * 1、需要列表选择一条或者多条数据
     * 2、总店已经绑定分店，不能删除
     * 3、只有店的时候，可以删除，在删除总店数据时，需要删除以下表数据
     * a) dub_user_shop_ref，删除门店与用户的关系
     * b) dub_device，删除对应门店的设备
     * c) dub_shop，总店的数据，根据 ID 删除
     * 4、其他表的数据不删除，保留
     *
     * @param shopIds
     * @return
     */
    @Transactional
    @ShopPermission(operate = true, entity = Shop.class, idParam = "shopIds")
    public Result deleteByIds(List<Long> shopIds) {
        if (CollUtil.isEmpty(shopIds)) {
            return Result.fail("请选择要删除的店铺");
        }
        log.info("删除店铺，shopIds:{}", shopIds);
        List<Shop> list = shopRepository.findAllById(shopIds);
        if (CollUtil.isEmpty(list)) {
            return Result.fail("店铺不存在");
        }
        List<Shop> itemList = shopRepository.selectByParentIdIn(shopIds);
        Map<Long, List<Shop>> map = Optional.of(itemList.stream().collect(Collectors.groupingBy(Shop::getParentId)))
                .orElse(new HashMap<>());

        log.info("查询店铺列表，list:{}", itemList);
        for (Shop shopVo : itemList) {
            if (map.containsKey(shopVo.getId())) {
                return Result.fail(shopVo.getShopName() + "下有分店，不能删除");
            }
        }
        shopUserRefRepository.deleteAllByShopIdIn(shopIds);
        deviceRepository.deleteByShopIdIn(shopIds);
        shopRepository.deleteByIds(shopIds);
        return Result.success();
    }

    @ShopPermission(entity = Shop.class, idParam = "id")
    public Result detailById(Long id) {
        log.info("查询店铺详情，id:{}", id);
        String sql = "SELECT shop.id AS id, shop.shop_name AS shopName, u.id AS userId, u.nickname AS userName, u.phone AS userPhone "
                + "FROM dub_shop shop " + "LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id AND ref.role = 1 "
                + "LEFT JOIN dub_wechat_user u ON u.id = ref.user_id " + "WHERE shop.id = ?";
        List<ShopAddVo> vos = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopAddVo.class), id);
        if (CollUtil.isNotEmpty(vos)) {
            return Result.success(vos.get(0));
        }
        return Result.fail("查询错误【空数据】");
    }

    /**
     * 只有绑定门店才需要审核
     * 1、审核通过，修改审核状态为 1
     * 2、审核不通过，修改审核状态为 0，同时将 parent_id 改为 0
     *
     * @param examineVo
     * @return
     */
    @ShopPermission(entity = Shop.class, idParam = "id")
    public Result updateByStatus(ShopExamineVo examineVo) {
        log.info("只有绑定门店才需要审核，examineVo :{}", examineVo);
        String sql;
        if (examineVo.getStatus() == 0) {
            sql = "UPDATE dub_shop SET status = 0 WHERE id = ?";
        } else {
            sql = "UPDATE dub_shop SET status = 1 WHERE id = ?";
        }
        log.info("只有绑定门店才需要审核，sql:{}", sql);
        int i = jdbcTemplate.update(sql, examineVo.getId());
        if (i <= 0) {
            return Result.fail("操作失败");
        }
        return Result.success(i);
    }

    public Result getByAdminPhone(String phone) {
        String sql = "SELECT shop.id AS shopId, shop.shop_name AS shopName "
                + "FROM dub_shop shop LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id AND ref.role = 1 "
                + "LEFT JOIN dub_wechat_user u ON u.id = ref.user_id WHERE u.phone = ?";
        List<ShopBindVo> shopBindVos = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopBindVo.class), phone);
        if (CollUtil.isNotEmpty(shopBindVos)) {
            return Result.success(shopBindVos.get(0));
        }
        return Result.fail("查询错误【空数据】");
    }

    /**
     * 绑定门店时需要审核
     *
     * @param bindVo
     * @return
     */
    @ShopPermission(entity = Shop.class, idParam = "id")
    @Transactional(rollbackFor = Exception.class)
    public Result bind(ShopBindVo bindVo) {
        if (bindVo.getId() == null) {
            return Result.fail("店铺id不能为空");
        }
        if (bindVo.getParentId() == null) {
            return Result.fail("上级店铺id不能为空");
        }
        Shop shop = shopRepository.getById(bindVo.getId());
        log.info("查询店铺，shop:{}", shop);
        if (shop.getId() == null) {
            return Result.fail("店铺不存在");
        }
        if (shop.getStatus() != 1) {
            return Result.fail("店铺非审核通过状态");
        }
        String sql = "SELECT * FROM dub_wechat_user WHERE id = ?";
        List<WechatUserVo> wxUsers = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(WechatUserVo.class),
                bindVo.getUserId());
        if (CollUtil.isEmpty(wxUsers)) {
            return Result.fail("不存在此管理员用户");
        }
        shop.setParentId(bindVo.getParentId());
        shop.setType(StoreTypeEnum.BRANCH.getCode());
        shop.setStatus(2);
        return Result.success(shopRepository.save(shop));
    }

    /**
     * 解绑
     *
     * @param shopId
     * @return
     */
    @ShopPermission(operate = true, entity = Shop.class, idParam = "id")
    public Result unbind(Long shopId) {
        if (shopId == null) {
            return Result.fail("店铺id不能为空");
        }
        Shop shop = shopRepository.getById(shopId);
        if (shop.getId() == null) {
            return Result.fail("店铺不存在");
        }
        shop.setParentId(0L);
        shop.setStatus(1);
        shop.setType(StoreTypeEnum.PARENT.getCode());
        shopRepository.save(shop);
        return Result.success();
    }

    /**
     * 设置管理员
     *
     * @param settingVo
     * @return
     */
    @ShopPermission(operate = true, entity = Shop.class, idParam = "id")
    public Result settingAdmin(ShopSettingVo settingVo) {
        if (settingVo.getShopId() == null) {
            return Result.fail("店铺id不能为空");
        }
        if (settingVo.getUserId() == null) {
            return Result.fail("用户id不能为空");
        }
        if (settingVo.getRole() == 1) {
            String sql = "update dub_user_shop_ref set role = 2 where shop_id = ? and role = 1";
            jdbcTemplate.update(sql, settingVo.getShopId());

            String sql2 = "update dub_user_shop_ref set role = 1 where shop_id = ? and user_id = ?";
            int i = jdbcTemplate.update(sql2, settingVo.getShopId(), settingVo.getUserId());
            if (i <= 0) {
                return Result.fail("设置失败");
            }
        } else {
            ShopUserRef ref = new ShopUserRef();
            ref.setUserId(settingVo.getUserId());
            ref.setShopId(settingVo.getShopId());
            ref.setRole(StoreUserTypeEnum.USER.getCode());
            shopUserRefRepository.save(ref);
        }
        return Result.success();
    }

    /**
     * 获取未绑定店铺人员列表
     *
     * @return
     */
    public Result getNotBindShopUsers() {
        String sql = "SELECT wechat.id, wechat.nickname, wechat.phone " + "FROM dub_wechat_user wechat " +
                "LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id " + "WHERE ref.id IS NULL ";
        List<ShopUserResVo> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopUserResVo.class));
        return Result.success(list);
    }

    /**
     * 获取店铺人员列表
     *
     * @param shopId
     * @return
     */
    @ShopPermission(entity = Shop.class, idParam = "shopId")
    public Result listByShopId(Long shopId) {
        String sql = "SELECT wechat.id, wechat.nickname, wechat.phone, wechat.avatar, ref.role " +
                "FROM dub_wechat_user wechat " + "LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id " +
                "WHERE ref.id IS NOT NULL " + "AND ref.shop_id = ?";
        List<ShopUserResVo> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopUserResVo.class), shopId);
        return Result.success(list);
    }

    /**
     * 获取所有父级店铺
     *
     * @return
     */
    public Result getParentList() {
//        String sql = "SELECT shop.id, shop.shop_name, COUNT(s2.id) AS shop_count FROM dub_shop shop " +
//                "LEFT JOIN dub_shop s2 ON s2.parent_id = shop.id AND s2.`status` = 1 "
//                + "WHERE shop.parent_id = 0 AND shop.`status` = 1 " +
//                "GROUP BY shop.id " + "HAVING shop_count = 0";
        String sql = "SELECT shop.id, shop.shop_name, FROM dub_shop shop " +
                "WHERE shop.parent_id = 0 AND shop.`status` = 1 ";
        List<ShopBindVo> shopBindVos = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopBindVo.class));
        return Result.success(shopBindVos);
    }

    /**
     * 修改门店店员
     * 将店员设置为管理员时，需要将当前店员的类型改为管理员，将当前店铺下的管理员改为店员
     *
     * @param updatePerson
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @ShopPermission(entity = Shop.class, idParam = "shopId")
    public Result updatePerson(@Valid ShopUpdatePerson updatePerson) {
        if (updatePerson.getStatus() == 1) {
            String updateAdminSql = "update dub_user_shop_ref set role = 2 where shop_id = ? and role = 1";
            jdbcTemplate.update(updateAdminSql, updatePerson.getShopId());
            String updateShopSql = "update dub_user_shop_ref set role = 1 where shop_id = ? and user_id = ?";
            jdbcTemplate.update(updateShopSql, updatePerson.getShopId(), updatePerson.getUserId());
        } else if (updatePerson.getStatus() == 2) {
            String sql = "delete from dub_user_shop_ref where shop_id = ? and user_id = ? and role = 2";
            jdbcTemplate.update(sql, updatePerson.getShopId(), updatePerson.getUserId());
        } else if (updatePerson.getStatus() == 3) {
            // 查询门店是否存在
            Shop shop = shopRepository.getById(updatePerson.getShopId());
            if (shop.getId() == null) {
                return Result.fail("店铺不存在");
            }
            // 查询店员是否存在
            WechatUser wxUser = wechatUserRepository.getById(updatePerson.getUserId());
            if (wxUser.getId() == null) {
                return Result.fail("店员不存在");
            }
            // 绑定店员到门店
            ShopUserRef ref = new ShopUserRef();
            ref.setUserId(updatePerson.getUserId());
            ref.setShopId(updatePerson.getShopId());
            ref.setRole(StoreUserTypeEnum.USER.getCode());
            shopUserRefRepository.save(ref);
        }
        return Result.success();
    }

    /**
     * 门店店员列表
     *
     * @return
     */
    @ShopPermission(entity = Shop.class, idParam = "shopId")
    public Result shopPersonList(Long shopId) {
        String sql = "SELECT ref.shop_id, wechat.nickname, wechat.phone, wechat.avatar, ref.role,  ref.user_id " +
                "FROM dub_wechat_user wechat " + "LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id " +
                "AND ref.shop_id = ?";
        List<ShopUserVo> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopUserVo.class), shopId);
        return Result.success(list);
    }

    /**
     * 微信用户只能 查询 自己
     *
     * @param shopId
     * @return
     */
    @ShopPermission(entity = Shop.class, idParam = "shopId")
    public Result shopUserList(Long shopId, Boolean isAll) {
        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        if (!isAll && Objects.equals(user.getType(), UserTypeEnum.SITE.getType()) && user.getSiteId() != null) {
            boolean boo = wechatUserRepository.existsById(user.getSiteId());
            if (!boo) {
                return Result.success(new ArrayList<>());
            }
            WechatUser wxUser = wechatUserRepository.getById(user.getSiteId());
            log.info("查询用户：{}", wxUser);
            ShopUserVo vo = new ShopUserVo();
            vo.setNickname(wxUser.getNickname());
            vo.setPhone(wxUser.getPhone());
            vo.setAvatar(wxUser.getAvatar());
            vo.setUserId(wxUser.getId());
            vo.setUserId(wxUser.getId());
            return Result.success(Collections.singletonList(vo));
        }
        StringBuilder sqlBuilder = new StringBuilder(
                "SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar " +
                        "FROM dub_wechat_user wechat " + "LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id ");
        List<Object> params = new ArrayList<>();
        if (shopId > 0) {
            sqlBuilder.append(" AND (ref.shop_id = ? OR ref.id IS NULL) ");
            params.add(shopId);
        } else {
            sqlBuilder.append(" AND ref.id IS NULL ");
        }
        String finalSql = sqlBuilder.toString();
        log.info("查询用户列表，sql:{}", finalSql);
        List<ShopUserVo> list = jdbcTemplate.query(finalSql, new BeanPropertyRowMapper<>(ShopUserVo.class),
                params.toArray());
        return Result.success(list);
    }

    /**
     * 门店列表
     *
     * @return
     */
    public Result getShopList() {
        List<Shop> list;

        SysUserVo user = SecurityUtils.getLoginUser().getUser();
        if (user.getSiteId() != null) {
            List<Long> ids = shopRefUtil.getShopRef();
            if (CollUtil.isEmpty(ids)) {
                return Result.success(new ArrayList<>());
            }
            list = shopRepository.findAllById(ids);
        } else {
            list = shopRepository.findAll();
        }
        log.info("查询门店列表，list:{}", list);
        if (CollUtil.isEmpty(list)) {
            return Result.success(new ArrayList<>());
        }
        log.info("查询门店列表，list:{}", list);
        List<ShopSelectListVo> vos = list.stream().map(e -> {
            ShopSelectListVo vo = new ShopSelectListVo();
            vo.setId(e.getId());
            vo.setName(e.getShopName());
            return vo;
        }).collect(Collectors.toList());

        return Result.success(vos);
    }

    /**
     * 根据手机号查询 门店信息
     *
     * @param phone
     * @return
     */
    public Result searchShopByPhone(String phone) {
        String sql = "SELECT shop.shop_name, shop.id AS parentId, us.id AS userId, us.phone AS phoneNumber " +
                "FROM dub_user_shop_ref ref " + "LEFT JOIN dub_shop shop ON shop.id = ref.shop_id " +
                "LEFT JOIN dub_wechat_user us ON us.id = ref.user_id " + "WHERE shop.parent_id = 0 AND us.phone = ? ";
        List<ShopBindVo> bindVos = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopBindVo.class), phone);
        if (CollUtil.isNotEmpty(bindVos)) {
            return Result.success(bindVos.get(0));
        } else {
            return Result.fail("未找到该手机号绑定的门店");
        }
    }

    public Result shareBindShop() {
        Long shopId = shopRefUtil.getOneShop();
        return null;
    }
}
