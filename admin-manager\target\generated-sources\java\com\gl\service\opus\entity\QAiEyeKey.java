package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAiEyeKey is a Querydsl query type for AiEyeKey
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAiEyeKey extends EntityPathBase<AiEyeKey> {

    private static final long serialVersionUID = 1171091559L;

    public static final QAiEyeKey aiEyeKey = new QAiEyeKey("aiEyeKey");

    public final StringPath agentKey = createString("agentKey");

    public final StringPath apiKey = createString("apiKey");

    public final StringPath appId = createString("appId");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath prompt = createString("prompt");

    public final StringPath promptId = createString("promptId");

    public QAiEyeKey(String variable) {
        super(AiEyeKey.class, forVariable(variable));
    }

    public QAiEyeKey(Path<? extends AiEyeKey> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAiEyeKey(PathMetadata metadata) {
        super(AiEyeKey.class, metadata);
    }

}

