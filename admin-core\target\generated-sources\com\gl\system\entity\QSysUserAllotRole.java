package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysUserAllotRole is a Querydsl query type for SysUserAllotRole
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysUserAllotRole extends EntityPathBase<SysUserAllotRole> {

    private static final long serialVersionUID = 344951948L;

    public static final QSysUserAllotRole sysUserAllotRole = new QSysUserAllotRole("sysUserAllotRole");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Long> roleId = createNumber("roleId", Long.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QSysUserAllotRole(String variable) {
        super(SysUserAllotRole.class, forVariable(variable));
    }

    public QSysUserAllotRole(Path<? extends SysUserAllotRole> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysUserAllotRole(PathMetadata metadata) {
        super(SysUserAllotRole.class, metadata);
    }

}

