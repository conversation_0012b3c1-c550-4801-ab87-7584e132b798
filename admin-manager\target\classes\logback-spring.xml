<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<property name="PATTERN" value="%date{HH:mm:ss.SSS} %highlight(%-5level) [%thread] %logger{36} - %msg%n" />
	<property name="LOG_FILE_PATH" value="log" />
	<springProperty scope="context" name="LOG_FILE_NAME" source="spring.application.name"/>
	<property name="MAX_HISTORY" value="10" />

	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${PATTERN}</pattern>
		</encoder>
	</appender>
	
	<appender name="LOG-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_FILE_PATH}/${LOG_FILE_NAME}.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_FILE_PATH}/${LOG_FILE_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
			<MaxHistory>${MAX_HISTORY}</MaxHistory>
		</rollingPolicy>

		<layout class="ch.qos.logback.classic.PatternLayout">
			<pattern>${PATTERN}</pattern>
		</layout>
	</appender>

	<root level="info">
		<appender-ref ref="console" />
		<appender-ref ref="LOG-FILE" />
	</root>

</configuration>