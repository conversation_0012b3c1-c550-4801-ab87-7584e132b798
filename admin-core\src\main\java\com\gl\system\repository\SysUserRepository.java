package com.gl.system.repository;

import com.gl.system.entity.SysUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SysUserRepository extends JpaRepository<SysUser, Long>, JpaSpecificationExecutor<SysUser> {

	SysUser findByPhone(String phone);

	SysUser findByEmail(String email);

	int countByLoginName(String loginName);

	int countByLoginNameAndDeleted(String loginName,Integer deleted);

	int countByPhone(String loginName);

	int countByPhoneAndDeleted(String loginName,Integer deleted);

	int countByEmail(String email);

	int countByEmailAndDeleted(String email,Integer deleted);


	List<SysUser> findByIdIn(List<Long> ids);

	/**
	 * 根据登录账号查询
	 *
	 * @param loginName 登录账号
	 * @return
	 */
	SysUser findByLoginName(String loginName);

	/**
	 * 查询部门是否存在用户
	 *
	 * @param deptId
	 * @return
	 */
	int countByDeptId(Long deptId);

	List<SysUser> findByDeptId(Long deptId);


	List<SysUser> findByDeptIdAndDeleted(Long deptId,Integer beleted);

	@Transactional
	@Modifying
	@Query(value = "delete from SysUser u where u.id in (?1)")
	void deleteBatch(List<Long> ids);

	/**
	 * 查询为删除，状态正确，有权限的部门用户
	 * @param deleted
	 * @param status
	 * @param ids
	 * @return
	 * <AUTHOR>
	 * @date 2022年5月7日
	 */
	List<SysUser> findByDeletedAndStatusAndDeptIdIn(Integer deleted, Integer status, List<Long> ids);


	SysUser findByUserNameAndTypeAndDeleted(String userName,Integer type,Integer deleted);

	SysUser findByLoginNameAndTypeAndDeleted(String LoginName,Integer type,Integer deleted);

	SysUser findByEmailAndTypeAndDeleted(String email,Integer type,Integer deleted);

	SysUser findByPhoneAndTypeAndDeleted(String Phone,Integer type,Integer deleted);

	@Query(value = "select * from sys_user where site_id = ?1 and deleted = 0",nativeQuery = true)
    SysUser getBySiteId(@Param("siteId") Long siteId);
}
