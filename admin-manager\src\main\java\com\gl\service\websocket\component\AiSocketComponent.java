package com.gl.service.websocket.component;

import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.Role;
import com.alibaba.fastjson.JSON;
import com.aliyun.bailian20230601.Client;
import com.aliyun.bailian20230601.models.GetPromptRequest;
import com.aliyun.bailian20230601.models.GetPromptResponse;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.redis.RedisService;
import com.gl.service.opus.entity.AiEyeKey;
import com.gl.service.opus.entity.AiKey;
import com.gl.service.opus.entity.CategoryAiKey;
import com.gl.service.opus.repository.AiEyeKeyRepository;
import com.gl.service.opus.repository.AiKeyRepository;
import com.gl.service.opus.repository.CategoryAiKeyRepository;
import com.gl.service.websocket.entity.AiMessage;
import com.gl.service.websocket.entity.AiPromptParam;
import com.gl.service.websocket.entity.AiResult;
import com.gl.system.vo.SysUserVo;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.reactivex.Flowable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import com.aliyun.teaopenapi.models.Config;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.gl.framework.security.service.TokenService;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-02-01-12:51
 */
@Component
public class AiSocketComponent {

    @Value("${ty.accessKeyId}")
    private String tyAccessKeyId;
    @Value("${ty.accessKeySecret}")
    private String tyAccessKeySecret;
    @Resource
    private AiKeyRepository aiKeyRepository;
    @Resource
    private RedisService redisService;
    @Resource
    private AiEyeKeyRepository aiEyeKeyRepository;
    @Resource
    private CategoryAiKeyRepository categoryAiKeyRepository;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private TokenService tokenService;

    public void ai(AiMessage aiMessage, ChannelHandlerContext channelHandlerContext) {
        AiKey aiKey = aiKeyRepository.findAll().get(0);
        SysUserVo user = null;
        if (StringUtils.isNotBlank(aiMessage.getToken())) {
            try {
                LoginUser loginUser = tokenService
                        .getLoginUser(aiMessage.getToken().replace("Bearer ", ""));
                if (loginUser != null) {
                    user = loginUser.getUser();
                }
            } catch (Exception e) {
                // ignore, will handle null user below
            }
        }
        AiResult aiResult = new AiResult();
        if (user == null) {
            aiResult.setMessage("用户未登陆");
            aiResult.setFlag(1);
            channelHandlerContext.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
            return;
        }
        try {
            Config config = new Config().setAccessKeyId(tyAccessKeyId).setAccessKeySecret(tyAccessKeySecret)
                    .setEndpoint("bailian.cn-beijing.aliyuncs.com");
            Client client = new Client(config);
            if (StringUtils.isBlank(aiMessage.getAiClass())) {
                aiMessage.setAiClass(aiMessage.getAiText());
            }
            if (StringUtils.isBlank(aiMessage.getAiStyle())) {
                aiMessage.setAiStyle(aiMessage.getAiText());
            }
            AiPromptParam aiPromptParam = new AiPromptParam();
            BeanUtils.copyProperties(aiMessage, aiPromptParam);
            Map<String, String> map = new HashMap<>();
            map.put("AgentKey", aiKey.getAgentKey());
            map.put("PromptId", aiKey.getPromptId());
            map.put("Vars", JSON.toJSONString(aiPromptParam));
            GetPromptResponse response = client.getPrompt(GetPromptRequest.build(map));
            String prompt;
            if (response.body.getSuccess()) {
                prompt = response.getBody().getData().getPromptContent();
            } else {
                String content = aiKey.getPrompt();
                prompt = String.format(content, aiPromptParam.getAiClass(), aiPromptParam.getAiStyle(),
                        aiPromptParam.getAiText(),
                        aiPromptParam.getAiLanguage(), aiPromptParam.getAiCount(), aiPromptParam.getAiCount());
            }
            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(aiKey.getApiKey())
                    .appId(aiKey.getAppId())
                    .prompt(prompt)
                    .incrementalOutput(true)
                    .hasThoughts(true)
                    .build();
            if (aiMessage.getIsFollow() == 1) {
                param.setSessionId(aiMessage.getSessionId());
            }
            Application application = new Application();
            Flowable<ApplicationResult> result = application.streamCall(param);
            result.blockingForEach(data -> {
                aiResult.setMessage(data.getOutput().getText().replaceAll("###", "<br/>"));
                aiResult.setSessionId(data.getOutput().getSessionId());
                aiResult.setIsEnd(data.getOutput().getFinishReason());
                aiResult.setFlag(0);
                channelHandlerContext.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void aiEye(AiMessage aiMessage, ChannelHandlerContext channelHandlerContext) {
        AiEyeKey aiEyeKey = aiEyeKeyRepository.findAll().get(0);
        SysUserVo user = null;
        if (StringUtils.isNotBlank(aiMessage.getToken())) {
            try {
                LoginUser loginUser = tokenService
                        .getLoginUser(aiMessage.getToken().replace("Bearer ", ""));
                if (loginUser != null) {
                    user = loginUser.getUser();
                }
            } catch (Exception e) {
                // ignore, will handle null user below
            }
        }
        AiResult aiResult = new AiResult();
        if (user == null) {
            aiResult.setMessage("用户未登陆");
            aiResult.setFlag(1);
            channelHandlerContext.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
            return;
        }
        try {
            Config config = new Config().setAccessKeyId(tyAccessKeyId).setAccessKeySecret(tyAccessKeySecret)
                    .setEndpoint("bailian.cn-beijing.aliyuncs.com");
            Client client = new Client(config);
            if (StringUtils.isBlank(aiMessage.getAiClass())) {
                aiMessage.setAiClass(aiMessage.getAiText());
            }
            if (StringUtils.isBlank(aiMessage.getAiStyle())) {
                aiMessage.setAiStyle(aiMessage.getAiText());
            }
            MultiModalConversationResult result = null;
            if (aiMessage.isSendImage()) {
                MultiModalConversation conv = new MultiModalConversation();
                MultiModalMessage systemMessage = MultiModalMessage.builder()
                        .role(Role.SYSTEM.getValue())
                        .content(Arrays
                                .asList(Collections.singletonMap("text", "你是一个能从图片中完全正确识别出店铺用于促销物品，如果图片有价格额外识别出价格的专家")))
                        .build();
                MultiModalMessage userMessage = MultiModalMessage.builder()
                        .role(Role.USER.getValue())
                        .content(Arrays.asList(Collections.singletonMap("image", aiMessage.getImage()),
                                Collections.singletonMap("text", "这个店铺卖的东西是啥，只说物品名称，如果图片里面标注了价格就写上价格")))
                        .build();
                MultiModalConversationParam param = MultiModalConversationParam.builder()
                        .apiKey(aiEyeKey.getApiKey())
                        .model("qwen-vl-max-latest").message(systemMessage)
                        .message(userMessage).build();
                result = conv.call(param);
            } else {
                MultiModalConversation conv = new MultiModalConversation();
                MultiModalMessage systemMessage = MultiModalMessage.builder()
                        .role(Role.SYSTEM.getValue())
                        .content(Arrays
                                .asList(Collections.singletonMap("text", "你是一个能从视频中完全正确识别出店铺用于促销物品，如果视频有价格额外识别出价格的专家")))
                        .build();
                MultiModalMessage userMessage = MultiModalMessage.builder()
                        .role(Role.USER.getValue())
                        .content(Arrays.asList(Collections.singletonMap("video", aiMessage.getVideo()),
                                Collections.singletonMap("text", "从这个视频找出这个店铺所有卖的东西是啥，只说物品名称，如果视频里面标注了价格就写上价格")))
                        .build();
                MultiModalConversationParam param = MultiModalConversationParam.builder()
                        .apiKey(aiEyeKey.getApiKey())
                        .model("qwen-vl-max-latest").message(systemMessage)
                        .message(userMessage).build();
                result = conv.call(param);
            }
            MultiModalMessage message = result.getOutput().getChoices().get(0).getMessage();
            List<Map<String, Object>> messageContent = message.getContent();
            Map<String, Object> resMap = messageContent.get(0);
            aiMessage.setAiText(String.valueOf(resMap.get("text")));
            AiPromptParam aiPromptParam = new AiPromptParam();
            BeanUtils.copyProperties(aiMessage, aiPromptParam);
            Map<String, String> map = new HashMap<>();
            map.put("AgentKey", aiEyeKey.getAgentKey());
            map.put("PromptId", aiEyeKey.getPromptId());
            map.put("Vars", JSON.toJSONString(aiPromptParam));
            GetPromptResponse response = client.getPrompt(GetPromptRequest.build(map));
            String prompt;
            if (response.body.getSuccess()) {
                prompt = response.getBody().getData().getPromptContent();
            } else {
                String content = aiEyeKey.getPrompt();
                prompt = String.format(content, aiPromptParam.getAiClass(), aiPromptParam.getAiStyle(),
                        aiPromptParam.getAiText(),
                        aiPromptParam.getAiLanguage(), aiPromptParam.getAiCount(), aiPromptParam.getAiCount());
            }
            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(aiEyeKey.getApiKey())
                    .appId(aiEyeKey.getAppId())
                    .prompt(prompt)
                    .incrementalOutput(true)
                    .build();
            if (aiMessage.getIsFollow() == 1) {
                param.setSessionId(aiMessage.getSessionId());
            }
            Application application = new Application();
            Flowable<ApplicationResult> flowableResult = application.streamCall(param);
            flowableResult.blockingForEach(data -> {
                aiResult.setMessage(data.getOutput().getText().replaceAll("###", "<br/>"));
                aiResult.setSessionId(data.getOutput().getSessionId());
                aiResult.setIsEnd(data.getOutput().getFinishReason());
                aiResult.setFlag(0);
                channelHandlerContext.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void deepSeekAi(AiMessage aiMessage, ChannelHandlerContext channelHandlerContext) {
        CategoryAiKey categoryAiKey = categoryAiKeyRepository.findByCategoryId(aiMessage.getCategoryId());
        SysUserVo user = null;
        if (StringUtils.isNotBlank(aiMessage.getToken())) {
            try {
                LoginUser loginUser = tokenService
                        .getLoginUser(aiMessage.getToken().replace("Bearer ", ""));
                if (loginUser != null) {
                    user = loginUser.getUser();
                }
            } catch (Exception e) {
            }
        }
        AiResult aiResult = new AiResult();
        if (user == null) {
            aiResult.setMessage("用户未登陆");
            aiResult.setFlag(1);
            channelHandlerContext.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
            return;
        }
        try {
            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(categoryAiKey.getApiKey())
                    .appId(categoryAiKey.getAppId())
                    .prompt(aiMessage.getAiText())
                    .incrementalOutput(true)
                    .hasThoughts(true)
                    .build();
            if (aiMessage.getIsFollow() == 1) {
                param.setSessionId(aiMessage.getSessionId());
            }
            Application application = new Application();
            Flowable<ApplicationResult> result = application.streamCall(param);
            // 创建锁的Key：用户ID + 分类ID
            String lockKey = "ai_lock:" + user.getSiteId() + ":" + aiMessage.getCategoryId();
            // 使用AtomicBoolean跟踪是否完成
            AtomicBoolean isCompleted = new AtomicBoolean(false);
            boolean lockAcquired = StringUtils.isBlank(redisTemplate.opsForValue().get(lockKey));
            // 如果获取锁失败，说明该用户正在进行此分类的对话
            if (!lockAcquired) {
                aiResult.setMessage("您有一个正在进行的对话，请等待完成后再试");
                aiResult.setFlag(0);
                channelHandlerContext.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
                return;
            }
            redisTemplate.opsForValue().set(lockKey, "1", 30, TimeUnit.MILLISECONDS);
            result.blockingForEach(data -> {
                String thought = "";
                if (data.getOutput().getThoughts().size() > 1) {
                    thought = data.getOutput().getThoughts().get(1).getThought();
                } else {
                    thought = data.getOutput().getThoughts().get(0).getThought();
                }
                aiResult.setThought(thought);
                aiResult.setMessage(data.getOutput().getText().replaceAll("###", "")
                        .replaceAll("\\*", ""));
                aiResult.setSessionId(data.getOutput().getSessionId());
                aiResult.setIsEnd(data.getOutput().getFinishReason());
                aiResult.setFlag(0);
                channelHandlerContext.writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(aiResult)));
                // 检查是否完成对话
                if (data.getOutput().getFinishReason() != null && data.getOutput().getFinishReason().equals("stop")) {
                    isCompleted.set(true);
                    // 释放锁
                    redisTemplate.delete(lockKey);
                }
            });
            // 如果处理完毕但没有明确结束（防止某些异常情况），也释放锁
            if (!isCompleted.get() && lockAcquired) {
                redisTemplate.delete(lockKey);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
