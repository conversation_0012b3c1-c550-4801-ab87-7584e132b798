package com.gl.redis;

import com.gl.commons.constant.RedisKeyConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

@Service
public class RedisService {
    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取系统参数数据
     *
     * @param key
     * @return
     */
    public String getValue(String key) {
        Object object = redisTemplate.opsForHash().get(Constant.MZJ_MG_PARAMS, key);
        if (object == null) {
            return null;
        }

        return object.toString().replace("\"", "");
    }
    /**
     * 获取小程序系统参数数据
     * @param key
     * @return
     */
    public String getRedisValue(String key) {
        Object object = redisTemplate.opsForHash().get(Constant.MZJ_PY_SYS_PARAMS, key);
        if (object == null) {
            return null;
        }

        return object.toString();
    }

    /**
     * 缓存语音合成token
     * @param token
     * @param time
     */
    public void putVoiceToken(String token, Long time) {
        redisTemplate.opsForValue().set(Constant.VOICE_TOKEN, token, time, TimeUnit.SECONDS);
    }

    /**
     * 获取语音合成token
     * @return
     */
    public String getVoiceToken() {
        return redisTemplate.opsForValue().get(Constant.VOICE_TOKEN);
    }

    /**
     * 缓存语音合成名称序号
     *
     * @param number
     */
    public void putVoiceNumber(Integer number,Long userId) {
        String key = String.format(RedisKeyConstant.VOICE_NUMBER, userId);
        redisTemplate.opsForValue().set(key, String.valueOf(number));
        redisTemplate.expire(String.format(RedisKeyConstant.VOICE_NUMBER, userId),600,TimeUnit.HOURS);
    }

    /**
     * 获取语音合成名称序号
     *
     * @return
     */
    public Integer getVoiceNumber(Long userId) {
        String key = String.format(RedisKeyConstant.VOICE_NUMBER, userId);
        Random random = new Random();
        Integer number = null;
        String voiceNumber = redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(voiceNumber)) {
            number = random.nextInt(99) + 1;
        } else {
            number = Integer.parseInt(voiceNumber);
            number = number + 1;
            // 如果长度到了 99，9999 ， 则重新从000001开始
            if (number > 99) {
                number = random.nextInt(99) + 1;
            }
        }
        return number;
    }

    public String getPyLockNum(String taskId) {
        return redisTemplate.opsForValue().get(String.format(RedisKeyConstant.PYLOCK_NUM, taskId));
    }

    public void incrPyLockNum(String taskId) {
        redisTemplate.opsForValue().increment(String.format(RedisKeyConstant.PYLOCK_NUM, taskId), 1);
        redisTemplate.expire(String.format(RedisKeyConstant.PYLOCK_NUM, taskId), 5, TimeUnit.SECONDS);
    }

    /**
     * 合成判断重复
     *
     * @param dto
     * @param time
     */
    public void setvoiceRepeat(Long userId, String dto, Long time) {
        redisTemplate.opsForValue().set(String.format(RedisKeyConstant.PY_REPEAT, userId), dto, time, TimeUnit.MILLISECONDS);
    }

    /**
     * 合成判断重复
     *
     * @return
     */
    public String getvoiceRepeat(Long userId) {
        return redisTemplate.opsForValue().get(String.format(RedisKeyConstant.PY_REPEAT, userId));
    }
}
