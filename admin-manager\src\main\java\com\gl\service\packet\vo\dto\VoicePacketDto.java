package com.gl.service.packet.vo.dto;

import com.gl.framework.web.domain.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: duanjinze
 * @date: 2022/11/11 13:38
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoicePacketDto extends BaseVo {
    /**
     * 语音包id
     */
    private Long id;

    /**
     * 搜索框 【语音包名称/所属用户/用户手机】
     */
    private String searchCondition;

    private Long shopId;
}
