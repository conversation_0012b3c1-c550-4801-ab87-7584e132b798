package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 *   @ Date：2024-08-08-10:46
 */
@Entity
@Table(name = "ai_style")
@Data
public class AiStyle {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "title")
    private String title;
    @Basic
    @Column(name = "content")
    private String content;
    @Basic
    @Column(name = "del_status", columnDefinition = "smallint")
    private Integer delStatus;
    @Basic
    @Column(name = "order_index", columnDefinition = "smallint")
    private Integer orderIndex;
}
