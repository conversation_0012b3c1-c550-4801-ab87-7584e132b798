package com.gl.system.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.system.entity.SysOperLog;
import com.gl.system.repository.SysOperLogRepository;
import com.gl.system.vo.SysOperLogVo;
import com.gl.system.vo.SysUserVo;
import com.gl.framework.web.domain.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.List;
import java.util.Optional;

/**
 * 操作日志服务实现类
 */
@Service
public class SysOperLogService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SysOperLogRepository operLogRepository;

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    public void saveOperlog(SysOperLog operLog) {
        operLogRepository.save(operLog);
    }

    /**
     * 查询系统操作日志集合
     *
     * @param filter 过滤条件
     * @return 操作日志集合
     */
    public PageData<SysOperLogVo> selectOperLogList(SysOperLogVo filter) {
        // 数据权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        boolean admin = SecurityUtils.isSuperAdmin(user.getId());

        PageData<SysOperLogVo> pageData = new PageData<>();

        Page<SysOperLogVo> page = operLogRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.and();

            if (StringUtils.isNotBlank(filter.getTitle())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("title"), "%" + filter.getTitle() + "%"));
            }
            if (StringUtils.isNotBlank(filter.getOperName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("operName"), "%" + filter.getOperName() + "%"));
            }
            if (filter.getBusinessType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("businessType"), filter.getBusinessType()));
            }
            if (filter.getStatus() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), filter.getStatus()));
            }
            if (StringUtils.isNotBlank(filter.getBeginTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get("operTime").as(String.class), filter.getBeginTime() + " 00:00:00"));
            }
            if (StringUtils.isNotBlank(filter.getEndTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThan(root.get("operTime").as(String.class), filter.getEndTime() + " 23:59:59"));
            }
            
            if(StringUtils.isNoneBlank(filter.getBusinessTypeName())){
            	predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("businessTypeName"), "%" + filter.getBusinessTypeName() + "%"));
            }

            return predicate;
        }, PageRequest.of(filter.getPageNumber() - 1, filter.getPageSize(), Direction.DESC, "id")).map(this::convert);

        long total = page.getTotalElements();
        List<SysOperLogVo> data = page.getContent();

        pageData.setTotal(total);
        pageData.setData(data);

        return pageData;
    }

    /**
     * 数据转换
     *
     * @param entity
     * @return
     */
    private SysOperLogVo convert(SysOperLog entity) {
        BeanCopier beanCopier = BeanCopier.create(SysOperLog.class, SysOperLogVo.class, false);
        SysOperLogVo vo = new SysOperLogVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public void deleteOperLogByIds(List<Long> operIds) {
        operLogRepository.deleteByIdIn(operIds);
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLogVo selectOperLogById(Long operId) {
        Optional<SysOperLog> optional = operLogRepository.findById(operId);
        if (!optional.isPresent()) {
            throw new CustomException("数据不存在");
        }

        SysOperLog entity = optional.get();

        BeanCopier beanCopier = BeanCopier.create(SysOperLog.class, SysOperLogVo.class, false);
        SysOperLogVo vo = new SysOperLogVo();
        beanCopier.copy(entity, vo, null);

        return vo;
    }

    /**
     * 清空操作日志
     */
    public void cleanOperLog() {
        jdbcTemplate.execute("TRUNCATE TABLE sys_oper_log");
    }
}
