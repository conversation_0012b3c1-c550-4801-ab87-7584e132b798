package com.gl.service.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 微信用户表
 * @author: duanjinze
 * @date: 2022/11/10 16:16
 * @version: 1.0
 */
@Data
public class WechatUserVo {
    private Long id;

    /**
     * openid
     */
    private String openid;

    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String avatar;

    /**
     * 0未知 1男 2女
     */
    private Integer gender;

    /**
     * 手机
     */
    private String phone;

    /**
     * 地区
     */
    private String area;

    /**
     * 授权时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date authTime;

    private String shopName;
}
