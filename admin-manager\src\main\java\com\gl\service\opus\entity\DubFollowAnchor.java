package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2025-06-19-13:11
 */
@Data
@Entity
@Table(name = "dub_follow_anchor")
public class DubFollowAnchor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "user_id")
    private Long userId;

    @Basic
    @Column(name = "anchor_id")
    private Long anchorId;
}
