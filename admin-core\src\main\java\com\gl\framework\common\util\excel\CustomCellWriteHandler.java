package com.gl.framework.common.util.excel;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自动设置列宽处理器
 */
public class CustomCellWriteHandler extends AbstractColumnWidthStyleStrategy {

	private Map<Integer, Map<Integer, Integer>> CACHE = new HashMap<>();

	@SuppressWarnings("rawtypes")
	@Override
	protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cellDataList, Cell cell, Head head,
			Integer relativeRowIndex, Boolean isHead) {
		boolean needSetWidth = isHead || !CollectionUtils.isEmpty(cellDataList);
		if (needSetWidth) {
			Map<Integer, Integer> maxColumnWidthMap = CACHE.get(writeSheetHolder.getSheetNo());
			if (maxColumnWidthMap == null) {
				maxColumnWidthMap = new HashMap<>();
				CACHE.put(writeSheetHolder.getSheetNo(), maxColumnWidthMap);
			}

			Integer columnWidth = this.dataLength(cellDataList, cell, isHead);
			if (columnWidth >= 0) {
				if (columnWidth > 255) {
					columnWidth = 255;
				}

				Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
				if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
					maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
					writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
				}

			}
		}
	}

	@SuppressWarnings("rawtypes")
	private Integer dataLength(List<CellData> cellDataList, Cell cell, Boolean isHead) {
		if (isHead) {
			return cell.getStringCellValue().getBytes().length;
		} else {
			CellData cellData = cellDataList.get(0);
			CellDataTypeEnum type = cellData.getType();
			if (type == null) {
				return -1;
			} else {
				switch (type) {
				case STRING:
					return cellData.getStringValue().getBytes().length;
				case BOOLEAN:
					return cellData.getBooleanValue().toString().getBytes().length;
				case NUMBER:
					return cellData.getNumberValue().toString().getBytes().length;
				default:
					return -1;
				}
			}
		}
	}
}
