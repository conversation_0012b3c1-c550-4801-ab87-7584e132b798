package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysRoleMenu is a Querydsl query type for SysRoleMenu
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysRoleMenu extends EntityPathBase<SysRoleMenu> {

    private static final long serialVersionUID = 914966234L;

    public static final QSysRoleMenu sysRoleMenu = new QSysRoleMenu("sysRoleMenu");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Long> menuId = createNumber("menuId", Long.class);

    public final NumberPath<Long> roleId = createNumber("roleId", Long.class);

    public QSysRoleMenu(String variable) {
        super(SysRoleMenu.class, forVariable(variable));
    }

    public QSysRoleMenu(Path<? extends SysRoleMenu> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysRoleMenu(PathMetadata metadata) {
        super(SysRoleMenu.class, metadata);
    }

}

