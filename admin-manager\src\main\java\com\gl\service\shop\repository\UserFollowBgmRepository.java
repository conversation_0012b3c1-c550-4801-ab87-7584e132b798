package com.gl.service.shop.repository;


import com.gl.service.opus.entity.UserFollowBgm;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface UserFollowBgmRepository extends PagingAndSortingRepository<UserFollowBgm,Long>, JpaSpecificationExecutor<UserFollowBgm> {


	List<UserFollowBgm> findByBgmIdAndUserId(Long bgmId, Long id);
}
