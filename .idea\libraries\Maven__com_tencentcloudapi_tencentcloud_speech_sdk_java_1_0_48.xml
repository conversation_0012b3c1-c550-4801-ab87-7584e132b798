<component name="libraryTable">
  <library name="Maven: com.tencentcloudapi:tencentcloud-speech-sdk-java:1.0.48">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/tencentcloudapi/tencentcloud-speech-sdk-java/1.0.48/tencentcloud-speech-sdk-java-1.0.48.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/tencentcloudapi/tencentcloud-speech-sdk-java/1.0.48/tencentcloud-speech-sdk-java-1.0.48-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/tencentcloudapi/tencentcloud-speech-sdk-java/1.0.48/tencentcloud-speech-sdk-java-1.0.48-sources.jar!/" />
    </SOURCES>
  </library>
</component>