package com.gl.config.mqtt;

import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.StringUtils;
import com.gl.service.deviceOperationLog.service.DeviceOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MqttSubscriptCallBack implements MqttCallback {

    @Value("${mqtt.clientId}")
    private String clientId;

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private DeviceOperationLogService deviceOperationLogService;

    /**
     * 与服务器断开的回调
     */
    @Override
    public void connectionLost(Throwable cause) {
        log.error(clientId + "-Sub" + "与服务器断开连接！！" + cause.getMessage());
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        int lastIndex = topic.lastIndexOf("/");
        String subTopic = topic.substring(lastIndex + 1);
        String deviceSn = topic.substring(topic.lastIndexOf("/") + 1);

        String payload = new String(message.getPayload());
        JSONObject jsonObject = JSONObject.parseObject(payload);
        String timeStamp = jsonObject.getString("timeStamp");

        log.info(String.format("接收消息主题 : %s", topic));
        log.info(String.format("接收消息Qos : %d", message.getQos()));
        log.info(String.format("接收消息内容 : %s", payload));
        log.info(String.format("接收消息retained : %b", message.isRetained()));
        log.info(String.format("接收消息 timeStamp : %b", timeStamp));

        if (StringUtils.isNotEmpty(timeStamp) && !timeStamp.startsWith("2-")) {
            return;
        }

        String key = deviceSn + "-" + timeStamp;
        redisTemplate.opsForValue().set(key, payload, 300, TimeUnit.SECONDS);

        deviceOperationLogService.update(timeStamp, payload);
    }

    /**
     * 消息发布成功的回调
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        IMqttAsyncClient client = token.getClient();
        log.info(client.getClientId() + "发布消息成功！");

    }
}
