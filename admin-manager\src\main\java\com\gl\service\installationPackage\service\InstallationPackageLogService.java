package com.gl.service.installationPackage.service;

import com.gl.framework.web.response.Result;
import com.gl.service.installationPackage.entity.InstallationPackageLog;
import com.gl.service.installationPackage.repository.InstallationPackageLogRepository;
import com.gl.service.installationPackage.vo.installationPackageLog.InstallationPackageLogVo;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDelDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogRenewalDto;
import com.gl.util.GetShopRefUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class InstallationPackageLogService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private InstallationPackageLogRepository installationPackageLogRepository;
    @Autowired
    private AsyncPackageService asyncPackageService;

    @Autowired
    private GetShopRefUtil shopRefUtil;


    public Result list(InstallationPackageLogDto dto, Integer exportType) {
        Result result = Result.success();

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and ( p.version_name like ?  or d.sn like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
            }


            if (com.gl.framework.common.util.StringUtils.isNotBlank(dto.getBeginTime())) {
                where.append(" and l.create_time >= ? ");
                args.add(dto.getBeginTime());
            }
            if (com.gl.framework.common.util.StringUtils.isNotBlank(dto.getEndTime())) {
                where.append(" and l.create_time <= ? ");
                args.add(dto.getEndTime());
            }

        }

        String sql = "select \n" +
                "l.id,\n" +
                "s.shop_name ,\n" +
                "d.`name` deviceName,\n" +
                "d.sn,\n" +
                "p.version_name ,\n" +
                "l.create_time ,\n" +
                "l.response_time,\n" +
                "l.status \n" +
                "from dub_installation_package_log l \n" +
                "left join dub_installation_package p on l.package_id = p.id \n" +
                "left join dub_device d on l.device_id = d.id \n" +
                "left join dub_shop s on s.id=d.shop_id " +
                "where 1=1 ";


        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }

        where.append("order by l.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<InstallationPackageLogVo> deviceVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(InstallationPackageLogVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", deviceVos);
        return result;
    }


    public Result delete(InstallationPackageLogDelDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getIds().isEmpty()) {
            return Result.fail("更新日志id不能为空");
        }
        for (Long id : dto.getIds()) {
            installationPackageLogRepository.deleteById(id);
        }
        return Result.success();
    }


    public Result renewal(InstallationPackageLogRenewalDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getDeviceIdList().isEmpty()) {
            return Result.fail("选中设备不能为空");
        }
        if (dto.getInstallationPackageId() == null) {
            return Result.fail("安装包id不能为空");
        }

        // 日志
        List<Long> deviceIdList = dto.getDeviceIdList();
        List<InstallationPackageLog> list = new ArrayList<>();
        for (Long deviceId : deviceIdList) {
            InstallationPackageLog log = new InstallationPackageLog();
            log.setDeviceId(deviceId);
            log.setCreateTime(new Date());
            log.setPackageId(dto.getInstallationPackageId());
            log.setStatus(1);
            list.add(log);
        }
        List<InstallationPackageLog> sList = installationPackageLogRepository.saveAll(list);
        asyncPackageService.uploadPackage(sList);
        return Result.success();
    }
}
