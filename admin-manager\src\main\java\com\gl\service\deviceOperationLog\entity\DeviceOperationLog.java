package com.gl.service.deviceOperationLog.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2025/4/21
 * @description:
 */
@Data
@Entity
@Table(name = "dub_device_operation_log")
public class DeviceOperationLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 设备ID
    private Long deviceId;

    // 操作用户ID
    private Long userId;

    // 设备ID
    private Long shopId;

    // 类型
    private String type;

    // 保存发送包的全部数据
    @Column(columnDefinition = "text")
    private String content;

    // 发送时间
    private Date sendTime;

    // 响应时间
    private Date responseTime;

    // 响应内容
    private String responseContent;

    // 流水号
    private String timeStamp;

    // 流水号
    private String topic;

}
