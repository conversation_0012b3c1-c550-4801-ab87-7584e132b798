package com.gl.service.pay.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.service.WxPayService;
import com.gl.commons.enums.DeviceFlowStateEnum;
import com.gl.commons.enums.PayStatusEnum;
import com.gl.framework.common.util.DateUtils;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.exception.CustomException;
import com.gl.framework.web.response.Result;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.opus.entity.Device;
import com.gl.service.order.entity.Order;
import com.gl.service.order.entity.OrderItem;
import com.gl.service.order.entity.OrderPayRecord;
import com.gl.service.order.repository.OrderItemRepository;
import com.gl.service.order.repository.OrderPayRecordRepository;
import com.gl.service.order.repository.OrderRepository;
import com.gl.service.paidPackages.entity.PaidPackages;
import com.gl.service.paidPackages.repository.PaidPackagesRepository;
import com.gl.service.pay.conf.WechatPayV3Config;
import com.gl.service.pay.vo.PayParams;
import com.gl.service.pay.vo.WechatPayNotifyVo;
import com.gl.wechat.entity.WechatUser;
import com.gl.wechat.repository.WechatUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/wechatpay")
public class WechatPayController {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private PaidPackagesRepository paidPackagesRepository;
    @Resource
    private WechatPayV3Config wechatPayV3Config;
    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private OrderItemRepository orderItemRepository;
    @Autowired
    private WechatUserRepository wechatUserRepository;

    public WxPayService getWxPayService() {
        return wechatPayV3Config.getWxPayService();
    }

    private static final String NOTIFY_FUN = "/wechatpay/callback";

    @ResponseBody
    @PostMapping("/createOrder")
    public Result createOrder(@Valid @RequestBody PayParams params) {
        if (!paidPackagesRepository.existsById(params.getPaidId())) {
            return Result.fail("该套餐不存在");
        }
        PaidPackages paid = paidPackagesRepository.getById(params.getPaidId());
        int totalFee = paid.getSellingPrice().multiply(new BigDecimal(100)).intValue();
        if (totalFee <= 0) {
            return Result.fail("价格必须大于0");
        }
        totalFee = totalFee * params.getDeviceIds().size();
        String outTradeNo = UUID.randomUUID().toString().replace("-", "");
        String payQrCode = getPayQrcode(paid.getName().concat("x").concat(String.valueOf(params.getDeviceIds().size())), totalFee, outTradeNo);
        if (StringUtils.isEmpty(payQrCode)) {
            return Result.fail("生成支付二维码失败");
        } else {
            // 生成订单
            List<Device> deviceList = deviceRepository.findAllById(params.getDeviceIds());
            if (CollUtil.isEmpty(deviceList)) {
                return Result.fail("设备不存在");
            }
            WechatUser wechatUser = wechatUserRepository.findById(SecurityUtils.getLoginUser().getUser().getSiteId()).get();
            Order order = new Order();
            order.setAmount(paid.getSellingPrice().divide(BigDecimal.valueOf(100), RoundingMode.CEILING));
            order.setDescription(paid.getName());
            order.setShopId(deviceList.get(0).getShopId());
            order.setStatus(PayStatusEnum.PAY_WAIT.ordinal());
            order.setCreateTime(new Date());
            order.setUserId(SecurityUtils.getLoginUser().getUser().getSiteId());
            order.setAppid(wechatPayV3Config.getAppid());
            order.setMchid(wechatPayV3Config.getMerchantId());//商户号
            order.setDeviceId(deviceList.get(0).getId());
            order.setPackageId(paid.getId());
            order.setOutTradeNo(outTradeNo);//我们平台订单号
            order.setPayerClientIp("127.0.0.1");//ip
            order.setUnionid(wechatUser.getUnionid());
            order.setOpenid(wechatUser.getH5Openid());
            List<OrderItem> orderItems = new ArrayList<>(deviceList.size());
            for (Device device : deviceList) {
                //创建支付记录表
                OrderItem orderItem = new OrderItem();
                orderItem.setDeviceId(device.getId());//设备id
                orderItem.setPackageId(params.getPaidId());//流量包商品id
                orderItem.setFlowAmount(paid.getDataNum());
                orderItem.setStatus(DeviceFlowStateEnum.NEW.ordinal());
                orderItem.setOrderId(order.getId());//订单id
                orderItem.setCreateTime(new Date());//支付时间
                orderItems.add(orderItem);
            }
            orderItemRepository.saveAll(orderItems);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("qrcode", payQrCode);
        jsonObject.put("outTradeNo", outTradeNo);
        jsonObject.put("totalFee", totalFee);
        jsonObject.put("params", params);
        return Result.success(jsonObject);
    }

    @ResponseBody
    @GetMapping("/queryOrder/{outTradeNo}")
    public Result queryOrder(@Valid @PathVariable String outTradeNo) {
        List<Order> orders = orderRepository.findByOutTradeNoAndStatus(outTradeNo, 1);
        if (CollUtil.isNotEmpty(orders)) {
            return Result.success(true);
        }
        return Result.success(false);
    }

    @PostMapping("/callback")
    public Map<String, String> callback(@RequestBody WechatPayNotifyVo notifyData, HttpServletRequest request) {
        Map<String, String> response = new HashMap<>();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            log.info("支付回调 callback body == {}", objectMapper.writeValueAsString(notifyData));
            // 从请求头中获取必要的签名验证信息
            String serialNo = request.getHeader("Wechatpay-Serial");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String signature = request.getHeader("Wechatpay-Signature");
            SignatureHeader signatureHeader = new SignatureHeader();
            signatureHeader.setSerial(serialNo);
            signatureHeader.setNonce(nonce);
            signatureHeader.setTimeStamp(timestamp);
            signatureHeader.setSignature(signature);
            log.info("支付回调 WxPayOrderNotifyV3Result signatureHeader == {}", JSONObject.toJSONString(signatureHeader));
            // 解析支付结果通知
            WxPayOrderNotifyV3Result result = getWxPayService().parseOrderNotifyV3Result(objectMapper.writeValueAsString(notifyData), signatureHeader);
            log.info("支付回调 WxPayOrderNotifyV3Result result == {}", JSONObject.toJSONString(result));
            // 验证签名通过后，处理业务逻辑
            if (result != null && result.getResult() != null) {
                WxPayOrderNotifyV3Result.DecryptNotifyResult notifyResult = result.getResult();
                log.info("支付回调 WxPayOrderNotifyV3Result.DecryptNotifyResul  resource == {}", notifyResult);
                if (notifyResult != null && notifyResult.getTradeState().equals("SUCCESS")) {
                    OffsetDateTime offsetDateTime = OffsetDateTime.parse(notifyResult.getSuccessTime());
                    Date date = Date.from(offsetDateTime.atZoneSameInstant(ZoneId.systemDefault()).toInstant());
                    updateOrderStatus(notifyResult.getOutTradeNo(), notifyResult.getTransactionId(), date);
                    // 返回成功响应给微信支付平台
                    response.put("code", "SUCCESS");
                    response.put("message", "成功");
                    return response;
                }
            }
            response.put("code", "FAIL");
            response.put("message", "通知解析失败");
        } catch (Exception e) {
            response.put("code", "FAIL");
            response.put("message", "签名验证失败或其他异常: " + e.getMessage());
        }
        return response;
    }

    private String getPayQrcode(String body, int totalFee, String outTradeNo) {
        try {
            WxPayUnifiedOrderV3Request request = getWxPayUnifiedOrderV3Request(body, totalFee, outTradeNo);
            log.info("创建订单成功1：{}", JSONObject.toJSONString(request));
            String codeUrl = getWxPayService().createOrderV3(TradeTypeEnum.NATIVE, request);
            log.info("创建订单成功2：{}", codeUrl);
            return codeUrl;
        } catch (Exception e) {
            throw new CustomException("生成支付二维码失败：" + e.getMessage());
        }
    }

    private WxPayUnifiedOrderV3Request getWxPayUnifiedOrderV3Request(String body, int totalFee, String outTradeNo) {
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(wechatPayV3Config.getAppid());
        request.setMchid(wechatPayV3Config.getMerchantId());
        request.setDescription(body);
        request.setOutTradeNo(outTradeNo);
        request.setNotifyUrl(wechatPayV3Config.getNotifyPath().concat(NOTIFY_FUN));
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(totalFee);
        request.setAmount(amount);
        return request;
    }

    private void updateOrderStatus(String outTradeNo, String transactionId, Date successTime) {
        List<Order> orders = orderRepository.findByOutTradeNoAndStatus(outTradeNo, 2);
        orders.forEach(order -> {
            order.setTimeExpire(DateUtils.formatDateTime(successTime));
            order.setStatus(PayStatusEnum.PAY_SUCCESS.ordinal());
            order.setUpdateTime(new Date());
        });
        orderRepository.saveAll(orders);
    }

    private String generateNonceStr() {
        return "PY".concat(com.gl.util.DateUtils.datetimeToString(new Date(), "yyyyMMddHHmmssSSS")).concat(RandomUtil.randomNumbers(4));
    }
}

