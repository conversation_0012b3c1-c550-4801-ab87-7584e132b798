package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAiKey is a Querydsl query type for AiKey
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAiKey extends EntityPathBase<AiKey> {

    private static final long serialVersionUID = -564380922L;

    public static final QAiKey aiKey = new QAiKey("aiKey");

    public final StringPath agentKey = createString("agentKey");

    public final StringPath apiKey = createString("apiKey");

    public final StringPath appId = createString("appId");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath prompt = createString("prompt");

    public final StringPath promptId = createString("promptId");

    public QAiKey(String variable) {
        super(AiKey.class, forVariable(variable));
    }

    public QAiKey(Path<? extends AiKey> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAiKey(PathMetadata metadata) {
        super(AiKey.class, metadata);
    }

}

