<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.gl.service.commercial.service.CommercialServiceTest" time="2.867" tests="13" errors="10" skipped="0" failures="1">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\yny\4g\admin\admin-manager\target\test-classes;D:\yny\4g\admin\admin-manager\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.5.2\spring-boot-starter-web-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.5.2\spring-boot-starter-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.5.2\spring-boot-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.5.2\spring-boot-starter-logging-2.5.2.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.14.1\log4j-to-slf4j-2.14.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.14.1\log4j-api-2.14.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.31\jul-to-slf4j-1.7.31.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.28\snakeyaml-1.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.5.2\spring-boot-starter-json-2.5.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.12.3\jackson-datatype-jdk8-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.12.3\jackson-datatype-jsr310-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.12.3\jackson-module-parameter-names-2.12.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.5.2\spring-boot-starter-tomcat-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.48\tomcat-embed-core-9.0.48.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.48\tomcat-embed-websocket-9.0.48.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.8\spring-web-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.8\spring-beans-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.8\spring-webmvc-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.8\spring-context-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.8\spring-expression-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.5.2\spring-boot-starter-validation-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.48\tomcat-embed-el-9.0.48.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.0.Final\hibernate-validator-6.2.0.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.5.2\spring-boot-starter-data-jpa-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.5.2\spring-boot-starter-jdbc-2.5.2.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.8\spring-jdbc-5.3.8.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.5.2\spring-data-jpa-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.5.2\spring-data-commons-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.8\spring-orm-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.8\spring-tx-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.8\spring-aspects-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.5.2\spring-boot-starter-security-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.8\spring-aop-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.5.1\spring-security-config-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.5.1\spring-security-web-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\5.5.1\spring-security-test-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.5.1\spring-security-core-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.5.1\spring-security-crypto-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.8\spring-core-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.8\spring-jcl-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.8\spring-test-5.3.8.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.12.3\jackson-databind-2.12.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.5.2\spring-boot-starter-aop-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.5.2\spring-boot-starter-actuator-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.5.2\spring-boot-actuator-autoconfigure-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.5.2\spring-boot-actuator-2.5.2.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.7.1\micrometer-core-1.7.1.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\integration\spring-integration-mqtt\5.5.1\spring-integration-mqtt-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\integration\spring-integration-core\5.5.1\spring-integration-core-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.3.8\spring-messaging-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\retry\spring-retry\1.3.1\spring-retry-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\eclipse\paho\org.eclipse.paho.client.mqttv3\1.2.5\org.eclipse.paho.client.mqttv3-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-apt\4.4.0\querydsl-apt-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-codegen\4.4.0\querydsl-codegen-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\mysema\codegen\codegen\0.6.8\codegen-0.6.8.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jdt\core\compiler\ecj\4.3.1\ecj-4.3.1.jar;C:\Users\<USER>\.m2\repository\org\reflections\reflections\0.9.9\reflections-0.9.9.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\annotations\2.0.1\annotations-2.0.1.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-jpa\4.4.0\querydsl-jpa-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-core\4.4.0\querydsl-core-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\18.0\guava-18.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\1.3.9\jsr305-1.3.9.jar;C:\Users\<USER>\.m2\repository\com\mysema\commons\mysema-commons-lang\0.2.4\mysema-commons-lang-0.2.4.jar;C:\Users\<USER>\.m2\repository\com\infradna\tool\bridge-method-annotation\1.13\bridge-method-annotation-1.13.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.31\slf4j-api-1.7.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\2.5.2\spring-boot-configuration-processor-2.5.2.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid-spring-boot-starter\1.1.22\druid-spring-boot-starter-1.1.22.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.1.22\druid-1.1.22.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.5.2\spring-boot-autoconfigure-2.5.2.jar;C:\Users\<USER>\.m2\repository\redis\clients\jedis\3.6.1\jedis-3.6.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.9.0\commons-pool2-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.7\commons-io-2.7.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.73\fastjson-1.2.73.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-all\5.4.3\hutool-all-5.4.3.jar;C:\Users\<USER>\.m2\repository\cglib\cglib\3.3.0\cglib-3.3.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\7.1\asm-7.1.jar;C:\Users\<USER>\.m2\repository\eu\bitwalker\UserAgentUtils\1.21\UserAgentUtils-1.21.jar;C:\Users\<USER>\.m2\repository\com\github\penggle\kaptcha\2.3.2\kaptcha-2.3.2.jar;C:\Users\<USER>\.m2\repository\com\jhlabs\filters\2.0.235-1\filters-2.0.235-1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel\2.2.10\easyexcel-2.2.10.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\3.17\poi-3.17.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.1\commons-collections4-4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\3.17\poi-ooxml-3.17.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.04\curvesapi-1.04.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\3.17\poi-ooxml-schemas-3.17.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\2.6.0\xmlbeans-2.6.0.jar;C:\Users\<USER>\.m2\repository\org\ehcache\ehcache\3.9.4\ehcache-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\aliyun\oss\aliyun-sdk-oss\3.11.1\aliyun-sdk-oss-3.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6\jdom2-2.0.6.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;C:\Users\<USER>\.m2\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-core\4.5.10\aliyun-java-sdk-core-4.5.10.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;C:\Users\<USER>\.m2\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;C:\Users\<USER>\.m2\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.5.2\spring-boot-starter-test-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.5.2\spring-boot-test-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.5.2\spring-boot-test-autoconfigure-2.5.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.5.0\json-path-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.7\json-smart-2.4.7.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.7\accessors-smart-2.4.7.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.19.0\assertj-core-3.19.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.7.2\junit-jupiter-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.7.2\junit-jupiter-api-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.7.2\junit-platform-commons-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.7.2\junit-jupiter-params-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.7.2\junit-jupiter-engine-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.7.2\junit-platform-engine-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.9.0\mockito-core-3.9.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.9.0\mockito-junit-jupiter-3.9.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.8.2\xmlunit-core-2.8.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-inline\3.9.0\mockito-inline-3.9.0.jar;C:\Users\<USER>\.m2\repository\com\h2database\h2\1.4.200\h2-1.4.200.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;C:\Users\<USER>\.m2\repository\org\jaudiotagger\2.0.1\jaudiotagger-2.0.1.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-pay\4.5.0\weixin-java-pay-4.5.0.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-common\4.5.0\weixin-java-common-4.5.0.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\xstream\xstream\1.4.20\xstream-1.4.20.jar;C:\Users\<USER>\.m2\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;C:\Users\<USER>\.m2\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.13\httpmime-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\1.7.31\jcl-over-slf4j-1.7.31.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\qrcode-utils\1.1\qrcode-utils-1.1.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.2.1\core-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.68\bcpkix-jdk15on-1.68.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.68\bcprov-jdk15on-1.68.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.7\gson-2.8.7.jar;C:\Users\<USER>\.m2\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-mp\4.4.0\weixin-java-mp-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\googlecode\mp4parser\isoparser\1.1.22\isoparser-1.1.22.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\it\sauronsoftware\jave\1.0.2\jave-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\microsoft\cognitiveservices\speech\client-sdk\1.40.0\client-sdk-1.40.0.jar;C:\Users\<USER>\.m2\repository\com\huaweicloud\sdk\huaweicloud-sdk-sis\3.1.112\huaweicloud-sdk-sis-3.1.112.jar;C:\Users\<USER>\.m2\repository\com\huaweicloud\sdk\huaweicloud-sdk-core\3.1.112\huaweicloud-sdk-core-3.1.112.jar;C:\Users\<USER>\.m2\repository\org\openeuler\bgmprovider\1.1.2\bgmprovider-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\openeuler\jca\1.1.2\jca-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk18on\1.75\bcprov-jdk18on-1.75.jar;C:\Users\<USER>\.m2\repository\org\openeuler\jsse\1.1.2\jsse-1.1.2.jar;C:\Users\<USER>\.m2\repository\com\tencentcloudapi\tencentcloud-speech-sdk-java\1.0.48\tencentcloud-speech-sdk-java-1.0.48.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-core\5.8.11\hutool-core-5.8.11.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\3.14.9\okhttp-3.14.9.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\logging-interceptor\3.14.9\logging-interceptor-3.14.9.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.14\httpcore-nio-4.4.14.jar;C:\Users\<USER>\.m2\repository\net\jodah\expiringmap\0.5.8\expiringmap-0.5.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.12.3\jackson-annotations-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.12.3\jackson-core-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\alibaba\dashscope-sdk-java\2.18.0\dashscope-sdk-java-2.18.0.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava2\rxjava\2.2.21\rxjava-2.2.21.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-simple\1.7.31\slf4j-simple-1.7.31.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.5.20\kotlin-stdlib-jdk8-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.5.20\kotlin-stdlib-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.5.20\kotlin-stdlib-common-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.5.20\kotlin-stdlib-jdk7-1.5.20.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp-sse\3.14.9\okhttp-sse-3.14.9.jar;C:\Users\<USER>\.m2\repository\com\github\victools\jsonschema-generator\4.31.1\jsonschema-generator-4.31.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\broadscope-bailian-sdk-java\1.3.0\broadscope-bailian-sdk-java-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\bailian20230601\1.7.0\bailian20230601-1.7.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-util\0.2.21\tea-util-0.2.21.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-openapi\0.3.1\tea-openapi-0.3.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\credentials-java\0.3.0\credentials-java-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\2.3.0\jaxb-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.0\jaxb-impl-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\alibabacloud-gateway-spi\0.0.1\alibabacloud-gateway-spi-0.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-xml\0.1.5\tea-xml-0.1.5.jar;C:\Users\<USER>\.m2\repository\com\aliyun\openapiutil\0.2.1\openapiutil-0.2.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\endpoint-util\0.0.7\endpoint-util-0.0.7.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea\1.2.7\tea-1.2.7.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\23.0.0\annotations-23.0.0.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.7\reactor-core-3.4.7.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.25.Final\netty-all-4.1.25.Final.jar;C:\Users\<USER>\.m2\repository\com\gl\admin\admin-core\1.6.0\admin-core-1.6.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.5.2\spring-boot-starter-data-redis-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.5.2\spring-data-redis-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.5.2\spring-data-keyvalue-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.8\spring-oxm-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.8\spring-context-support-5.3.8.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-boot-starter\3.0.0\springfox-boot-starter-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-oas\3.0.0\springfox-oas-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.1.2\swagger-models-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-data-rest\3.0.0\springfox-data-rest-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-bean-validators\3.0.0\springfox-bean-validators-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\freemarker\freemarker\2.3.31\freemarker-2.3.31.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom\1.1.3\jdom-1.1.3.jar;C:\Users\<USER>\.m2\repository\com\gl\admin\admin-commons\1.6.0\admin-commons-1.6.0.jar;C:\Users\<USER>\.m2\repository\com\jamesmurty\utils\java-xmlbuilder\1.3\java-xmlbuilder-1.3.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nls\nls-sdk-tts\2.1.6\nls-sdk-tts-2.1.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nls\nls-sdk-common\2.1.6\nls-sdk-common-2.1.6.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.65.Final\netty-transport-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.65.Final\netty-resolver-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.65.Final\netty-handler-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.65.Final\netty-common-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.65.Final\netty-codec-http-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.65.Final\netty-codec-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.65.Final\netty-buffer-4.1.65.Final.jar;"/>
    <property name="java.vm.vendor" value="Azul Systems, Inc."/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://www.azul.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 11"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire279836178754694558\surefirebooter2199943391801114538.jar C:\Users\<USER>\AppData\Local\Temp\surefire279836178754694558 2025-07-30T12-38-28_824-jvmRun1 surefire3778453268012865186tmp surefire_06742227454499557040tmp"/>
    <property name="test" value="CommercialServiceTest"/>
    <property name="surefire.test.class.path" value="D:\yny\4g\admin\admin-manager\target\test-classes;D:\yny\4g\admin\admin-manager\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.5.2\spring-boot-starter-web-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.5.2\spring-boot-starter-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.5.2\spring-boot-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.5.2\spring-boot-starter-logging-2.5.2.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.14.1\log4j-to-slf4j-2.14.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.14.1\log4j-api-2.14.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.31\jul-to-slf4j-1.7.31.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.28\snakeyaml-1.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.5.2\spring-boot-starter-json-2.5.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.12.3\jackson-datatype-jdk8-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.12.3\jackson-datatype-jsr310-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.12.3\jackson-module-parameter-names-2.12.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.5.2\spring-boot-starter-tomcat-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.48\tomcat-embed-core-9.0.48.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.48\tomcat-embed-websocket-9.0.48.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.8\spring-web-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.8\spring-beans-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.8\spring-webmvc-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.8\spring-context-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.8\spring-expression-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.5.2\spring-boot-starter-validation-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.48\tomcat-embed-el-9.0.48.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.0.Final\hibernate-validator-6.2.0.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.5.2\spring-boot-starter-data-jpa-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.5.2\spring-boot-starter-jdbc-2.5.2.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.8\spring-jdbc-5.3.8.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.5.2\spring-data-jpa-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.5.2\spring-data-commons-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.8\spring-orm-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.8\spring-tx-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.8\spring-aspects-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.5.2\spring-boot-starter-security-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.8\spring-aop-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.5.1\spring-security-config-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.5.1\spring-security-web-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\5.5.1\spring-security-test-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.5.1\spring-security-core-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.5.1\spring-security-crypto-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.8\spring-core-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.8\spring-jcl-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.8\spring-test-5.3.8.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.12.3\jackson-databind-2.12.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.5.2\spring-boot-starter-aop-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.5.2\spring-boot-starter-actuator-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.5.2\spring-boot-actuator-autoconfigure-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.5.2\spring-boot-actuator-2.5.2.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.7.1\micrometer-core-1.7.1.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\integration\spring-integration-mqtt\5.5.1\spring-integration-mqtt-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\integration\spring-integration-core\5.5.1\spring-integration-core-5.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.3.8\spring-messaging-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\retry\spring-retry\1.3.1\spring-retry-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\eclipse\paho\org.eclipse.paho.client.mqttv3\1.2.5\org.eclipse.paho.client.mqttv3-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-apt\4.4.0\querydsl-apt-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-codegen\4.4.0\querydsl-codegen-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\mysema\codegen\codegen\0.6.8\codegen-0.6.8.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jdt\core\compiler\ecj\4.3.1\ecj-4.3.1.jar;C:\Users\<USER>\.m2\repository\org\reflections\reflections\0.9.9\reflections-0.9.9.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\annotations\2.0.1\annotations-2.0.1.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-jpa\4.4.0\querydsl-jpa-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\querydsl\querydsl-core\4.4.0\querydsl-core-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\18.0\guava-18.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\1.3.9\jsr305-1.3.9.jar;C:\Users\<USER>\.m2\repository\com\mysema\commons\mysema-commons-lang\0.2.4\mysema-commons-lang-0.2.4.jar;C:\Users\<USER>\.m2\repository\com\infradna\tool\bridge-method-annotation\1.13\bridge-method-annotation-1.13.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.31\slf4j-api-1.7.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\2.5.2\spring-boot-configuration-processor-2.5.2.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid-spring-boot-starter\1.1.22\druid-spring-boot-starter-1.1.22.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.1.22\druid-1.1.22.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.5.2\spring-boot-autoconfigure-2.5.2.jar;C:\Users\<USER>\.m2\repository\redis\clients\jedis\3.6.1\jedis-3.6.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.9.0\commons-pool2-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.7\commons-io-2.7.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.73\fastjson-1.2.73.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-all\5.4.3\hutool-all-5.4.3.jar;C:\Users\<USER>\.m2\repository\cglib\cglib\3.3.0\cglib-3.3.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\7.1\asm-7.1.jar;C:\Users\<USER>\.m2\repository\eu\bitwalker\UserAgentUtils\1.21\UserAgentUtils-1.21.jar;C:\Users\<USER>\.m2\repository\com\github\penggle\kaptcha\2.3.2\kaptcha-2.3.2.jar;C:\Users\<USER>\.m2\repository\com\jhlabs\filters\2.0.235-1\filters-2.0.235-1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel\2.2.10\easyexcel-2.2.10.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\3.17\poi-3.17.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.1\commons-collections4-4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\3.17\poi-ooxml-3.17.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.04\curvesapi-1.04.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\3.17\poi-ooxml-schemas-3.17.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\2.6.0\xmlbeans-2.6.0.jar;C:\Users\<USER>\.m2\repository\org\ehcache\ehcache\3.9.4\ehcache-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\aliyun\oss\aliyun-sdk-oss\3.11.1\aliyun-sdk-oss-3.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6\jdom2-2.0.6.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;C:\Users\<USER>\.m2\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-core\4.5.10\aliyun-java-sdk-core-4.5.10.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;C:\Users\<USER>\.m2\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;C:\Users\<USER>\.m2\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.5.2\spring-boot-starter-test-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.5.2\spring-boot-test-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.5.2\spring-boot-test-autoconfigure-2.5.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.5.0\json-path-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.7\json-smart-2.4.7.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.7\accessors-smart-2.4.7.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.19.0\assertj-core-3.19.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.7.2\junit-jupiter-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.7.2\junit-jupiter-api-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.7.2\junit-platform-commons-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.7.2\junit-jupiter-params-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.7.2\junit-jupiter-engine-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.7.2\junit-platform-engine-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.9.0\mockito-core-3.9.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.9.0\mockito-junit-jupiter-3.9.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.8.2\xmlunit-core-2.8.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-inline\3.9.0\mockito-inline-3.9.0.jar;C:\Users\<USER>\.m2\repository\com\h2database\h2\1.4.200\h2-1.4.200.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;C:\Users\<USER>\.m2\repository\org\jaudiotagger\2.0.1\jaudiotagger-2.0.1.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-pay\4.5.0\weixin-java-pay-4.5.0.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-common\4.5.0\weixin-java-common-4.5.0.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\xstream\xstream\1.4.20\xstream-1.4.20.jar;C:\Users\<USER>\.m2\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;C:\Users\<USER>\.m2\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.13\httpmime-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\1.7.31\jcl-over-slf4j-1.7.31.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\qrcode-utils\1.1\qrcode-utils-1.1.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.2.1\core-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.68\bcpkix-jdk15on-1.68.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.68\bcprov-jdk15on-1.68.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.7\gson-2.8.7.jar;C:\Users\<USER>\.m2\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-mp\4.4.0\weixin-java-mp-4.4.0.jar;C:\Users\<USER>\.m2\repository\com\googlecode\mp4parser\isoparser\1.1.22\isoparser-1.1.22.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\it\sauronsoftware\jave\1.0.2\jave-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\microsoft\cognitiveservices\speech\client-sdk\1.40.0\client-sdk-1.40.0.jar;C:\Users\<USER>\.m2\repository\com\huaweicloud\sdk\huaweicloud-sdk-sis\3.1.112\huaweicloud-sdk-sis-3.1.112.jar;C:\Users\<USER>\.m2\repository\com\huaweicloud\sdk\huaweicloud-sdk-core\3.1.112\huaweicloud-sdk-core-3.1.112.jar;C:\Users\<USER>\.m2\repository\org\openeuler\bgmprovider\1.1.2\bgmprovider-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\openeuler\jca\1.1.2\jca-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk18on\1.75\bcprov-jdk18on-1.75.jar;C:\Users\<USER>\.m2\repository\org\openeuler\jsse\1.1.2\jsse-1.1.2.jar;C:\Users\<USER>\.m2\repository\com\tencentcloudapi\tencentcloud-speech-sdk-java\1.0.48\tencentcloud-speech-sdk-java-1.0.48.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-core\5.8.11\hutool-core-5.8.11.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\3.14.9\okhttp-3.14.9.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\logging-interceptor\3.14.9\logging-interceptor-3.14.9.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.14\httpcore-nio-4.4.14.jar;C:\Users\<USER>\.m2\repository\net\jodah\expiringmap\0.5.8\expiringmap-0.5.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.12.3\jackson-annotations-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.12.3\jackson-core-2.12.3.jar;C:\Users\<USER>\.m2\repository\com\alibaba\dashscope-sdk-java\2.18.0\dashscope-sdk-java-2.18.0.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava2\rxjava\2.2.21\rxjava-2.2.21.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-simple\1.7.31\slf4j-simple-1.7.31.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.5.20\kotlin-stdlib-jdk8-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.5.20\kotlin-stdlib-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.5.20\kotlin-stdlib-common-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.5.20\kotlin-stdlib-jdk7-1.5.20.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp-sse\3.14.9\okhttp-sse-3.14.9.jar;C:\Users\<USER>\.m2\repository\com\github\victools\jsonschema-generator\4.31.1\jsonschema-generator-4.31.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\broadscope-bailian-sdk-java\1.3.0\broadscope-bailian-sdk-java-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\bailian20230601\1.7.0\bailian20230601-1.7.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-util\0.2.21\tea-util-0.2.21.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-openapi\0.3.1\tea-openapi-0.3.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\credentials-java\0.3.0\credentials-java-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\2.3.0\jaxb-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.0\jaxb-impl-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\alibabacloud-gateway-spi\0.0.1\alibabacloud-gateway-spi-0.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-xml\0.1.5\tea-xml-0.1.5.jar;C:\Users\<USER>\.m2\repository\com\aliyun\openapiutil\0.2.1\openapiutil-0.2.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\endpoint-util\0.0.7\endpoint-util-0.0.7.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea\1.2.7\tea-1.2.7.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\23.0.0\annotations-23.0.0.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.7\reactor-core-3.4.7.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.25.Final\netty-all-4.1.25.Final.jar;C:\Users\<USER>\.m2\repository\com\gl\admin\admin-core\1.6.0\admin-core-1.6.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.5.2\spring-boot-starter-data-redis-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.5.2\spring-data-redis-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.5.2\spring-data-keyvalue-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.8\spring-oxm-5.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.8\spring-context-support-5.3.8.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-boot-starter\3.0.0\springfox-boot-starter-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-oas\3.0.0\springfox-oas-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.1.2\swagger-models-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-data-rest\3.0.0\springfox-data-rest-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-bean-validators\3.0.0\springfox-bean-validators-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\freemarker\freemarker\2.3.31\freemarker-2.3.31.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom\1.1.3\jdom-1.1.3.jar;C:\Users\<USER>\.m2\repository\com\gl\admin\admin-commons\1.6.0\admin-commons-1.6.0.jar;C:\Users\<USER>\.m2\repository\com\jamesmurty\utils\java-xmlbuilder\1.3\java-xmlbuilder-1.3.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nls\nls-sdk-tts\2.1.6\nls-sdk-tts-2.1.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nls\nls-sdk-common\2.1.6\nls-sdk-common-2.1.6.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.65.Final\netty-transport-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.65.Final\netty-resolver-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.65.Final\netty-handler-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.65.Final\netty-common-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.65.Final\netty-codec-http-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.65.Final\netty-codec-4.1.65.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.65.Final\netty-buffer-4.1.65.Final.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Users\Administrator\.jdks\azul-1.8.0_382\jre"/>
    <property name="basedir" value="D:\yny\4g\admin\admin-manager"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="jdk.vendor.version" value="Zulu 8.72.0.17-CA-win64"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire279836178754694558\surefirebooter2199943391801114538.jar"/>
    <property name="sun.boot.class.path" value="C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\resources.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\rt.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\sunrsasign.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\jsse.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\jce.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\charsets.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\jfr.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\cat.jar;C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_382-b05"/>
    <property name="user.name" value="Administrator"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="http://www.azul.com/support/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_382"/>
    <property name="user.dir" value="D:\yny\4g\admin\admin-manager"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\ShadowBot;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\虚拟机\bin\;\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\新建文件夹\EasyShare\x86\;D:\新建文件夹\EasyShare\x64\;C:\Users\<USER>\.jdks\azul-1.8.0_382\bin;D:\ffmpeg-6.1.1\bin;C:\Python27;C:\WINDOWS\system32;D:\微信web开发者工具\dll;C:\Users\<USER>\AppData\Local\Programs\python\Python312;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\IntelliJ IDEA 2022.3.3\bin;C:\Windows\System32;D:\maven\apache-maven-3.6.3\bin;D:\adb\adb;D:\Downloads\apache-tomcat-8.5.97\apache-tomcat-8.5.97\bin;D:\PyCharm 2022.2.5\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;d:\Downloads\zh-fiddler-master\zh-fiddler-master;C:\Users\<USER>\.jdks\azul-1.8.0_382\bin;D:\ffmpeg-6.1.1\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\App;C:\Users\<USER>\AppData\Roaming\python\Python312\Scripts;%NVM_HOME%;%NVM_SYMLINK%;D:\ShadowBot;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\IntelliJ IDEA 2022.3.3\bin;D:\Microsoft VS Code\bin;C:\Windows\System32;D:\maven\apache-maven-3.6.3\bin;D:\adb\adb;D:\Downloads\apache-tomcat-8.5.97\apache-tomcat-8.5.97\bin;D:\PyCharm 2022.2.5\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;d:\Downloads\zh-fiddler-master\zh-fiddler-master;C:\Users\<USER>\.jdks\azul-1.8.0_382\bin;D:\ffmpeg-6.1.1\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\python\Python312;D:\anaconda3;D:\anaconda3\Library\bin;D:\anaconda3\Library\usr\bin;D:\anaconda3\Scripts;C:\Users\<USER>\AppData\Roaming\python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Azul Systems, Inc."/>
    <property name="java.vm.version" value="25.382-b05"/>
    <property name="java.specification.maintenance.version" value="5"/>
    <property name="java.ext.dirs" value="C:\Users\<USER>\.jdks\azul-1.8.0_382\jre\lib\ext;C:\WINDOWS\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testList_QueryResultNull_ShouldReturnEmptyResult" classname="com.gl.service.commercial.service.CommercialServiceTest" time="1.594">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultNull_ShouldReturnEmptyResult(CommercialServiceTest.java:192)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultNull_ShouldReturnEmptyResult(CommercialServiceTest.java:192)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultNull_ShouldReturnEmptyResult(CommercialServiceTest.java:196)
]]></error>
    <system-out><![CDATA[12:38:32.244 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
    <system-err><![CDATA[SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.31/slf4j-simple-1.7.31.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [ch.qos.logback.classic.util.ContextSelectorStaticBinder]
]]></system-err>
  </testcase>
  <testcase name="testExportList_DataWithNullFields_ShouldHandleNullValues" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.051">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testExportList_DataWithNullFields_ShouldHandleNullValues(CommercialServiceTest.java:443)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_DataWithNullFields_ShouldHandleNullValues(CommercialServiceTest.java:443)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_DataWithNullFields_ShouldHandleNullValues(CommercialServiceTest.java:450)
]]></error>
    <system-out><![CDATA[12:38:32.317 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testList_NullDto_ShouldQueryWithoutFilters" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.003">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testList_NullDto_ShouldQueryWithoutFilters(CommercialServiceTest.java:283)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem">org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_NullDto_ShouldQueryWithoutFilters(CommercialServiceTest.java:283)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_NullDto_ShouldQueryWithoutFilters(CommercialServiceTest.java:289)
</error>
    <system-out><![CDATA[12:38:32.328 [main] INFO com.gl.service.commercial.service.CommercialService - args = [1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testList_ExportType_ShouldReturnAllResults" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.007">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testList_ExportType_ShouldReturnAllResults(CommercialServiceTest.java:221)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_ExportType_ShouldReturnAllResults(CommercialServiceTest.java:221)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_ExportType_ShouldReturnAllResults(CommercialServiceTest.java:227)
]]></error>
    <system-out><![CDATA[12:38:32.344 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testExportList_IOExceptionThrown_ShouldPropagateException" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.032">
    <failure message="应该抛出IOException ==&gt; Unexpected exception type thrown ==&gt; expected: &lt;java.io.IOException&gt; but was: &lt;org.mockito.exceptions.misusing.PotentialStubbingProblem&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: 应该抛出IOException ==> Unexpected exception type thrown ==> expected: <java.io.IOException> but was: <org.mockito.exceptions.misusing.PotentialStubbingProblem>
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_IOExceptionThrown_ShouldPropagateException(CommercialServiceTest.java:478)
Caused by: org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_IOExceptionThrown_ShouldPropagateException(CommercialServiceTest.java:471)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.lambda$testExportList_IOExceptionThrown_ShouldPropagateException$0(CommercialServiceTest.java:479)
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_IOExceptionThrown_ShouldPropagateException(CommercialServiceTest.java:478)
]]></failure>
    <system-out><![CDATA[12:38:32.374 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testList_WxUserNeedFilterWithNoShops_ShouldReturnEmptyResult" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.018"/>
  <testcase name="testExportList_NormalExport_ShouldGenerateExcelFile" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.008">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testExportList_NormalExport_ShouldGenerateExcelFile(CommercialServiceTest.java:354)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_NormalExport_ShouldGenerateExcelFile(CommercialServiceTest.java:354)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_NormalExport_ShouldGenerateExcelFile(CommercialServiceTest.java:361)
]]></error>
    <system-out><![CDATA[12:38:32.425 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.008">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile(CommercialServiceTest.java:382)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile(CommercialServiceTest.java:382)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile(CommercialServiceTest.java:387)
]]></error>
    <system-out><![CDATA[12:38:32.430 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testList_VariousQueryConditions_ShouldBuildCorrectSql" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.008">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    100L,&#10;    &quot;%搜索条件%&quot;,&#10;    &quot;%搜索条件%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testList_VariousQueryConditions_ShouldBuildCorrectSql(CommercialServiceTest.java:323)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    100L,
    "%搜索条件%",
    "%搜索条件%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_VariousQueryConditions_ShouldBuildCorrectSql(CommercialServiceTest.java:323)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_VariousQueryConditions_ShouldBuildCorrectSql(CommercialServiceTest.java:329)
]]></error>
    <system-out><![CDATA[12:38:32.440 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 100, %搜索条件%, %搜索条件%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testList_EmptyShopRefList_ShouldQueryWithoutShopFilter" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.012">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ? GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testList_EmptyShopRefList_ShouldQueryWithoutShopFilter(CommercialServiceTest.java:252)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ? GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59"
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_EmptyShopRefList_ShouldQueryWithoutShopFilter(CommercialServiceTest.java:252)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_EmptyShopRefList_ShouldQueryWithoutShopFilter(CommercialServiceTest.java:258)
]]></error>
    <system-out><![CDATA[12:38:32.448 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59]
]]></system-out>
  </testcase>
  <testcase name="testList_QueryResultEmpty_ShouldReturnEmptyResult" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.053">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultEmpty_ShouldReturnEmptyResult(CommercialServiceTest.java:163)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultEmpty_ShouldReturnEmptyResult(CommercialServiceTest.java:163)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_QueryResultEmpty_ShouldReturnEmptyResult(CommercialServiceTest.java:167)
]]></error>
    <system-out><![CDATA[12:38:32.458 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
  <testcase name="testExportList_WxUserFilterExport_ShouldGenerateEmptyExcelFile" classname="com.gl.service.commercial.service.CommercialServiceTest" time="1.056"/>
  <testcase name="testList_NormalQuery_ShouldReturnSuccessResult" classname="com.gl.service.commercial.service.CommercialServiceTest" time="0.005">
    <error message="&#10;Strict stubbing argument mismatch. Please check:&#10; - this invocation of &apos;queryForObject&apos; method:&#10;    jdbcTemplate.queryForObject(&#10;    &quot;select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time &gt;= ?  AND wu.auth_time &lt;= ?  AND s.id in (?, ?, ?)&#10;GROUP BY wu.id ) t&quot;,&#10;    class java.lang.Long,&#10;    &quot;%测试店铺%&quot;,&#10;    1L,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;%测试用户%&quot;,&#10;    &quot;2024-01-01 00:00:00&quot;,&#10;    &quot;2024-12-31 23:59:59&quot;,&#10;    1L,&#10;    2L,&#10;    3L&#10;);&#10;    -&gt; at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)&#10; - has following stubbing(s) with different arguments:&#10;    1. jdbcTemplate.queryForObject(&quot;&quot;, null, null);&#10;      -&gt; at com.gl.service.commercial.service.CommercialServiceTest.testList_NormalQuery_ShouldReturnSuccessResult(CommercialServiceTest.java:106)&#10;Typically, stubbing argument mismatch indicates user mistake when writing tests.&#10;Mockito fails early so that you can debug potential problem easily.&#10;However, there are legit scenarios when this exception generates false negative signal:&#10;  - stubbing the same method multiple times using &apos;given().will()&apos; or &apos;when().then()&apos; API&#10;    Please use &apos;will().given()&apos; or &apos;doReturn().when()&apos; API for stubbing.&#10;  - stubbed method is intentionally invoked with different arguments by code under test&#10;    Please use default or &apos;silent&apos; JUnit Rule (equivalent of Strictness.LENIENT).&#10;For more information see javadoc for PotentialStubbingProblem class." type="org.mockito.exceptions.misusing.PotentialStubbingProblem"><![CDATA[org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL AND s.shop_name like ?  AND s.id = ?  AND wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
    -> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:89)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.commercial.service.CommercialServiceTest.testList_NormalQuery_ShouldReturnSuccessResult(CommercialServiceTest.java:106)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.commercial.service.CommercialServiceTest.testList_NormalQuery_ShouldReturnSuccessResult(CommercialServiceTest.java:112)
]]></error>
    <system-out><![CDATA[12:38:33.568 [main] INFO com.gl.service.commercial.service.CommercialService - args = [%测试店铺%, 1, %测试用户%, %测试用户%, 2024-01-01 00:00:00, 2024-12-31 23:59:59, 1, 2, 3]
]]></system-out>
  </testcase>
</testsuite>