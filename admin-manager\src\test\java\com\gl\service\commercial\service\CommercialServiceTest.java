package com.gl.service.commercial.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.web.response.Result;
import com.gl.service.commercial.vo.CommercialVo;
import com.gl.service.commercial.vo.ExcelCommercial;
import com.gl.service.commercial.vo.dto.CommercialDto;
import com.gl.util.GetShopRefUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CommercialService单元测试类
 * 测试商户管理服务的所有业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("商户管理服务测试")
class CommercialServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private GetShopRefUtil shopRefUtil;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ServletOutputStream outputStream;

    @InjectMocks
    private CommercialService commercialService;

    private CommercialDto testDto;
    private List<CommercialVo> testCommercialVos;
    private List<Long> testShopRefs;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testDto = new CommercialDto();
        testDto.setPageNumber(0);
        testDto.setPageSize(10);
        testDto.setShopName("测试店铺");
        testDto.setShopId(1L);
        testDto.setSearchCondition("测试用户");
        testDto.setBeginTime("2024-01-01 00:00:00");
        testDto.setEndTime("2024-12-31 23:59:59");

        // 初始化商户数据
        testCommercialVos = new ArrayList<>();
        CommercialVo vo1 = new CommercialVo();
        vo1.setId(1L);
        vo1.setNickname("测试用户1");
        vo1.setPhone("13800138001");
        vo1.setGender(1);
        vo1.setAuthTime(new Date());
        vo1.setShopNames("测试店铺1");
        vo1.setDeviceNames("设备1");
        vo1.setDeviceNum(2);
        testCommercialVos.add(vo1);

        CommercialVo vo2 = new CommercialVo();
        vo2.setId(2L);
        vo2.setNickname("测试用户2");
        vo2.setPhone("13800138002");
        vo2.setGender(2);
        vo2.setAuthTime(new Date());
        vo2.setShopNames("测试店铺2");
        vo2.setDeviceNames("设备2");
        vo2.setDeviceNum(1);
        testCommercialVos.add(vo2);

        // 初始化店铺关联数据
        testShopRefs = Arrays.asList(1L, 2L, 3L);
    }

    @Test
    @DisplayName("测试list方法 - 正常查询场景")
    void testList_NormalQuery_ShouldReturnSuccessResult() {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(2L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testCommercialVos);

        // When - 执行测试方法
        Result result = commercialService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");
        assertEquals("success", result.getMessage(), "返回消息应为success");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(testCommercialVos, data.get("result"), "返回的商户列表应匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 微信用户需要过滤且无店铺关联")
    void testList_WxUserNeedFilterWithNoShops_ShouldReturnEmptyResult() {
        // Given - 微信用户需要过滤且无店铺关联
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When - 执行测试方法
        Result result = commercialService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(0, data.get("total"), "总数应为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "返回的商户列表应为空");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(jdbcTemplate, never()).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 查询结果为空")
    void testList_QueryResultEmpty_ShouldReturnEmptyResult() {
        // Given - 查询结果为空
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(0L);

        // When - 执行测试方法
        Result result = commercialService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(0, data.get("total"), "总数应为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "返回的商户列表应为空");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 查询结果为null")
    void testList_QueryResultNull_ShouldReturnEmptyResult() {
        // Given - 查询结果为null
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(null);

        // When - 执行测试方法
        Result result = commercialService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(0, data.get("total"), "总数应为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "返回的商户列表应为空");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 导出类型查询（不分页）")
    void testList_ExportType_ShouldReturnAllResults() {
        // Given - 导出类型查询
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(2L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testCommercialVos);

        // When - 执行测试方法（exportType = 2表示导出）
        Result result = commercialService.list(testDto, 2);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(testCommercialVos, data.get("result"), "返回的商户列表应匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 空店铺关联列表")
    void testList_EmptyShopRefList_ShouldQueryWithoutShopFilter() {
        // Given - 空店铺关联列表
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Collections.emptyList());
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(2L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testCommercialVos);

        // When - 执行测试方法
        Result result = commercialService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(testCommercialVos, data.get("result"), "返回的商户列表应匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 空DTO参数")
    void testList_NullDto_ShouldQueryWithoutFilters() {
        // Given - 空DTO参数
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(2L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testCommercialVos);

        // When - 执行测试方法
        Result result = commercialService.list(null, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(testCommercialVos, data.get("result"), "返回的商户列表应匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 各种查询条件组合")
    void testList_VariousQueryConditions_ShouldBuildCorrectSql() {
        // Given - 设置各种查询条件
        CommercialDto dto = new CommercialDto();
        dto.setPageNumber(1);
        dto.setPageSize(20);
        dto.setShopName("测试店铺");
        dto.setShopId(100L);
        dto.setSearchCondition("搜索条件");
        dto.setBeginTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testCommercialVos.subList(0, 1));

        // When - 执行测试方法
        Result result = commercialService.list(dto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "返回数据不应为空");
        assertEquals(1L, data.get("total"), "总数应为1");
        assertEquals(1, ((List<?>) data.get("result")).size(), "返回的商户列表大小应为1");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试exportList方法 - 正常导出场景")
    void testExportList_NormalExport_ShouldGenerateExcelFile() throws IOException {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(2L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testCommercialVos);
        when(response.getOutputStream()).thenReturn(outputStream);

        // When - 执行测试方法
        commercialService.exportList(testDto, response);

        // Then - 验证响应设置
        verify(response).setContentType("application/vnd.ms-excel");
        verify(response).setCharacterEncoding("utf-8");
        verify(response).setHeader(eq("Content-disposition"), anyString());
        verify(response).getOutputStream();

        // 验证业务方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试exportList方法 - 空数据导出")
    void testExportList_EmptyDataExport_ShouldGenerateEmptyExcelFile() throws IOException {
        // Given - 空数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(0L);
        when(response.getOutputStream()).thenReturn(outputStream);

        // When - 执行测试方法
        commercialService.exportList(testDto, response);

        // Then - 验证响应设置
        verify(response).setContentType("application/vnd.ms-excel");
        verify(response).setCharacterEncoding("utf-8");
        verify(response).setHeader(eq("Content-disposition"), anyString());
        verify(response).getOutputStream();

        // 验证业务方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试exportList方法 - 微信用户过滤导出")
    void testExportList_WxUserFilterExport_ShouldGenerateEmptyExcelFile() throws IOException {
        // Given - 微信用户需要过滤
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);
        when(response.getOutputStream()).thenReturn(outputStream);

        // When - 执行测试方法
        commercialService.exportList(testDto, response);

        // Then - 验证响应设置
        verify(response).setContentType("application/vnd.ms-excel");
        verify(response).setCharacterEncoding("utf-8");
        verify(response).setHeader(eq("Content-disposition"), anyString());
        verify(response).getOutputStream();

        // 验证业务方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil, never()).getShopRef();
        verify(jdbcTemplate, never()).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试exportList方法 - 包含空值字段的数据导出")
    void testExportList_DataWithNullFields_ShouldHandleNullValues() throws IOException {
        // Given - 包含空值的测试数据
        List<CommercialVo> vosWithNulls = new ArrayList<>();
        CommercialVo voWithNulls = new CommercialVo();
        voWithNulls.setId(1L);
        voWithNulls.setNickname(null); // 空昵称
        voWithNulls.setPhone(null); // 空手机号
        voWithNulls.setGender(1);
        voWithNulls.setAuthTime(new Date());
        voWithNulls.setShopNames(null); // 空店铺名
        voWithNulls.setDeviceNames(null); // 空设备名
        voWithNulls.setDeviceNum(0);
        vosWithNulls.add(voWithNulls);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(vosWithNulls);
        when(response.getOutputStream()).thenReturn(outputStream);

        // When - 执行测试方法
        commercialService.exportList(testDto, response);

        // Then - 验证响应设置
        verify(response).setContentType("application/vnd.ms-excel");
        verify(response).setCharacterEncoding("utf-8");
        verify(response).setHeader(eq("Content-disposition"), anyString());
        verify(response).getOutputStream();

        // 验证业务方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试exportList方法 - IO异常处理")
    void testExportList_IOExceptionThrown_ShouldPropagateException() throws IOException {
        // Given - 模拟IO异常
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(testShopRefs);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(2L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(testCommercialVos);
        when(response.getOutputStream()).thenThrow(new IOException("模拟IO异常"));

        // When & Then - 执行测试方法并验证异常
        assertThrows(IOException.class, () -> {
            commercialService.exportList(testDto, response);
        }, "应该抛出IOException");

        // 验证响应设置
        verify(response).setContentType("application/vnd.ms-excel");
        verify(response).setCharacterEncoding("utf-8");
        verify(response).setHeader(eq("Content-disposition"), anyString());
        verify(response).getOutputStream();
    }
}
