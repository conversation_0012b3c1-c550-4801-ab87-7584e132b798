package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysPositionUser is a Querydsl query type for SysPositionUser
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysPositionUser extends EntityPathBase<SysPositionUser> {

    private static final long serialVersionUID = 1216883193L;

    public static final QSysPositionUser sysPositionUser = new QSysPositionUser("sysPositionUser");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Long> positionId = createNumber("positionId", Long.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QSysPositionUser(String variable) {
        super(SysPositionUser.class, forVariable(variable));
    }

    public QSysPositionUser(Path<? extends SysPositionUser> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysPositionUser(PathMetadata metadata) {
        super(SysPositionUser.class, metadata);
    }

}

