package com.gl.wechat.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 微信用户表
 * @author: duanjinze
 * @date: 2022/11/10 16:16
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_wechat_user")
public class WechatUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * openid
     */
    @Basic
    @Column(name = "openid")
    private String openid;

    /**
     * 昵称
     */
    @Basic
    @Column(name = "nickname")
    private String nickname;
    /**
     * 头像
     */
    @Basic
    @Column(name = "avatar")
    private String avatar;

    /**
     * 0未知 1男 2女
     */
    @Basic
    @Column(name = "gender",columnDefinition = "smallint")
    private Integer gender;

    /**
     * 手机
     */
    @Basic
    @Column(name = "phone")
    private String phone;

    /**
     * 地区
     */
    @Basic
    @Column(name = "area")
    private String area;

    /**
     * 授权时间
     */
    @Basic
    @Column(name = "auth_time")
    private Date authTime;

    @Basic
    @Column(name = "h5_openid")
    private String h5Openid;

    /**
     * H5授权时间
     */
    @Basic
    @Column(name = "h5_auth_time")
    private Date h5AuthTime;

    @Basic
    @Column(name = "unionid")
    private String unionid;

    /**
     * 昵称名称
     */
    @Transient
    private String nicknames;



}
