package com.gl.service.basis.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBaseAbout is a Querydsl query type for BaseAbout
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBaseAbout extends EntityPathBase<BaseAbout> {

    private static final long serialVersionUID = -1873810730L;

    public static final QBaseAbout baseAbout = new QBaseAbout("baseAbout");

    public final StringPath content = createString("content");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public QBaseAbout(String variable) {
        super(BaseAbout.class, forVariable(variable));
    }

    public QBaseAbout(Path<? extends BaseAbout> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBaseAbout(PathMetadata metadata) {
        super(BaseAbout.class, metadata);
    }

}

