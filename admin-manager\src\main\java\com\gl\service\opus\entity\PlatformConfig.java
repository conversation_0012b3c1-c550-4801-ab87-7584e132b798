package com.gl.service.opus.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 平台配置表
 * 
 * @author: system
 * @date: 2024/01/01 00:00
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "platform_config")
public class PlatformConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台类型(ALI_DUBBING,AZURE_DUBBING等)
     */
    @Basic
    @Column(name = "platform_type", length = 50, nullable = false)
    private String platformType;

    /**
     * 访问令牌
     */
    @Basic
    @Column(name = "access_token", length = 500)
    private String accessToken;

    /**
     * 应用ID
     */
    @Basic
    @Column(name = "app_id", length = 100)
    private String appId;

    /**
     * 访问密钥
     */
    @Basic
    @Column(name = "access_key", length = 500)
    private String accessKey;

    /**
     * 秘密密钥
     */
    @Basic
    @Column(name = "secret_key", length = 500)
    private String secretKey;

    /**
     * 应用密钥
     */
    @Basic
    @Column(name = "app_key", length = 500)
    private String appKey;

    /**
     * 状态(1:启用 0:禁用)
     */
    @Basic
    @Column(name = "status", columnDefinition = "tinyint")
    private Integer status = 1;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @Basic
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}