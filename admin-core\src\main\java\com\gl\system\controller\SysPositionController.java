package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.interceptor.annotation.RepeatSubmit;
import com.gl.system.service.SysDeptService;
import com.gl.system.service.SysPositionService;
import com.gl.system.vo.SysDeptVo;
import com.gl.system.vo.SysPositionVo;
import com.gl.framework.web.domain.PageData;
import com.gl.framework.web.response.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 岗位管理控制器
 */
@RestController
@RequestMapping("/system/position")
public class SysPositionController {

    @Autowired
    private SysPositionService postService;
    @Autowired
    private SysDeptService deptService;

    @Autowired
    public void setSysPostService(SysPositionService postService) {
        this.postService = postService;
    }

    /**
     * 分页列表
     *
     * @param filter
     * @return
     */
    @GetMapping("/list")
    @PreAuthorize("@ps.hasPermi('system:post:list')")
    public PageData<SysPositionVo> list(SysPositionVo filter) {
        return postService.list(filter);
    }

    /**
     * 获取详细信息
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/{id}")
    @PreAuthorize("@ps.hasPermi('system:post:query')")
    public SysPositionVo get(@PathVariable Long id) {
        return postService.selectPostById(id);
    }

    /**
     * 新增
     *
     * @param vo
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @PreAuthorize("@ps.hasPermi('system:post:add')")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT, businessTypeName = "新增")
    public Result add(@Validated @RequestBody SysPositionVo vo) {
        if (postService.checkPostNameUnique(vo)) {
            return Result.fail("新增岗位'" + vo.getName() + "'失败，岗位名称已存在");
        } else if (postService.checkPostCodeUnique(vo)) {
            return Result.fail("新增岗位'" + vo.getCode() + "'失败，岗位编码已存在");
        }

        postService.save(vo);
        return Result.success();
    }

    /**
     * 修改信息
     *
     * @param vo
     * @return
     */
    @PutMapping
    @PreAuthorize("@ps.hasPermi('system:post:edit')")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE, businessTypeName = "修改信息")
    public Result edit(@Validated @RequestBody SysPositionVo vo) {
        if (postService.checkPostIdAndCodeUnique(vo)) {
            return Result.fail("修改岗位'" + vo.getName() + "'失败，岗位编码已存在");
        }

        postService.update(vo);
        return Result.success();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ps.hasPermi('system:post:remove')")
    @Log(title = "岗位管理", businessType = BusinessType.DELETE, businessTypeName = "删除")
    public Result remove(@PathVariable List<Long> ids) {
        postService.deleteByIds(ids);
        return Result.success();
    }

    /**
     * 获取对应岗位的部门列表树
     *
     * @param positoinId
     * @return
     */
    @PreAuthorize("@ps.hasPermi('system:post:edit')")
    @GetMapping("/dept/treeselect/{positoinId}")
    public Map<String, Object> positionDeptTreeselect(@PathVariable Long positoinId) {
        List<SysDeptVo> depts = deptService.selectAllDept();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("checkedKeys", deptService.selectDeptListByPositionId(positoinId));
        map.put("depts", deptService.buildDeptTreeSelect(depts));
        return map;
    }

    /**
     * 修改数据权限
     *
     * @param position
     * @return
     */
    @PutMapping("/datascope")
    @PreAuthorize("@ps.hasPermi('system:post:edit')")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE, businessTypeName = "修改数据权限")
    public Result dataScope(@RequestBody SysPositionVo position) {
        postService.authDataScope(position);
        return Result.success();
    }
}
