package com.gl.framework.security.handle;

import com.alibaba.fastjson.JSON;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.common.util.servlet.ServletUtils;
import com.gl.framework.constant.Constants;
import com.gl.framework.manager.AsyncManager;
import com.gl.framework.manager.factory.AsyncFactory;
import com.gl.framework.security.LoginUser;
import com.gl.framework.security.service.TokenService;
import com.gl.framework.web.response.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 自定义退出处理类 返回成功
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    @Autowired
    private TokenService tokenService;

    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        LoginUser loginUser = tokenService.getLoginUser(request);

        if (StringUtils.isNotNull(loginUser)) {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLoginLog(userName, Constants.LOGOUT, "退出成功"));
        }

        ServletUtils.renderString(response, JSON.toJSONString(Result.success("退出成功")));
    }
}
