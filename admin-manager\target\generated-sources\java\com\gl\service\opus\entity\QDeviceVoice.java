package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QDeviceVoice is a Querydsl query type for DeviceVoice
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QDeviceVoice extends EntityPathBase<DeviceVoice> {

    private static final long serialVersionUID = -1871357141L;

    public static final QDeviceVoice deviceVoice = new QDeviceVoice("deviceVoice");

    public final NumberPath<Long> backgroundMusicId = createNumber("backgroundMusicId", Long.class);

    public final NumberPath<Integer> backgroundMusicVolume = createNumber("backgroundMusicVolume", Integer.class);

    public final StringPath content = createString("content");

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> deviceId = createNumber("deviceId", Long.class);

    public final NumberPath<Long> deviceVoiceId = createNumber("deviceVoiceId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> pitch = createNumber("pitch", Integer.class);

    public final NumberPath<Integer> sampleRate = createNumber("sampleRate", Integer.class);

    public final NumberPath<Integer> sortby = createNumber("sortby", Integer.class);

    public final NumberPath<Integer> speed = createNumber("speed", Integer.class);

    public final StringPath title = createString("title");

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public final NumberPath<Long> voiceId = createNumber("voiceId", Long.class);

    public final NumberPath<Integer> voiceTime = createNumber("voiceTime", Integer.class);

    public final StringPath voiceUrl = createString("voiceUrl");

    public final NumberPath<Long> voiceWorkId = createNumber("voiceWorkId", Long.class);

    public final NumberPath<Integer> volume = createNumber("volume", Integer.class);

    public QDeviceVoice(String variable) {
        super(DeviceVoice.class, forVariable(variable));
    }

    public QDeviceVoice(Path<? extends DeviceVoice> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDeviceVoice(PathMetadata metadata) {
        super(DeviceVoice.class, metadata);
    }

}

