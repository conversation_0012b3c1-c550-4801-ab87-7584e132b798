<component name="libraryTable">
  <library name="Maven: com.huaweicloud.sdk:huaweicloud-sdk-sis:3.1.112">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/huaweicloud/sdk/huaweicloud-sdk-sis/3.1.112/huaweicloud-sdk-sis-3.1.112.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/huaweicloud/sdk/huaweicloud-sdk-sis/3.1.112/huaweicloud-sdk-sis-3.1.112-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/huaweicloud/sdk/huaweicloud-sdk-sis/3.1.112/huaweicloud-sdk-sis-3.1.112-sources.jar!/" />
    </SOURCES>
  </library>
</component>