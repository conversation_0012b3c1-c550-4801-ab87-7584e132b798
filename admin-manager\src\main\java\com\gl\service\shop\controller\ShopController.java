package com.gl.service.shop.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.web.response.Result;
import com.gl.service.shop.service.ShopService;
import com.gl.service.shop.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Controller
@RequestMapping("/shop")
public class ShopController {

    @Autowired
    private ShopService shopService;

    /**
     * 列表查询
     * 
     * @param queryParamVo
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('shop:shop:list')")
    public Result list(ShopQueryParamVo queryParamVo) {
        return shopService.list(queryParamVo);
    }

    /**
     * 新增
     * 
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('shop:shop:add')")
    public Result add(@RequestBody ShopAddVo vo) {
        return shopService.add(vo);
    }

    /**
     * 修改
     * 
     * @param vo
     * @return
     */
    @PutMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('shop:shop:add')")
    public Result edit(@RequestBody ShopAddVo vo) {
        return shopService.update(vo);
    }

    @DeleteMapping
    @PreAuthorize("@ps.hasPermi('shop:shop:delete')")
    @ResponseBody
    @Log(title = "门店管理", businessType = BusinessType.DELETE, businessTypeName = "删除门店")
    public Result remove(@RequestBody ShopQueryParamVo queryParamVo) {
        return shopService.deleteByIds(queryParamVo.getShopIds());
    }

    /**
     * 详情
     * 
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @ResponseBody
    public Result detail(@RequestParam(name = "id") Long id) {
        return shopService.detailById(id);
    }

    /**
     * 审核 examine
     * 
     * @param examineVo
     * @return
     */
    @PostMapping("/examine")
    @ResponseBody
    public Result examine(@Valid @RequestBody ShopExamineVo examineVo) {
        return shopService.updateByStatus(examineVo);
    }

    /**
     * 输入管理员手机号点击查询，自动获取门店信息
     * 
     * @param phone
     * @return
     */
    @GetMapping("/getByAdminPhone")
    @ResponseBody
    public Result getByAdminPhone(@RequestParam(name = "phone") String phone) {
        return shopService.getByAdminPhone(phone);
    }

    /**
     * 绑定门店
     * 
     * @return
     */
    @PostMapping("/bind")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('shop:shop:bind')")
    public Result bind(@RequestBody ShopBindVo bindVo) {
        return shopService.bind(bindVo);
    }

    /**
     * 解绑
     * 
     * @param id
     * @return
     */
    @PostMapping("/unbind")
    @ResponseBody
    public Result unbind(@RequestParam(name = "id") Long id) {
        return shopService.unbind(id);
    }

    /**
     * "没有门店"用户
     */
    @GetMapping("/notBindShopUsers")
    @ResponseBody
    public Result getNotBindShopUsers() {
        return shopService.getNotBindShopUsers();
    }

    /**
     * 查询门店用户
     */
    @GetMapping("/listByShopId")
    @ResponseBody
    public Result listByShopId(@RequestParam(name = "name") Long shopId) {
        return shopService.listByShopId(shopId);
    }

    /**
     * 设为管理员、 管理店员
     * 
     * @return
     */
    @PostMapping("/settingAdmin")
    @ResponseBody
    public Result settingAdmin(@RequestBody ShopSettingVo settingVo) {
        return shopService.settingAdmin(settingVo);
    }

    @GetMapping(value = "/getShopList")
    @ResponseBody
    public Result getShopList() {
        return shopService.getShopList();
    }

    /**
     * 查询可绑定的门店 parentId = 0
     * 
     * @return
     */
    @GetMapping("getParentList")
    @ResponseBody
    public Result getParentList() {
        return shopService.getParentList();
    }

    /**
     * 更新店员角色
     * 
     * @return
     */
    @PostMapping("updatePerson")
    @ResponseBody
    public Result updatePerson(@Valid @RequestBody ShopUpdatePerson updatePerson) {
        return shopService.updatePerson(updatePerson);
    }

    /**
     * 查询关联的店员列表
     * 
     * @return
     */
    @GetMapping("shopPersonList/{shopId}")
    @ResponseBody
    public Result shopPersonList(@PathVariable Long shopId) {
        return shopService.shopPersonList(shopId);
    }

    /**
     * 查询可选店员列表
     * 
     * @return
     */
    @PostMapping("shopUserList")
    @ResponseBody
    public Result shopUserList(@RequestParam(name = "shopId") Long shopId,
            @RequestParam(name = "isAll") Boolean isAll) {
        return shopService.shopUserList(shopId, isAll);
    }

    @PostMapping("searchShopByPhone")
    @ResponseBody
    public Result searchShopByPhone(@RequestParam(name = "phone") String phone) {
        return shopService.searchShopByPhone(phone);
    }

    @PostMapping("shareBindShop")
    @ResponseBody
    public Result shareBindShop() {
        return shopService.shareBindShop();
    }
}
