package com.gl.system.repository;

import com.gl.system.entity.SysConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface SysConfigRepository extends JpaRepository<SysConfig, Long>, JpaSpecificationExecutor<SysConfig> {

	List<SysConfig> findByIdIn(List<Long> ids);

	SysConfig findByKey(String key);

}
