package com.gl.system.entity;

import com.gl.framework.entity.IdEntity;
import lombok.Data;

import javax.persistence.*;
import java.util.*;

/**
 * 离职继承操作记录表
 */
@Data
@Entity
@Table(name = "sys_user_leave_log")
public class SysUserLeaveLog extends IdEntity {

	/**
	 * 离职员工id
	 */
	@Column(name = "leave_user_id", nullable = false)
	private Long leaveUserId;

	/**
	 * 继承员工id
	 */
	@Column(name = "inherit_user_id", nullable = false)
	private Long inheritUserId;

	/**
	 * 操作用户id
	 */
	@Column(name = "operator_user_id", nullable = false)
	private Long operatorUserId;

	/**
	 * 操作时间
	 */
	@Column(name = "operator_time", nullable = false)
	private Date operatorTime;
}
