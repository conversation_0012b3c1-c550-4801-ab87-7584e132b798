package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * 发音人表
 *
 * @author: duanjinze
 * @date: 2022/11/10 11:51
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_anchor")
public class Anchor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 名称
     */
    @Column(name = "name", columnDefinition = "varchar")
    private String name;

    /**
     * voice参数
     */
    @Column(name = "voice_name", columnDefinition = "varchar")
    private String voiceName;

    /**
     * 类型名称
     */
    @Column(name = "type_name", columnDefinition = "varchar")
    private String typeName;

    /**
     * 使用场景
     */
    @Column(name = "usage_scenario", columnDefinition = "varchar")
    private String usageScenario;

    /**
     * 1中文及英文混合 2纯中文场景 3标准粤文
     */
    @Column(name = "support_voice_type", columnDefinition = "smallint")
    private Integer supportVoiceType;

    /**
     * 支持采样率
     */
    @Column(name = "sample_rate", columnDefinition = "smallint")
    private Integer sampleRate;

    /**
     * 支持时间戳
     */
    @Column(name = "enable_subtitle", columnDefinition = "smallint")
    private Integer enableSubtitle;

    /**
     * 支持儿化音
     */
    @Column(name = "rhotic_accent", columnDefinition = "smallint")
    private Integer rhoticAccent;

    @Column(name = "url", columnDefinition = "varchar")
    private String url;

    @Column(name = "type", columnDefinition = "varchar")
    private String type;

    @Column(name = "voice_url", columnDefinition = "varchar")
    private String voiceUrl;

    @Column(name = "archor_tag", columnDefinition = "varchar")
    private String archorTag;

    @Column(name = "is_emotion", columnDefinition = "smallint")
    private Integer isEmotion;

    @Column(name = "emotion", columnDefinition = "varchar")
    private String emotion;

    @Column(name = "order_index", columnDefinition = "smallint")
    private Integer orderIndex;
}
