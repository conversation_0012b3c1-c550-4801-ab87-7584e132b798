package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysUserLeaveLog is a Querydsl query type for SysUserLeaveLog
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysUserLeaveLog extends EntityPathBase<SysUserLeaveLog> {

    private static final long serialVersionUID = 1992462173L;

    public static final QSysUserLeaveLog sysUserLeaveLog = new QSysUserLeaveLog("sysUserLeaveLog");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Long> inheritUserId = createNumber("inheritUserId", Long.class);

    public final NumberPath<Long> leaveUserId = createNumber("leaveUserId", Long.class);

    public final DateTimePath<java.util.Date> operatorTime = createDateTime("operatorTime", java.util.Date.class);

    public final NumberPath<Long> operatorUserId = createNumber("operatorUserId", Long.class);

    public QSysUserLeaveLog(String variable) {
        super(SysUserLeaveLog.class, forVariable(variable));
    }

    public QSysUserLeaveLog(Path<? extends SysUserLeaveLog> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysUserLeaveLog(PathMetadata metadata) {
        super(SysUserLeaveLog.class, metadata);
    }

}

