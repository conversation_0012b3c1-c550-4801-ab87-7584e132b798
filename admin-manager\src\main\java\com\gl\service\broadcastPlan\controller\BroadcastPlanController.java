package com.gl.service.broadcastPlan.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.broadcastPlan.service.BroadcastPlanService;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/broadcastPlan")
public class BroadcastPlanController {

    @Autowired
    private BroadcastPlanService broadcastPlanService;

    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:broadcastPlan:list')")
    public Result list(BroadcastPlanDto dto) {
        return broadcastPlanService.list(dto, 1);
    }

    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:broadcastPlan:add')")
    public Result addOrUpdate(@RequestBody BroadcastPlanAddDto vo) {
        return broadcastPlanService.addOrUpdate(vo);
    }

    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('dub:broadcastPlan:delete')")
    public Result delete(@RequestBody BroadcastPlanAddDto dto) {
        return broadcastPlanService.delete(dto);
    }

    @GetMapping("/{id}")
    @ResponseBody
    public Result getInfo(@PathVariable Long id) {
        return broadcastPlanService.getInfo(id);
    }

}
