package com.gl.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import com.gl.framework.common.util.redis.RedisUtils;
import com.gl.framework.security.service.UserDetailsServiceImpl;
import com.gl.redis.RedisService;
import com.gl.service.shop.service.WeChatService;
import com.gl.system.service.SysConfigService;
import com.gl.system.service.SysUserService;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.when;

/**
 * 控制器测试基类
 * 提供统一的测试配置和通用的Mock设置
 *
 * @author: Test
 * @date: 2024/12/19
 * @version: 1.0
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = com.gl.ManagerApplication.class, properties = {
        "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration"
})
@TestPropertySource(locations = "classpath:application-test.yml")
@ActiveProfiles("test")
@AutoConfigureMockMvc
@Import(TestRedisConfig.class)
public abstract class BaseControllerTest {

    @Autowired
    protected MockMvc mockMvc;

    @MockBean(name = "ps")
    protected com.gl.framework.security.service.PermissionService permissionService;

    @MockBean
    protected com.gl.framework.security.service.TokenService tokenService;

    @MockBean
    protected StringRedisTemplate stringRedisTemplate;

    @MockBean
    protected RedisUtils redisUtils;

    @MockBean
    protected RedisService redisService;

    @MockBean
    protected SysConfigService sysConfigService;

    @MockBean
    protected SysUserService sysUserService;

    @MockBean
    protected UserDetailsServiceImpl userDetailsService;

    @MockBean
    protected WeChatService weChatService;

    @Autowired
    protected ObjectMapper objectMapper;

    @BeforeEach
    void baseSetUp() {
        // 配置默认权限服务模拟
        setupDefaultPermissions();
    }

    /**
     * 设置默认权限配置
     * 子类可以重写此方法来自定义权限设置
     */
    protected void setupDefaultPermissions() {
        // 默认允许所有权限，子类可以重写来自定义
        when(permissionService.hasPermi(argThat(perm -> true))).thenReturn(true);
    }

    /**
     * 配置特定权限为拒绝
     * @param permission 权限字符串
     */
    protected void denyPermission(String permission) {
        when(permissionService.hasPermi(permission)).thenReturn(false);
    }

    /**
     * 配置特定权限为允许
     * @param permission 权限字符串
     */
    protected void allowPermission(String permission) {
        when(permissionService.hasPermi(permission)).thenReturn(true);
    }
}
