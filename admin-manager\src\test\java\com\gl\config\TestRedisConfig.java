package com.gl.config;

import com.gl.redis.RedisService;
import com.gl.framework.common.util.redis.RedisUtils;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;

import static org.mockito.Mockito.mock;

/**
 * 测试Redis配置类
 * 为测试环境提供模拟的Redis相关Bean
 *
 * @author: test
 * @date: 2025-01-11
 * @version: 1.0
 */
@TestConfiguration
public class TestRedisConfig {

    /**
     * 提供模拟的StringRedisTemplate Bean
     * 使用stringRedisTemplate作为Bean名称，满足@Qualifier的要求
     */
    @Bean(name = "stringRedisTemplate")
    @Primary
    public StringRedisTemplate stringRedisTemplate() {
        return mock(StringRedisTemplate.class);
    }

    /**
     * 提供模拟的RedisTemplate Bean
     */
    @Bean
    @Primary
    @SuppressWarnings("unchecked")
    public RedisTemplate<String, Object> redisTemplate() {
        return mock(RedisTemplate.class);
    }

    /**
     * 提供模拟的RedisService Bean
     */
    @Bean
    @Primary
    public RedisService redisService() {
        return mock(RedisService.class);
    }

    /**
     * 提供模拟的JedisConnectionFactory Bean
     */
    @Bean
    @Primary
    public JedisConnectionFactory jedisConnectionFactory() {
        return mock(JedisConnectionFactory.class);
    }

    /**
     * 提供模拟的RedisUtils Bean
     */
    @Bean
    @Primary
    public RedisUtils redisUtils() {
        return mock(RedisUtils.class);
    }
}
