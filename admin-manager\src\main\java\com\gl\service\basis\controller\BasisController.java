package com.gl.service.basis.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.basis.service.BasisService;
import com.gl.service.basis.vo.BasisDto;
import com.gl.service.basis.vo.BasisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * @author: duanjinze
 * @date: 2022/11/11 16:42
 * @version: 1.0
 */
@Controller
@RequestMapping("/basis")
public class BasisController {
    @Autowired
    private BasisService basisService;

    /**
     * 查询
     *
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('basis:basis:list')")
    public Result select() {
        return basisService.select();
    }

    /**
     * 新增和修改
     *
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('basis:basis:add')")
    public Result add(@RequestBody BasisVo vo) {
        return basisService.add(vo);
    }

    /**
     * 删除模板类型
     *
     * @param dto templateTypeId
     * @return
     */
    @DeleteMapping("/template")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('basis:basis:template')")
    public Result deleteTemplate(BasisDto dto) {
        return basisService.deleteTemplate(dto.getTemplateTypeId());
    }

    /**
     * 删除背景音乐类型
     *
     * @param dto musicTypeId
     * @return
     */
    @DeleteMapping("/music")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('basis:basis:music')")
    public Result deleteMusic(BasisDto dto) {
        return basisService.deleteMusic(dto.getMusicTypeId());
    }
}
