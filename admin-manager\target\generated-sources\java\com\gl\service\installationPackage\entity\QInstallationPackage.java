package com.gl.service.installationPackage.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QInstallationPackage is a Querydsl query type for InstallationPackage
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QInstallationPackage extends EntityPathBase<InstallationPackage> {

    private static final long serialVersionUID = 373334488L;

    public static final QInstallationPackage installationPackage = new QInstallationPackage("installationPackage");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> createUserId = createNumber("createUserId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath packageUrl = createString("packageUrl");

    public final StringPath remark = createString("remark");

    public final StringPath versionName = createString("versionName");

    public QInstallationPackage(String variable) {
        super(InstallationPackage.class, forVariable(variable));
    }

    public QInstallationPackage(Path<? extends InstallationPackage> path) {
        super(path.getType(), path.getMetadata());
    }

    public QInstallationPackage(PathMetadata metadata) {
        super(InstallationPackage.class, metadata);
    }

}

