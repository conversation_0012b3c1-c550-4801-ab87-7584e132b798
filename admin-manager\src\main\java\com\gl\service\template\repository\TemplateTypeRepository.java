package com.gl.service.template.repository;

import com.gl.service.opus.entity.TemplateType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface TemplateTypeRepository extends JpaRepository<TemplateType,Long>, JpaSpecificationExecutor<TemplateType> {
    @Modifying
    @Transactional
    @Query(value = "update dub_template_type set del_status = 1 where id = ?1 ",nativeQuery = true)
    Integer updateDelStatusById(Long id);
}
