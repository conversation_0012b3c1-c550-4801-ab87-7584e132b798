package com.gl.framework.config.http;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate 配置
 */
@Configuration
public class RestTemplateConfig {

	@Bean
	public SimpleClientHttpRequestFactory simpleClientHttpRequestFactory() {
		SimpleClientHttpRequestFactory facotry = new SimpleClientHttpRequestFactory();
		facotry.setReadTimeout(120000);
		facotry.setConnectTimeout(30000);
		facotry.setBufferRequestBody(false);
		return facotry;
	}

	@Bean(name = "restTemplate")
	public RestTemplate restTemplate(SimpleClientHttpRequestFactory simpleClientHttpRequestFactory) {
		RestTemplate restTemplate = new RestTemplate(simpleClientHttpRequestFactory);
		restTemplate.setErrorHandler(new RestTemplateErrorHandler());
		return restTemplate;
	}

//	@Bean(name = "restTemplate")
//	public RestTemplate restTemplate(SimpleClientHttpRequestFactory simpleClientHttpRequestFactory,
//									 @Qualifier("restTemplateInterceptor") RestTemplateInterceptor restTemplateInterceptor) {
//
//		RestTemplate restTemplate = new RestTemplate(simpleClientHttpRequestFactory);
//		restTemplate.setErrorHandler(new RestTemplateErrorHandler());
//
//		// 添加拦截器
//		List<ClientHttpRequestInterceptor> rtiList = new ArrayList<>();
//		rtiList.add(restTemplateInterceptor);
//		restTemplate.setInterceptors(rtiList);
//
//		return restTemplate;
//	}

}
