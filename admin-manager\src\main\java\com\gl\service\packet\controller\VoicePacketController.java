package com.gl.service.packet.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.packet.service.VoicePacketService;
import com.gl.service.packet.vo.dto.VoicePacketDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 语音包管理
 *
 * @author: duanjinze
 * @date: 2022/11/11 14:06
 * @version: 1.0
 */
@Controller
@RequestMapping("/packet")
public class VoicePacketController {
    @Autowired
    private VoicePacketService voicePacketService;

    /**
     * 语音包列表
     *
     * @param dto
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('packet:packet:list')")
    public Result list(VoicePacketDto dto) {
        return voicePacketService.list(dto, 1);
    }

    /**
     * 删除
     *
     * @param dto
     * @return
     */
    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('packet:packet:delete')")
    public Result delete(@RequestBody VoicePacketDto dto) {
        return voicePacketService.delete(dto);
    }

    /**
     * 应用设备
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('packet:packet:detail')")
    public Result detail(@RequestParam(name = "id") Long id) {
        return voicePacketService.detail(id);
    }

    /**
     * 语音包下载
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @PostMapping("/zipDown")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('packet:packet:zipdown')")
    public void plistDownLoad(@RequestBody VoicePacketDto dto, HttpServletResponse response) throws Exception {
        voicePacketService.plistDownLoad(dto, response);
    }
}
