package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAiStyle is a Querydsl query type for AiStyle
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAiStyle extends EntityPathBase<AiStyle> {

    private static final long serialVersionUID = -1196348264L;

    public static final QAiStyle aiStyle = new QAiStyle("aiStyle");

    public final StringPath content = createString("content");

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> orderIndex = createNumber("orderIndex", Integer.class);

    public final StringPath title = createString("title");

    public QAiStyle(String variable) {
        super(AiStyle.class, forVariable(variable));
    }

    public QAiStyle(Path<? extends AiStyle> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAiStyle(PathMetadata metadata) {
        super(AiStyle.class, metadata);
    }

}

