package com.gl.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public enum FlowerSiteEnum {
    LEVEL_1(1,"A"),
    LEVEL_2(2,"B"),
    LEVEL_3(3,"C"),
    LEVEL_4(4,"D"),
    LEVEL_5(5,"E"),
    LEVEL_6(6,"F"),
    LEVEL_7(7,"G"),
    LEVEL_8(8,"H"),
    LEVEL_9(9,"I"),
    LEVEL_10(10,"J");

    private final Integer status;
    private final String msg;

    FlowerSiteEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, String> lookupMap = new HashMap<>();

    static {
        lookupMap = EnumSet.allOf(FlowerSiteEnum.class).stream()
                .collect(Collectors.toMap(FlowerSiteEnum::getStatus, FlowerSiteEnum::getMsg));
    }

    /**
     * 获取状态字符串
     */
    public static String getValue(Integer status) {
        return lookupMap.get(status);
    }

}
