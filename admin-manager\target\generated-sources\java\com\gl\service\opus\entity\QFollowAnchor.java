package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QFollowAnchor is a Querydsl query type for FollowAnchor
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QFollowAnchor extends EntityPathBase<FollowAnchor> {

    private static final long serialVersionUID = 55506167L;

    public static final QFollowAnchor followAnchor = new QFollowAnchor("followAnchor");

    public final NumberPath<Long> anchorId = createNumber("anchorId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QFollowAnchor(String variable) {
        super(FollowAnchor.class, forVariable(variable));
    }

    public QFollowAnchor(Path<? extends FollowAnchor> path) {
        super(path.getType(), path.getMetadata());
    }

    public QFollowAnchor(PathMetadata metadata) {
        super(FollowAnchor.class, metadata);
    }

}

