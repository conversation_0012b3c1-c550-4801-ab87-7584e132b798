package com.gl.service.broadcastPlan.repository;

import com.gl.service.broadcastPlan.entity.BroadcastPlanVoiceWorkRef;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface BroadcastPlanVoiceWorkRepository extends JpaRepository<BroadcastPlanVoiceWorkRef,Long>, JpaSpecificationExecutor<BroadcastPlanVoiceWorkRef> {

    @Transactional
    @Modifying
    @Query(value = "delete from dub_broadcast_plan_voice_work_ref  where plan_id = ?1 ",nativeQuery = true)
    Integer deleteByPlanId(Long planId);

    @Transactional
    @Modifying
    @Query(value = "delete from dub_broadcast_plan_voice_work_ref  where plan_id in ?1 ",nativeQuery = true)
    Integer deleteAllByPlanId(List<Long> planId);
    @Query(value = " SELECT\n" +
            "  br.id,v.id vId,  \n" +
            "  v.title name,\n" +
            "  br.sort \n" +
            "FROM\n" +
            "  dub_broadcast_plan_voice_work_ref br\n" +
            "  LEFT JOIN dub_voice_work v ON v.id = br.voice_work_id \n" +
            "WHERE\n" +
            "  br.plan_id = ?1 \n" +
            "ORDER BY\n" +
            "  br.sort ASC ",nativeQuery = true)
    List<Map<String,Object>> getByPlanId(Long planId);

}
