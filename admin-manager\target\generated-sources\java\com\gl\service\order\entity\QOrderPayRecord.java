package com.gl.service.order.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QOrderPayRecord is a Querydsl query type for OrderPayRecord
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QOrderPayRecord extends EntityPathBase<OrderPayRecord> {

    private static final long serialVersionUID = 898494241L;

    public static final QOrderPayRecord orderPayRecord = new QOrderPayRecord("orderPayRecord");

    public final NumberPath<Integer> amount = createNumber("amount", Integer.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final DateTimePath<java.util.Date> endTime = createDateTime("endTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> orderId = createNumber("orderId", Long.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final StringPath transactionId = createString("transactionId");

    public QOrderPayRecord(String variable) {
        super(OrderPayRecord.class, forVariable(variable));
    }

    public QOrderPayRecord(Path<? extends OrderPayRecord> path) {
        super(path.getType(), path.getMetadata());
    }

    public QOrderPayRecord(PathMetadata metadata) {
        super(OrderPayRecord.class, metadata);
    }

}

