package com.gl.service.shop.service;

import cn.hutool.core.collection.CollUtil;
import com.gl.commons.enums.StoreTypeEnum;
import com.gl.commons.enums.StoreUserTypeEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.opus.entity.Device;
import com.gl.service.shop.entity.Shop;
import com.gl.service.shop.entity.ShopUserRef;
import com.gl.service.shop.repository.ShopRepository;
import com.gl.service.shop.repository.ShopUserRefRepository;
import com.gl.service.shop.vo.*;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import com.gl.wechat.entity.WechatUser;
import com.gl.wechat.repository.WechatUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ShopService 单元测试类
 * 
 * @author: test
 * @date: 2025-01-11
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
class ShopServiceTest {

    @InjectMocks
    private ShopService shopService;

    @Mock
    private ShopRepository shopRepository;

    @Mock
    private ShopUserRefRepository shopUserRefRepository;

    @Mock
    private DeviceRepository deviceRepository;

    @Mock
    private WechatUserRepository wechatUserRepository;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private GetShopRefUtil shopRefUtil;

    // 测试数据
    private ShopQueryParamVo queryParamVo;
    private ShopAddVo shopAddVo;
    private ShopExamineVo shopExamineVo;
    private ShopBindVo shopBindVo;
    private ShopSettingVo shopSettingVo;
    private Shop shop;
    private ShopUserRef shopUserRef;
    private SysUserVo sysUserVo;
    private LoginUser loginUser;
    private WechatUser wechatUser;
    private Device device;

    @BeforeEach
    void setUp() {
        // 初始化查询参数VO
        queryParamVo = new ShopQueryParamVo();
        queryParamVo.setShopName("测试门店");
        queryParamVo.setStatus(1);
        queryParamVo.setType(1);
        queryParamVo.setShopIds(Arrays.asList(1L, 2L));

        // 初始化门店新增/修改VO
        shopAddVo = new ShopAddVo();
        shopAddVo.setId(1L);
        shopAddVo.setShopName("测试门店");
        shopAddVo.setUserId(1L);
        shopAddVo.setUserPhone("13800138000");
        shopAddVo.setUserName("测试用户");

        // 初始化门店审核VO
        shopExamineVo = new ShopExamineVo();
        shopExamineVo.setId(1L);
        shopExamineVo.setStatus(1);

        // 初始化门店绑定VO
        shopBindVo = new ShopBindVo();
        shopBindVo.setId(1L);
        shopBindVo.setParentId(2L);
        shopBindVo.setShopName("测试分店");
        shopBindVo.setUserId(1L);
        shopBindVo.setPhoneNumber("13800138000");

        // 初始化门店设置VO
        shopSettingVo = new ShopSettingVo();
        shopSettingVo.setUserId(1L);
        shopSettingVo.setShopId(1L);
        shopSettingVo.setRole(1);

        // 初始化门店实体
        shop = new Shop();
        shop.setId(1L);
        shop.setShopName("测试门店");
        shop.setParentId(0L);
        shop.setType(StoreTypeEnum.PARENT.getCode());
        shop.setStatus(1);
        shop.setCreateUserId(1L);
        shop.setCreateTime(new Date());

        // 初始化门店用户关系实体
        shopUserRef = new ShopUserRef();
        shopUserRef.setId(1L);
        shopUserRef.setUserId(1L);
        shopUserRef.setShopId(1L);
        shopUserRef.setRole(StoreUserTypeEnum.ADMIN.getCode());

        // 初始化系统用户VO
        sysUserVo = new SysUserVo();
        sysUserVo.setId(1L);
        sysUserVo.setSiteId(1L);
        sysUserVo.setType(2); // 微信用户

        // 初始化登录用户
        loginUser = new LoginUser();
        loginUser.setUser(sysUserVo);

        // 初始化微信用户
        wechatUser = new WechatUser();
        wechatUser.setId(1L);
        wechatUser.setNickname("测试用户");
        wechatUser.setPhone("13800138000");

        // 初始化设备
        device = new Device();
        device.setId(1L);
        device.setName("测试设备");
        device.setShopId(1L);
    }

    /**
     * 测试门店列表查询 - 成功场景
     */
    @Test
    void testList_Success() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(5L);

        List<ShopPageResVo> mockShops = new ArrayList<>();
        ShopPageResVo shopPageResVo = new ShopPageResVo();
        shopPageResVo.setId(1L);
        shopPageResVo.setShopName("测试门店");
        shopPageResVo.setDeviceCount(2);
        shopPageResVo.setUserCount(3);
        mockShops.add(shopPageResVo);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(mockShops);

        // When
        Result result = shopService.list(queryParamVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    /**
     * 测试门店列表查询 - 微信用户无权限场景
     */
    @Test
    void testList_WxUserNoPermission() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When
        Result result = shopService.list(queryParamVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"));
        assertEquals(new ArrayList<>(), data.get("result"));
    }

    /**
     * 测试门店新增 - 成功场景
     */
    @Test
    void testAdd_Success() {
        // Given
        shopAddVo.setId(null); // 新增场景
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(shopAddVo.getUserId())))
                .thenReturn(new ArrayList<>()); // 无绑定关系

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);
            when(shopRepository.save(any(Shop.class))).thenReturn(shop);
            when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(shopUserRef);

            // When
            Result result = shopService.add(shopAddVo);

            // Then
            assertNotNull(result);
            assertEquals(200, result.getCode());
            verify(shopRepository, times(1)).save(any(Shop.class));
            verify(shopUserRefRepository, times(1)).save(any(ShopUserRef.class));
        }
    }

    /**
     * 测试门店新增 - 用户已有绑定关系失败场景
     */
    @Test
    void testAdd_UserAlreadyBound() {
        // Given
        shopAddVo.setId(null); // 新增场景
        List<ShopUserRef> existingRefs = Arrays.asList(shopUserRef);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(shopAddVo.getUserId())))
                .thenReturn(existingRefs);

        // When
        Result result = shopService.add(shopAddVo);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("该用户已有绑定关系，不允许新增门店", result.getMessage());
    }

    /**
     * 测试门店修改 - 成功场景
     */
    @Test
    void testUpdate_Success() {
        // Given
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(shopAddVo.getUserId())))
                .thenReturn(Arrays.asList(shopUserRef)); // 有绑定关系但是同一个门店
        when(shopRepository.getById(shopAddVo.getId())).thenReturn(shop);
        when(shopRepository.save(any(Shop.class))).thenReturn(shop);

        // When
        Result result = shopService.update(shopAddVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(shopRepository, times(1)).getById(shopAddVo.getId());
        verify(shopRepository, times(1)).save(any(Shop.class));
    }

    /**
     * 测试门店修改 - ID为空失败场景
     */
    @Test
    void testUpdate_IdNull() {
        // Given
        shopAddVo.setId(null);

        // When
        Result result = shopService.update(shopAddVo);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("修改门店时id不能为空", result.getMessage());
    }

    /**
     * 测试门店删除 - 成功场景
     */
    @Test
    void testDeleteByIds_Success() {
        // Given
        List<Long> shopIds = Arrays.asList(1L, 2L);
        List<Shop> shops = Arrays.asList(shop);
        when(shopRepository.findAllById(shopIds)).thenReturn(shops);
        when(shopRepository.selectByParentIdIn(shopIds)).thenReturn(new ArrayList<>()); // 无分店

        // When
        Result result = shopService.deleteByIds(shopIds);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(shopRepository, times(1)).findAllById(shopIds);
        verify(shopUserRefRepository, times(1)).deleteAllByShopIdIn(shopIds);
        verify(deviceRepository, times(1)).deleteByShopIdIn(shopIds);
        verify(shopRepository, times(1)).deleteByIds(shopIds);
    }

    /**
     * 测试门店删除 - 参数为空失败场景
     */
    @Test
    void testDeleteByIds_EmptyIds() {
        // Given
        List<Long> emptyIds = new ArrayList<>();

        // When
        Result result = shopService.deleteByIds(emptyIds);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("请选择要删除的店铺", result.getMessage());
    }

    /**
     * 测试门店删除 - 门店不存在失败场景
     */
    @Test
    void testDeleteByIds_ShopsNotFound() {
        // Given
        List<Long> shopIds = Arrays.asList(1L, 2L);
        when(shopRepository.findAllById(shopIds)).thenReturn(new ArrayList<>());

        // When
        Result result = shopService.deleteByIds(shopIds);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("店铺不存在", result.getMessage());
    }

    /**
     * 测试门店详情查询 - 成功场景
     */
    @Test
    void testDetailById_Success() {
        // Given
        List<ShopAddVo> mockDetails = Arrays.asList(shopAddVo);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                .thenReturn(mockDetails);

        // When
        Result result = shopService.detailById(1L);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(shopAddVo, result.getData());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), eq(1L));
    }

    /**
     * 测试门店详情查询 - 无数据失败场景
     */
    @Test
    void testDetailById_NoData() {
        // Given
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                .thenReturn(new ArrayList<>());

        // When
        Result result = shopService.detailById(1L);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("查询错误【空数据】", result.getMessage());
    }

    /**
     * 测试门店审核状态更新 - 审核通过场景
     */
    @Test
    void testUpdateByStatus_ApproveSuccess() {
        // Given
        shopExamineVo.setStatus(1); // 审核通过
        when(jdbcTemplate.update(anyString(), eq(shopExamineVo.getId()))).thenReturn(1);

        // When
        Result result = shopService.updateByStatus(shopExamineVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(1, result.getData());
        verify(jdbcTemplate, times(1)).update(anyString(), eq(shopExamineVo.getId()));
    }

    /**
     * 测试门店审核状态更新 - 审核不通过场景
     */
    @Test
    void testUpdateByStatus_RejectSuccess() {
        // Given
        shopExamineVo.setStatus(0); // 审核不通过
        when(jdbcTemplate.update(anyString(), eq(shopExamineVo.getId()))).thenReturn(1);

        // When
        Result result = shopService.updateByStatus(shopExamineVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(1, result.getData());
        verify(jdbcTemplate, times(1)).update(anyString(), eq(shopExamineVo.getId()));
    }

    /**
     * 测试门店审核状态更新 - 操作失败场景
     */
    @Test
    void testUpdateByStatus_UpdateFailed() {
        // Given
        when(jdbcTemplate.update(anyString(), eq(shopExamineVo.getId()))).thenReturn(0);

        // When
        Result result = shopService.updateByStatus(shopExamineVo);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("操作失败", result.getMessage());
    }

    /**
     * 测试门店绑定 - 成功场景
     */
    @Test
    void testBind_Success() {
        // Given
        Shop parentShop = new Shop();
        parentShop.setId(2L);
        parentShop.setStatus(1);
        parentShop.setType(StoreTypeEnum.PARENT.getCode());

        Shop childShop = new Shop();
        childShop.setId(1L);
        childShop.setStatus(1);
        childShop.setType(StoreTypeEnum.PARENT.getCode());

        when(shopRepository.getById(shopBindVo.getId())).thenReturn(childShop);
        when(shopRepository.getById(shopBindVo.getParentId())).thenReturn(parentShop);
        when(shopRepository.save(any(Shop.class))).thenReturn(childShop);

        // When
        Result result = shopService.bind(shopBindVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(shopRepository, times(1)).getById(shopBindVo.getId());
        verify(shopRepository, times(1)).getById(shopBindVo.getParentId());
        verify(shopRepository, times(1)).save(any(Shop.class));
    }

    /**
     * 测试门店绑定 - 店铺ID为空失败场景
     */
    @Test
    void testBind_ShopIdNull() {
        // Given
        shopBindVo.setId(null);

        // When
        Result result = shopService.bind(shopBindVo);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("店铺id不能为空", result.getMessage());
    }

    /**
     * 测试门店绑定 - 上级店铺ID为空失败场景
     */
    @Test
    void testBind_ParentIdNull() {
        // Given
        shopBindVo.setParentId(null);

        // When
        Result result = shopService.bind(shopBindVo);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("上级店铺id不能为空", result.getMessage());
    }

    /**
     * 测试门店绑定 - 店铺不存在失败场景
     */
    @Test
    void testBind_ShopNotFound() {
        // Given
        Shop emptyShop = new Shop();
        emptyShop.setId(null);
        when(shopRepository.getById(shopBindVo.getId())).thenReturn(emptyShop);

        // When
        Result result = shopService.bind(shopBindVo);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("店铺不存在", result.getMessage());
    }

    /**
     * 测试门店绑定 - 店铺非审核通过状态失败场景
     */
    @Test
    void testBind_ShopNotApproved() {
        // Given
        Shop unapprovedShop = new Shop();
        unapprovedShop.setId(1L);
        unapprovedShop.setStatus(0); // 非审核通过状态
        when(shopRepository.getById(shopBindVo.getId())).thenReturn(unapprovedShop);

        // When
        Result result = shopService.bind(shopBindVo);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("店铺非审核通过状态", result.getMessage());
    }

    /**
     * 测试门店解绑 - 成功场景
     */
    @Test
    void testUnbind_Success() {
        // Given
        Shop boundShop = new Shop();
        boundShop.setId(1L);
        boundShop.setParentId(2L);
        boundShop.setType(StoreTypeEnum.BRANCH.getCode());

        when(shopRepository.getById(1L)).thenReturn(boundShop);
        when(shopRepository.save(any(Shop.class))).thenReturn(boundShop);

        // When
        Result result = shopService.unbind(1L);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(shopRepository, times(1)).getById(1L);
        verify(shopRepository, times(1)).save(any(Shop.class));
    }

    /**
     * 测试根据手机号查询门店 - 成功场景
     */
    @Test
    void testSearchShopByPhone_Success() {
        // Given
        String phone = "13800138000";
        List<ShopBindVo> mockResults = Arrays.asList(shopBindVo);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(phone)))
                .thenReturn(mockResults);

        // When
        Result result = shopService.searchShopByPhone(phone);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(shopBindVo, result.getData());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), eq(phone));
    }

    /**
     * 测试根据手机号查询门店 - 未找到门店失败场景
     */
    @Test
    void testSearchShopByPhone_NotFound() {
        // Given
        String phone = "13800138000";
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(phone)))
                .thenReturn(new ArrayList<>());

        // When
        Result result = shopService.searchShopByPhone(phone);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("未找到该手机号绑定的门店", result.getMessage());
    }

    /**
     * 测试获取未绑定门店的用户 - 成功场景
     */
    @Test
    void testGetNotBindShopUsers_Success() {
        // Given
        List<Object> mockUsers = new ArrayList<>();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockUsers);

        // When
        Result result = shopService.getNotBindShopUsers();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(mockUsers, result.getData());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    /**
     * 测试根据门店ID查询用户列表 - 成功场景
     */
    @Test
    void testListByShopId_Success() {
        // Given
        Long shopId = 1L;
        List<ShopUserVo> mockUsers = new ArrayList<>();
        ShopUserVo shopUserVo = new ShopUserVo();
        shopUserVo.setShopId(shopId);
        shopUserVo.setUserId(1L);
        shopUserVo.setRole(1);
        mockUsers.add(shopUserVo);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(shopId)))
                .thenReturn(mockUsers);

        // When
        Result result = shopService.listByShopId(shopId);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(mockUsers, result.getData());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), eq(shopId));
    }

    /**
     * 测试设置管理员 - 成功场景
     */
    @Test
    void testSettingAdmin_Success() {
        // Given
        when(shopUserRefRepository.findByShopIdAndUserIdAndRole(
                shopSettingVo.getShopId(),
                shopSettingVo.getUserId(),
                StoreUserTypeEnum.ADMIN.getCode()))
                .thenReturn(null); // 不存在管理员关系

        when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(shopUserRef);

        // When
        Result result = shopService.settingAdmin(shopSettingVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(shopUserRefRepository, times(1)).save(any(ShopUserRef.class));
    }

    /**
     * 测试获取门店列表 - 成功场景
     */
    @Test
    void testGetShopList_Success() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        List<Object> mockShops = new ArrayList<>();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockShops);

        // When
        Result result = shopService.getShopList();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(mockShops, result.getData());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    /**
     * 测试获取门店列表 - 微信用户无权限场景
     */
    @Test
    void testGetShopList_WxUserNoPermission() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When
        Result result = shopService.getShopList();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(new ArrayList<>(), result.getData());
    }

    /**
     * 测试获取可绑定的门店列表 - 成功场景
     */
    @Test
    void testGetParentList_Success() {
        // Given
        List<Object> mockParentShops = new ArrayList<>();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockParentShops);

        // When
        Result result = shopService.getParentList();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(mockParentShops, result.getData());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    /**
     * 测试根据管理员手机号获取门店 - 成功场景
     */
    @Test
    void testGetByAdminPhone_Success() {
        // Given
        String phone = "13800138000";
        List<Object> mockResults = new ArrayList<>();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(phone)))
                .thenReturn(mockResults);

        // When
        Result result = shopService.getByAdminPhone(phone);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(mockResults, result.getData());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), eq(phone));
    }

    /**
     * 测试分享绑定门店 - 成功场景
     */
    @Test
    void testShareBindShop_Success() {
        // Given
        when(shopRefUtil.getOneShop()).thenReturn(1L);

        // When
        Result result = shopService.shareBindShop();

        // Then
        assertNull(result); // 方法返回null
        verify(shopRefUtil, times(1)).getOneShop();
    }

    /**
     * 测试门店有分店时删除失败场景
     */
    @Test
    void testDeleteByIds_HasBranchShops() {
        // Given
        List<Long> shopIds = Arrays.asList(1L);
        List<Shop> shops = Arrays.asList(shop);
        Shop branchShop = new Shop();
        branchShop.setId(2L);
        branchShop.setParentId(1L);
        List<Shop> branchShops = Arrays.asList(branchShop);

        when(shopRepository.findAllById(shopIds)).thenReturn(shops);
        when(shopRepository.selectByParentIdIn(shopIds)).thenReturn(branchShops);

        // When
        Result result = shopService.deleteByIds(shopIds);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("总店已经绑定分店，不能删除", result.getMessage());
    }

    /**
     * 测试门店列表查询 - 带查询条件
     */
    @Test
    void testList_WithQueryConditions() {
        // Given
        queryParamVo.setShopName("测试");
        queryParamVo.setStatus(1);
        queryParamVo.setType(1);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(3L);

        List<ShopPageResVo> mockShops = new ArrayList<>();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(mockShops);

        // When
        Result result = shopService.list(queryParamVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }
}
