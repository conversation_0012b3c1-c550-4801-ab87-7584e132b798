package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysConfig is a Querydsl query type for SysConfig
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysConfig extends EntityPathBase<SysConfig> {

    private static final long serialVersionUID = -1711103897L;

    public static final QSysConfig sysConfig = new QSysConfig("sysConfig");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> createUserId = createNumber("createUserId", Long.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final StringPath key = createString("key");

    public final StringPath name = createString("name");

    public final StringPath remark = createString("remark");

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public final NumberPath<Long> updateUserId = createNumber("updateUserId", Long.class);

    public final StringPath value = createString("value");

    public QSysConfig(String variable) {
        super(SysConfig.class, forVariable(variable));
    }

    public QSysConfig(Path<? extends SysConfig> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysConfig(PathMetadata metadata) {
        super(SysConfig.class, metadata);
    }

}

