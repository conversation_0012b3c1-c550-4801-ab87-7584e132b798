package com.gl.service.opus.dto;


public class AnchorDTO {
    /**
     * 主播id
     */
    private Long id;

    /**
     * 主播名称
     */
    private String name;

    /**
     * 主播分类
     */
    private String usageScenario;

    /**
     * 主播场景
     */
    private String typeName;

    private String url;

    private String voiceUrl;


    private String archorTag;

    private Integer isFavorited = 1;

    public AnchorDTO() {
    }

    public AnchorDTO(Long id, String name, String usageScenario, String typeName, String url, String voiceUrl, String archorTag) {
        this.id = id;
        this.name = name;
        this.usageScenario = usageScenario;
        this.typeName = typeName;
        this.url = url;
        this.voiceUrl = voiceUrl;
        this.archorTag = archorTag;
    }

    public AnchorDTO(Long id, String name, String usageScenario, String typeName, String url, String voiceUrl) {
        this.id = id;
        this.name = name;
        this.usageScenario = usageScenario;
        this.typeName = typeName;
        this.url = url;
        this.voiceUrl = voiceUrl;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsageScenario() {
        return usageScenario;
    }

    public void setUsageScenario(String usageScenario) {
        this.usageScenario = usageScenario;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }


    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    public String getVoiceUrl() {
        return voiceUrl;
    }

    public void setVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
    }


    public String getArchorTag() {
        return archorTag;
    }

    public void setArchorTag(String archorTag) {
        this.archorTag = archorTag;
    }

    public Integer getIsFavorited() {
        return isFavorited;
    }

    public void setIsFavorited(Integer isFavorited) {
        this.isFavorited = isFavorited;
    }
}
