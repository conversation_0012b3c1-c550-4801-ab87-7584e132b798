package com.gl.service.installationPackage.service;

import com.alibaba.fastjson.JSONObject;
import com.gl.config.mqtt.MqttClientConfig;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.installationPackage.entity.InstallationPackage;
import com.gl.service.installationPackage.entity.InstallationPackageLog;
import com.gl.service.installationPackage.repository.InstallationPackageLogRepository;
import com.gl.service.installationPackage.repository.InstallationPackageRepository;
import com.gl.service.opus.entity.Device;
import com.gl.system.vo.SysUserVo;
import com.gl.util.mqtt.MqttSample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2025/3/28
 * @description:
 */
@Slf4j
@Service
public class AsyncPackageService {

    @Autowired
    private MqttSample mqttSample;

    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private InstallationPackageRepository packageRepository;
    @Autowired
    private InstallationPackageLogRepository installationPackageLogRepository;
    @Autowired
    private MqttClientConfig mqttClientConfig;

    @Async
    public void uploadPackage(List<InstallationPackageLog> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        List<Long> deviceIdList = new ArrayList<>();
        List<Long> packIdList = new ArrayList<>();
        list.forEach(packageLog -> {
            packIdList.add(packageLog.getPackageId());
            deviceIdList.add(packageLog.getDeviceId());
        });
        List<Device> devList = deviceRepository.findAllById(deviceIdList);
        Map<Long, Device> devMap = devList.stream().collect(Collectors.toMap(Device::getId, device -> device));

        List<InstallationPackage> packageList = packageRepository.findAllById(packIdList);
        Map<Long, InstallationPackage> packMap = packageList.stream().collect(Collectors.toMap(InstallationPackage::getId, device -> device));

        try {
            for (InstallationPackageLog packageLog : list) {
                Device dev = devMap.get(packageLog.getDeviceId());
                InstallationPackage aPackage = packMap.get(packageLog.getPackageId());

                String timeStamp = mqttClientConfig.getTimestamp();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("cmd", dev.getSn());
                jsonObject.put("url", aPackage.getPackageUrl());
                jsonObject.put("timeStamp", timeStamp);
                mqttClientConfig.publish(dev.getSn(), jsonObject.toJSONString(), 0, false, user.getId(), timeStamp);
                String msg = mqttClientConfig.getTopicResponse(timeStamp);
                // 阻塞等待返回消息，超时时间为 10 秒
                if (StringUtils.isNotEmpty(msg)) {
                    JSONObject data = JSONObject.parseObject(msg);
                    if (data.getInteger("status") == 0) {
                        packageLog.setResponseTime(new Date());
                        packageLog.setStatus(2);
                        installationPackageLogRepository.save(packageLog);
                    } else {
                        packageLog.setResponseTime(new Date());
                        packageLog.setStatus(0);
                        installationPackageLogRepository.save(packageLog);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
