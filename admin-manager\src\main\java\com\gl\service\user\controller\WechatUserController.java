package com.gl.service.user.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.user.service.WechatUserService;
import com.gl.service.user.vo.SysAreaTreeVo;
import com.gl.service.user.vo.dto.WechatUserDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 微信用户列表
 * @author: duanjinze
 * @date: 2022/11/11 15:07
 * @version: 1.0
 */
@Controller
@RequestMapping("/user")
public class WechatUserController {

    @Autowired
    private WechatUserService wechatUserService;

    /**
     * 微信用户列表
     * @param dto
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('user:user:list')")
    public Result list(WechatUserDto dto){
        return wechatUserService.list(dto, 1);
    }

    /**
     * 微信用户列表导出
     * @param dto
     * @param response
     * @throws IOException
     */
    @PostMapping("/export")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('user:user:export')")
    public void exportList(@RequestBody WechatUserDto dto, HttpServletResponse response) throws IOException{
        wechatUserService.exportList(dto, response);
    }

    /**
     * 区域
     * @param name
     * @return
     */
    @GetMapping("/area")
    @ResponseBody
    public List<SysAreaTreeVo> areaList(@RequestParam(name = "name",required = false) String name) {
        return wechatUserService.areaList(name);
    }

    /**
     * 区域
     * @return
     */
    @GetMapping("/checkPhone")
    @ResponseBody
    public Result checkPhone() {
        return wechatUserService.checkPhone();
    }



}
