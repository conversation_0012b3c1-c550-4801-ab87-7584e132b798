package com.gl.system.service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.criteria.Predicate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.gl.framework.common.util.StringUtils;
import com.gl.framework.exception.CustomException;
import com.gl.system.entity.QSysArea;
import com.gl.system.entity.SysArea;
import com.gl.system.repository.SysAreaRepository;
import com.gl.system.vo.SysAreaVo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;

import lombok.extern.slf4j.Slf4j;

/**
 * 角色服务实现类
 */
@Slf4j
@Service
public class SysAreaService {

    @Autowired
    private SysAreaRepository areaRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    JPAQueryFactory queryFactory;


    public List<SysAreaVo> list(SysAreaVo vo) {
        List<SysAreaVo> list = areaRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.and();

            if (vo != null) {
                if (StringUtils.isNotBlank(vo.getName())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("name"), "%" + vo.getName() + "%"));
                }
            }

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("parentId"), vo.getParentId()));

            return predicate;
        }, Sort.by("id")).stream().map(this::convert).collect(Collectors.toList());

        String sql = "SELECT a.id, COUNT(a2.id) AS total\n" +
                "FROM sys_area a\n" +
                "LEFT JOIN sys_area a2 ON a2.parent_id = a.id\n" +
                "WHERE a.parent_id = " + vo.getParentId() +
                " GROUP BY a.id";

        List<Map<String, Object>> totalList = jdbcTemplate.queryForList(sql);

        log.info(sql);

        for (SysAreaVo srVo : list) {
            for (Iterator it = totalList.iterator(); it.hasNext(); ) {
                Map<String, Object> map = (Map<String, Object>) it.next();
                Long id = Long.parseLong(map.get("id").toString());
                if (id.longValue() == srVo.getId().longValue()) {
                    Long total = Long.parseLong(map.get("total").toString());
                    if (total.longValue() == 0) {
                        srVo.setHasChildren(false);
                    }
                    it.remove();
                }
            }
        }

        return list;
    }

    public List<SysAreaVo> list(Long parentId) {

        JPAQuery<SysArea> jpaQuery = queryFactory.selectFrom(QSysArea.sysArea);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(QSysArea.sysArea.parentId.longValue().eq(parentId));
        jpaQuery.where(builder);

        QueryResults<SysArea> results = jpaQuery.fetchResults();
        List<SysArea> list = results.getResults();
        List<SysAreaVo> listVo = list.stream().map(this::convert).collect(Collectors.toList());
        return listVo;
    }

    public List<SysAreaVo> list(Long parentId, String keyword) {

        JPAQuery<SysArea> jpaQuery = queryFactory.select(QSysArea.sysArea).from(QSysArea.sysArea);
        if (parentId == null) {
            parentId = 0L;
        }
        jpaQuery.where(QSysArea.sysArea.parentId.eq(parentId));
        if (StringUtils.isNotEmpty(keyword)) {
            jpaQuery.where(QSysArea.sysArea.name.like("%" + keyword + "%"));
        }
        QueryResults<SysArea> results = jpaQuery.fetchResults();
        List<SysArea> list = results.getResults();
        List<SysAreaVo> listVo = list.stream().map(this::convert).collect(Collectors.toList());
        return listVo;
    }

    /**
     * 数据转换
     *
     * @param entity 菜单实体
     * @return 菜单VO
     */
    private SysAreaVo convert(SysArea entity) {
        BeanCopier beanCopier = BeanCopier.create(SysArea.class, SysAreaVo.class, false);
        SysAreaVo vo = new SysAreaVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    public SysAreaVo findById(Long id) {
        Optional<SysArea> optional = areaRepository.findById(id);

        if (!optional.isPresent()) {
            return null;
        }
        SysArea entity = optional.get();
        SysAreaVo vo = new SysAreaVo();
        BeanCopier beanCopier = BeanCopier.create(SysArea.class, SysAreaVo.class, false);
        beanCopier.copy(entity, vo, null);
        
        // 查询父部门名称
        if(vo.getParentId() != 0){
	        Optional<SysArea> parentOptional = areaRepository.findById(vo.getParentId());
	        if (parentOptional.isPresent()) {
	        	vo.setParentName(parentOptional.get().getName());
	        }
        }else{
        	vo.setParentName("无");
        }
        return vo;
    }

    /**
     * 新增
     *
     * @param vo
     */
    public void save(SysAreaVo vo) {
    	if(vo.getParentId() != null){
    		// 查询上级的层级
    		Optional<SysArea> optional = areaRepository.findById(vo.getParentId());
            if (optional.isPresent()) {
            	if(optional.get().getLevel() != null){
            		vo.setLevel(optional.get().getLevel() + 1);
            	}
            }

            if(vo.getParentId() == 0){
            	vo.setLevel(1);
            }
    	}
    	
    	
        if(vo.getId() == null){
        	vo.setId(Long.parseLong(vo.getCode()));
        }else{
        	Optional<SysArea> optional = areaRepository.findById(vo.getId());
            if (optional.isPresent()) {
                throw new CustomException("行政区域数据已存在");
            }
        }
    	
        BeanCopier beanCopier = BeanCopier.create(SysAreaVo.class, SysArea.class, false);
        SysArea entity = new SysArea();
        beanCopier.copy(vo, entity, null);
        areaRepository.save(entity);
    }

    /**
     * 更新
     *
     * @param vo
     */
    public void update(SysAreaVo vo) {
        Optional<SysArea> optional = areaRepository.findById(vo.getId());
        if (!optional.isPresent()) {
            throw new CustomException("行政区域数据不存在");
        }
        SysArea entity = new SysArea();
        BeanCopier beanCopier = BeanCopier.create(SysAreaVo.class, SysArea.class, false);
        beanCopier.copy(vo, entity, null);
        areaRepository.save(entity);
    }

    /**
     * 删除信息
     *
     * @param id
     */
    public void delete(Long id) {

        Integer count = areaRepository.countByParentId(id);
        if (count > 0) {
            throw new CustomException("有子节点数据，不允许删除");
        }
        areaRepository.deleteById(id);
    }

}
