package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QUserFollowBgm is a Querydsl query type for UserFollowBgm
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QUserFollowBgm extends EntityPathBase<UserFollowBgm> {

    private static final long serialVersionUID = 141408955L;

    public static final QUserFollowBgm userFollowBgm = new QUserFollowBgm("userFollowBgm");

    public final NumberPath<Long> bgmId = createNumber("bgmId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QUserFollowBgm(String variable) {
        super(UserFollowBgm.class, forVariable(variable));
    }

    public QUserFollowBgm(Path<? extends UserFollowBgm> path) {
        super(path.getType(), path.getMetadata());
    }

    public QUserFollowBgm(PathMetadata metadata) {
        super(UserFollowBgm.class, metadata);
    }

}

