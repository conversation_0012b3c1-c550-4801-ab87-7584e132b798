package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QLongFollowAnchor is a Querydsl query type for LongFollowAnchor
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QLongFollowAnchor extends EntityPathBase<LongFollowAnchor> {

    private static final long serialVersionUID = 865875219L;

    public static final QLongFollowAnchor longFollowAnchor = new QLongFollowAnchor("longFollowAnchor");

    public final NumberPath<Long> anchorId = createNumber("anchorId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QLongFollowAnchor(String variable) {
        super(LongFollowAnchor.class, forVariable(variable));
    }

    public QLongFollowAnchor(Path<? extends LongFollowAnchor> path) {
        super(path.getType(), path.getMetadata());
    }

    public QLongFollowAnchor(PathMetadata metadata) {
        super(LongFollowAnchor.class, metadata);
    }

}

