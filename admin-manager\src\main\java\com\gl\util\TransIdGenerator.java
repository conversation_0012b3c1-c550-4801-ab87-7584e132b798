package com.gl.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

public class TransIdGenerator {

    // 用于生成8位序列的原子长整型，确保线程安全
    private static final AtomicLong sequence = new AtomicLong(1);

    // 日期时间格式
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    /**
     * 生成事务编码
     * @return 事务编码字符串
     */
    public static String generateTransId() {
        // 获取当前日期时间
        String datetime = DATE_FORMAT.format(new Date());

        // 获取并递增序列号，确保唯一性
        long seq = sequence.getAndIncrement();

        // 格式化为8位数字，前面补零
        String sequenceStr = String.format("%08d", seq);

        // 组合APPID + 日期时间 + 序列号
        return datetime + sequenceStr;
    }

    public static void main(String[] args) {
        // 示例：生成10个事务编码
        for (int i = 0; i < 10; i++) {
            String transId = generateTransId();
            System.out.println(transId);
        }
    }
}
