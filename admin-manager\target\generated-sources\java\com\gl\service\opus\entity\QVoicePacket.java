package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QVoicePacket is a Querydsl query type for VoicePacket
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QVoicePacket extends EntityPathBase<VoicePacket> {

    private static final long serialVersionUID = -483135735L;

    public static final QVoicePacket voicePacket = new QVoicePacket("voicePacket");

    public final StringPath fileUrl = createString("fileUrl");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath name = createString("name");

    public final NumberPath<Long> shopId = createNumber("shopId", Long.class);

    public final NumberPath<Integer> voiceTime = createNumber("voiceTime", Integer.class);

    public final NumberPath<Long> voiceWorkId = createNumber("voiceWorkId", Long.class);

    public QVoicePacket(String variable) {
        super(VoicePacket.class, forVariable(variable));
    }

    public QVoicePacket(Path<? extends VoicePacket> path) {
        super(path.getType(), path.getMetadata());
    }

    public QVoicePacket(PathMetadata metadata) {
        super(VoicePacket.class, metadata);
    }

}

