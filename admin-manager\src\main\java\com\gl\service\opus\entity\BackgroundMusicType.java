package com.gl.service.opus.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * 背景音乐类型
 * @author: duanjinze
 * @date: 2022/11/10 13:33
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_background_music_type")
public class BackgroundMusicType {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 名称
     */
    @Basic
    @Column(name = "name")
    private String name;
}
