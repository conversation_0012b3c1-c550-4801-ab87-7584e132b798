package com.gl.framework.common.util.sql;

import com.gl.framework.common.util.StringUtils;
import com.gl.framework.exception.BaseException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * sql操作工具类
 */
public class SqlUtils {

	private SqlUtils() {
	}

	/**
	 * 仅支持字母、数字、下划线、空格、逗号、小数点（支持多个字段排序）
	 */
	public static String SQL_PATTERN = "[a-zA-Z0-9_\\ \\,\\.]+";

	/**
	 * 检查字符，防止注入绕过
	 */
	public static String escapeOrderBySql(String value) {
		if (StringUtils.isNotEmpty(value) && !isValidOrderBySql(value)) {
			throw new BaseException("参数不符合规范，不能进行查询");
		}
		return value;
	}

	/**
	 * 验证 order by 语法是否符合规范
	 */
	public static boolean isValidOrderBySql(String value) {
		return value.matches(SQL_PATTERN);
	}

	public static <T> String foreach(String open, String close, String separator, Iterable<T> collection) {
		Iterator<T> it = collection.iterator();
		if (!it.hasNext()) {
			return open + close;
		}

		StringBuilder sb = new StringBuilder();
		sb.append(open);
		for (;;) {
			T e = it.next();
			sb.append(e);
			if (!it.hasNext()) {
				return sb.append(close).toString();
			}
			sb.append(separator).append(' ');
		}
	}

	public static String foreachIn(int size) {
		List<Character> args = new ArrayList<>(size);
		for (int i = 0; i < size; i++) {
			args.add('?');
		}
		return foreach("(", ")\n", ",", args);
	}
}
