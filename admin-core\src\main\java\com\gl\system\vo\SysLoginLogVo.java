package com.gl.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.framework.web.domain.BaseVo;

import java.util.Date;

/**
 * 系统访问记录VO
 */
public class SysLoginLogVo extends BaseVo {
	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 用户账号
	 */
	private String loginName;

	/**
	 * 登录状态（0成功 1失败）
	 */
	private Integer status;

	/**
	 * 登录IP地址
	 */
	private String ipaddr;

	/**
	 * 登录地点
	 */
	private String loginLocation;

	/**
	 * 浏览器类型
	 */
	private String browser;

	/**
	 * 操作系统
	 */
	private String os;

	/**
	 * 提示消息
	 */
	private String msg;

	/**
	 * 访问时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date loginTime;

	@Override
    public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getIpaddr() {
		return ipaddr;
	}

	public void setIpaddr(String ipaddr) {
		this.ipaddr = ipaddr;
	}

	public String getLoginLocation() {
		return loginLocation;
	}

	public void setLoginLocation(String loginLocation) {
		this.loginLocation = loginLocation;
	}

	public String getBrowser() {
		return browser;
	}

	public void setBrowser(String browser) {
		this.browser = browser;
	}

	public String getOs() {
		return os;
	}

	public void setOs(String os) {
		this.os = os;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Date getLoginTime() {
		return loginTime;
	}

	public void setLoginTime(Date loginTime) {
		this.loginTime = loginTime;
	}

	@Override
	public String toString() {
		return "SysLoginLogVo [id=" + id + ", loginName=" + loginName + ", status=" + status + ", ipaddr=" + ipaddr
				+ ", loginLocation=" + loginLocation + ", browser=" + browser + ", os=" + os + ", msg=" + msg
				+ ", loginTime=" + loginTime + "]";
	}

}
