package com.gl.framework.config.http;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.IOException;

/**
 * RestTemplate 错误处理器
 */
public class RestTemplateErrorHandler implements ResponseErrorHandler {

	private ResponseErrorHandler myErrorHandler = new DefaultResponseErrorHandler();

	@Override
	public boolean hasError(ClientHttpResponse response) throws IOException {
		return myErrorHandler.hasError(response);
	}

	@Override
	public void handleError(ClientHttpResponse response) throws IOException {
		String body = IOUtils.toString(response.getBody(), "UTF-8");

		String msg = null;
		if (StringUtils.isNotBlank(body)) {
			JSONObject jsonObj = JSONObject.parseObject(body);
			Object msgObj = jsonObj.get("errMsg");
			if (msgObj == null) {
				msg = body;
			} else {
				msg = msgObj.toString();
			}
		}
		String message = msg == null ? "" : msg;
		throw new RestCustomException(response.getStatusCode(), body, message);
	}
}
