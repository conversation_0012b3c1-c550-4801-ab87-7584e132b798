package com.gl.system.repository;

import com.gl.system.entity.SysUserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SysUserRoleRepository extends JpaRepository<SysUserRole, Long>, JpaSpecificationExecutor<SysUserRole> {

    int countByRoleId(Long roleId);

    @Transactional
    void deleteByUserId(Long userId);

    List<SysUserRole> findByUserId(Long userId);

    List<SysUserRole> findByRoleId(Long roleId);

    @Transactional
    @Modifying
    @Query(value = "delete from SysUserRole u where u.userId in (?1)")
    void deleteBatch(List<Long> userId);
}
