package com.gl.framework.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.servlet.ServletUtils;
import com.gl.framework.interceptor.annotation.RepeatSubmit;
import com.gl.framework.web.response.Result;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 防止重复提交拦截器
 */
@Component
public abstract class RepeatSubmitInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            RepeatSubmit annotation = method.getAnnotation(RepeatSubmit.class);
            if (annotation != null) {
                if (this.isRepeatSubmit(request)) {
                    Result result = Result.fail("不允许重复提交，请稍后再试");
                    ServletUtils.renderString(response, JSONObject.toJSONString(result));
                    return false;
                }
            }

        }
        return true;
//        else {
//            return preHandle(request, response, handler);
//        }
    }

    /**
     * 验证是否重复提交由子类实现具体的防重复提交的规则
     *
     * @param request
     * @return
     */
    public abstract boolean isRepeatSubmit(HttpServletRequest request);
}
