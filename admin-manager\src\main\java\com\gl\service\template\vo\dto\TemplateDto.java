package com.gl.service.template.vo.dto;

import com.gl.framework.web.domain.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: duanjinze
 * @date: 2022/11/11 10:29
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TemplateDto extends BaseVo {
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 模板分类id
     */
    private Long templateTypeId;
    /**
     * 搜索框 【模板标题/模板内容】
     */
    private String searchCondition;

    private Long shopId;
    /**
     * 模板id集合
     */
    private List<Long> ids;
}
