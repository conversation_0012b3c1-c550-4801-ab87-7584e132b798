package com.gl.system.entity;

import com.gl.framework.entity.IdEntity;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 岗位和用户关联表实体
 */
@Entity
@Table(name = "sys_position_user")
public class SysPositionUser extends IdEntity {
    /**
     * 岗位ID
     */
    private Long positionId;

    /**
     * 用户ID
     */
    private Long userId;

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "SysPositionUser{" +
                "positionId=" + positionId +
                ", userId=" + userId +
                '}';
    }
}
