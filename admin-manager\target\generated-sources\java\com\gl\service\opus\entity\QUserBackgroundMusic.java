package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QUserBackgroundMusic is a Querydsl query type for UserBackgroundMusic
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QUserBackgroundMusic extends EntityPathBase<UserBackgroundMusic> {

    private static final long serialVersionUID = 936244731L;

    public static final QUserBackgroundMusic userBackgroundMusic = new QUserBackgroundMusic("userBackgroundMusic");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Integer> delStatus = createNumber("delStatus", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath musicUrl = createString("musicUrl");

    public final StringPath name = createString("name");

    public final NumberPath<Long> userId = createNumber("userId", Long.class);

    public QUserBackgroundMusic(String variable) {
        super(UserBackgroundMusic.class, forVariable(variable));
    }

    public QUserBackgroundMusic(Path<? extends UserBackgroundMusic> path) {
        super(path.getType(), path.getMetadata());
    }

    public QUserBackgroundMusic(PathMetadata metadata) {
        super(UserBackgroundMusic.class, metadata);
    }

}

