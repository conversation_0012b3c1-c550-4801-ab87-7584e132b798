package com.gl.service.shop.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.shop.service.ShopService;
import com.gl.service.shop.vo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ShopController 单元测试类
 * 
 * @author: test
 * @date: 2025-01-11
 * @version: 1.0
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = com.gl.ManagerApplication.class)
@AutoConfigureMockMvc
class ShopControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ShopService shopService;

    @Autowired
    private ObjectMapper objectMapper;

    // 测试数据
    private ShopQueryParamVo queryParamVo;
    private ShopAddVo shopAddVo;
    private ShopExamineVo shopExamineVo;
    private ShopBindVo shopBindVo;
    private ShopSettingVo shopSettingVo;
    private Result successResult;
    private Result failResult;

    @BeforeEach
    void setUp() {
        // 初始化查询参数VO
        queryParamVo = new ShopQueryParamVo();
        queryParamVo.setShopName("测试门店");
        queryParamVo.setStatus(1);
        queryParamVo.setType(1);
        queryParamVo.setShopIds(Arrays.asList(1L, 2L));

        // 初始化门店新增/修改VO
        shopAddVo = new ShopAddVo();
        shopAddVo.setId(1L);
        shopAddVo.setShopName("测试门店");
        shopAddVo.setUserId(1L);
        shopAddVo.setUserPhone("13800138000");
        shopAddVo.setUserName("测试用户");

        // 初始化门店审核VO
        shopExamineVo = new ShopExamineVo();
        shopExamineVo.setId(1L);
        shopExamineVo.setStatus(1);

        // 初始化门店绑定VO
        shopBindVo = new ShopBindVo();
        shopBindVo.setId(1L);
        shopBindVo.setParentId(2L);
        shopBindVo.setShopName("测试分店");
        shopBindVo.setUserId(1L);
        shopBindVo.setPhoneNumber("13800138000");

        // 初始化门店设置VO
        shopSettingVo = new ShopSettingVo();
        shopSettingVo.setUserId(1L);
        shopSettingVo.setShopId(1L);
        shopSettingVo.setRole(1);

        // 初始化结果对象
        successResult = Result.success("操作成功");
        failResult = Result.fail("操作失败");
    }

    /**
     * 测试门店列表查询 - 成功场景
     */
    @Test
    @WithMockUser(authorities = "shop:shop:list")
    void testList_Success() throws Exception {
        // Given
        when(shopService.list(any(ShopQueryParamVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop")
                .param("shopName", "测试门店")
                .param("status", "1")
                .param("type", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).list(any(ShopQueryParamVo.class));
    }

    /**
     * 测试门店列表查询 - 无权限场景
     */
    @Test
    @WithMockUser(authorities = "other:permission")
    void testList_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(get("/shop")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(shopService, never()).list(any(ShopQueryParamVo.class));
    }

    /**
     * 测试新增门店 - 成功场景
     */
    @Test
    @WithMockUser(authorities = "shop:shop:add")
    void testAdd_Success() throws Exception {
        // Given
        when(shopService.add(any(ShopAddVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopAddVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).add(any(ShopAddVo.class));
    }

    /**
     * 测试新增门店 - 参数验证失败场景
     */
    @Test
    @WithMockUser(authorities = "shop:shop:add")
    void testAdd_ValidationFailed() throws Exception {
        // Given
        ShopAddVo invalidVo = new ShopAddVo();
        invalidVo.setShopName(""); // 门店名称为空，应该验证失败

        // When & Then
        mockMvc.perform(post("/shop")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidVo)))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(shopService, never()).add(any(ShopAddVo.class));
    }

    /**
     * 测试修改门店 - 成功场景
     */
    @Test
    @WithMockUser(authorities = "shop:shop:add")
    void testEdit_Success() throws Exception {
        // Given
        when(shopService.update(any(ShopAddVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(put("/shop")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopAddVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).update(any(ShopAddVo.class));
    }

    /**
     * 测试删除门店 - 成功场景
     */
    @Test
    @WithMockUser(authorities = "shop:shop:delete")
    void testRemove_Success() throws Exception {
        // Given
        when(shopService.deleteByIds(anyList())).thenReturn(successResult);

        // When & Then
        mockMvc.perform(delete("/shop")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryParamVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).deleteByIds(anyList());
    }

    /**
     * 测试门店详情查询 - 成功场景
     */
    @Test
    @WithMockUser
    void testDetail_Success() throws Exception {
        // Given
        when(shopService.detailById(1L)).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop/detail")
                .param("id", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).detailById(1L);
    }

    /**
     * 测试门店审核 - 成功场景
     */
    @Test
    @WithMockUser
    void testExamine_Success() throws Exception {
        // Given
        when(shopService.updateByStatus(any(ShopExamineVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop/examine")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopExamineVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).updateByStatus(any(ShopExamineVo.class));
    }

    /**
     * 测试根据管理员手机号获取门店 - 成功场景
     */
    @Test
    @WithMockUser
    void testGetByAdminPhone_Success() throws Exception {
        // Given
        when(shopService.getByAdminPhone("13800138000")).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop/getByAdminPhone")
                .param("phone", "13800138000")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).getByAdminPhone("13800138000");
    }

    /**
     * 测试绑定门店 - 成功场景
     */
    @Test
    @WithMockUser(authorities = "shop:shop:bind")
    void testBind_Success() throws Exception {
        // Given
        when(shopService.bind(any(ShopBindVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop/bind")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopBindVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).bind(any(ShopBindVo.class));
    }

    /**
     * 测试解绑门店 - 成功场景
     */
    @Test
    @WithMockUser
    void testUnbind_Success() throws Exception {
        // Given
        when(shopService.unbind(1L)).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop/unbind")
                .with(csrf())
                .param("id", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).unbind(1L);
    }

    /**
     * 测试获取未绑定门店的用户 - 成功场景
     */
    @Test
    @WithMockUser
    void testGetNotBindShopUsers_Success() throws Exception {
        // Given
        when(shopService.getNotBindShopUsers()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop/notBindShopUsers")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).getNotBindShopUsers();
    }

    /**
     * 测试根据门店ID查询用户列表 - 成功场景
     */
    @Test
    @WithMockUser
    void testListByShopId_Success() throws Exception {
        // Given
        when(shopService.listByShopId(1L)).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop/listByShopId")
                .param("name", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).listByShopId(1L);
    }

    /**
     * 测试设置管理员 - 成功场景
     */
    @Test
    @WithMockUser
    void testSettingAdmin_Success() throws Exception {
        // Given
        when(shopService.settingAdmin(any(ShopSettingVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop/settingAdmin")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopSettingVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).settingAdmin(any(ShopSettingVo.class));
    }

    /**
     * 测试获取门店列表 - 成功场景
     */
    @Test
    @WithMockUser
    void testGetShopList_Success() throws Exception {
        // Given
        when(shopService.getShopList()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop/getShopList")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).getShopList();
    }

    /**
     * 测试获取可绑定的门店列表 - 成功场景
     */
    @Test
    @WithMockUser
    void testGetParentList_Success() throws Exception {
        // Given
        when(shopService.getParentList()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop/getParentList")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).getParentList();
    }

    /**
     * 测试根据手机号搜索门店 - 成功场景
     */
    @Test
    @WithMockUser
    void testSearchShopByPhone_Success() throws Exception {
        // Given
        when(shopService.searchShopByPhone("13800138000")).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop/searchShopByPhone")
                .with(csrf())
                .param("phone", "13800138000")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).searchShopByPhone("13800138000");
    }

    /**
     * 测试分享绑定门店 - 成功场景
     */
    @Test
    @WithMockUser
    void testShareBindShop_Success() throws Exception {
        // Given
        when(shopService.shareBindShop()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop/shareBindShop")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).shareBindShop();
    }

    /**
     * 测试门店审核 - 参数验证失败场景
     */
    @Test
    @WithMockUser
    void testExamine_ValidationFailed() throws Exception {
        // Given
        ShopExamineVo invalidVo = new ShopExamineVo();
        invalidVo.setId(null); // ID为空，应该验证失败

        // When & Then
        mockMvc.perform(post("/shop/examine")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidVo)))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(shopService, never()).updateByStatus(any(ShopExamineVo.class));
    }

    /**
     * 测试绑定门店 - 无权限场景
     */
    @Test
    @WithMockUser(authorities = "other:permission")
    void testBind_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(post("/shop/bind")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopBindVo)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(shopService, never()).bind(any(ShopBindVo.class));
    }

    /**
     * 测试服务层返回失败结果的场景
     */
    @Test
    @WithMockUser(authorities = "shop:shop:list")
    void testList_ServiceReturnsFail() throws Exception {
        // Given
        when(shopService.list(any(ShopQueryParamVo.class))).thenReturn(failResult);

        // When & Then
        mockMvc.perform(get("/shop")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(shopService, times(1)).list(any(ShopQueryParamVo.class));
    }
}
