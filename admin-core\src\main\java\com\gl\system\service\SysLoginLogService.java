package com.gl.system.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.StringUtils;
import com.gl.framework.security.LoginUser;
import com.gl.system.entity.SysLoginLog;
import com.gl.system.repository.SysLoginLogRepository;
import com.gl.system.vo.SysLoginLogVo;
import com.gl.system.vo.SysUserVo;
import com.gl.framework.web.domain.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.List;

/**
 * 系统访问日志服务实现类
 */
@Service
public class SysLoginLogService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SysLoginLogRepository loginLogRepository;

    /**
     * 新增系统登录日志
     *
     * @param loginLog 访问日志对象
     */
    public void saveLoginLog(SysLoginLog loginLog) {
        loginLogRepository.save(loginLog);
    }

    /**
     * 查询系统登录日志集合
     *
     * @param filter 过滤条件
     * @return 登录记录集合
     */
    public PageData<SysLoginLogVo> selectLoginLogList(SysLoginLogVo filter) {
        // 数据权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        List<Long> dataScopes = loginUser.getDataScopes();
        boolean admin = SecurityUtils.isSuperAdmin(user.getId());

        PageData<SysLoginLogVo> pageData = new PageData<>();

        Page<SysLoginLogVo> page = loginLogRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.and();

            if (StringUtils.isNotBlank(filter.getIpaddr())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("ipaddr"), "%" + filter.getIpaddr() + "%"));
            }
            if (StringUtils.isNotBlank(filter.getLoginName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("loginName"), "%" + filter.getLoginName() + "%"));
            }
            if (filter.getStatus() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), filter.getStatus()));
            }
            if (StringUtils.isNotBlank(filter.getBeginTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get("loginTime").as(String.class), filter.getBeginTime() + " 00:00:00"));
            }
            if (StringUtils.isNotBlank(filter.getEndTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThan(root.get("loginTime").as(String.class), filter.getEndTime() + " 23:59:59"));
            }

            return predicate;
        }, PageRequest.of(filter.getPageNumber() - 1, filter.getPageSize(), Direction.DESC, "id")).map(this::convert);

        long total = page.getTotalElements();
        List<SysLoginLogVo> data = page.getContent();

        pageData.setTotal(total);
        pageData.setData(data);

        return pageData;
    }

    /**
     * 数据转换
     *
     * @param entity
     * @return
     */
    private SysLoginLogVo convert(SysLoginLog entity) {
        BeanCopier beanCopier = BeanCopier.create(SysLoginLog.class, SysLoginLogVo.class, false);
        SysLoginLogVo vo = new SysLoginLogVo();
        beanCopier.copy(entity, vo, null);
        return vo;
    }

    /**
     * 批量删除系统登录日志
     *
     * @param ids 需要删除的登录日志ID
     * @return
     */
    public void deleteLoginLogByIds(List<Long> ids) {
        loginLogRepository.deleteByIdIn(ids);
    }

    /**
     * 清空系统登录日志
     */
    public void cleanLoginLog() {
        jdbcTemplate.execute("TRUNCATE TABLE sys_login_log");
    }
}
