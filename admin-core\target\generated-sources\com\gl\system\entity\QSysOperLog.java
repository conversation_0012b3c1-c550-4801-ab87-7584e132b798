package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysOperLog is a Querydsl query type for SysOperLog
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysOperLog extends EntityPathBase<SysOperLog> {

    private static final long serialVersionUID = 576143761L;

    public static final QSysOperLog sysOperLog = new QSysOperLog("sysOperLog");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    public final NumberPath<Integer> businessType = createNumber("businessType", Integer.class);

    public final StringPath businessTypeName = createString("businessTypeName");

    public final StringPath deptName = createString("deptName");

    public final StringPath errorMsg = createString("errorMsg");

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final StringPath jsonResult = createString("jsonResult");

    public final StringPath method = createString("method");

    public final NumberPath<Integer> operatorType = createNumber("operatorType", Integer.class);

    public final StringPath operIp = createString("operIp");

    public final StringPath operLocation = createString("operLocation");

    public final StringPath operName = createString("operName");

    public final StringPath operParam = createString("operParam");

    public final DateTimePath<java.util.Date> operTime = createDateTime("operTime", java.util.Date.class);

    public final StringPath operUrl = createString("operUrl");

    public final NumberPath<Long> operUserId = createNumber("operUserId", Long.class);

    public final StringPath requestMethod = createString("requestMethod");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final StringPath title = createString("title");

    public QSysOperLog(String variable) {
        super(SysOperLog.class, forVariable(variable));
    }

    public QSysOperLog(Path<? extends SysOperLog> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysOperLog(PathMetadata metadata) {
        super(SysOperLog.class, metadata);
    }

}

