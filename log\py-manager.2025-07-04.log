14:08:51.885 [34mIN<PERSON><PERSON> [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
14:08:51.913 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 19320 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
14:08:51.914 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
14:08:54.155 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
14:08:54.156 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
14:08:54.499 [34mIN<PERSON><PERSON> [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 335 ms. Found 54 JPA repository interfaces.
14:08:54.854 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
14:08:54.867 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
14:08:55.149 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@35cec305' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:08:55.158 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:08:55.172 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:08:55.183 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:08:55.185 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:08:55.544 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
14:08:55.555 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
14:08:55.555 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
14:08:55.556 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
14:08:55.688 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
14:08:55.689 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3722 ms
14:08:56.063 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
14:08:56.102 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
14:08:56.598 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
14:08:56.771 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
14:08:56.840 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
14:08:57.011 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
14:08:57.364 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
14:08:58.511 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
14:08:58.521 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
14:08:59.904 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
14:08:59.904 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
14:08:59.905 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
14:09:02.380 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
14:09:03.136 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
14:09:03.288 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@12e1413c, org.springframework.security.web.context.SecurityContextPersistenceFilter@184b8899, org.springframework.security.web.header.HeaderWriterFilter@774f2d7f, org.springframework.security.web.authentication.logout.LogoutFilter@308a2eb0, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@28367da7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e16fcaf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1f953e51, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@41b6ae51, org.springframework.security.web.session.SessionManagementFilter@4a547f9d, org.springframework.security.web.access.ExceptionTranslationFilter@58d85a00, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@75351aec]
14:09:03.517 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
14:09:03.517 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
14:09:03.517 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
14:09:03.517 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
14:09:03.536 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
14:09:03.655 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 12.36 seconds (JVM running for 13.725)
14:09:03.661 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

14:09:04.137 [34mINFO [0;39m [RMI TCP Connection(2)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:09:04.137 [34mINFO [0;39m [RMI TCP Connection(2)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
14:09:04.140 [34mINFO [0;39m [RMI TCP Connection(2)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
15:03:16.254 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.system.service.SysUserService - 查询用户部门信息1: 100
15:03:16.254 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.system.service.SysUserService - 查询用户部门信息2: 100
15:03:16.621 [34mINFO [0;39m [schedule-pool-1] sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
15:03:20.050 [34mINFO [0;39m [http-nio-8770-exec-7] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:20.091 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:20.109 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:20.111 [34mINFO [0;39m [http-nio-8770-exec-7] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:20.147 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:20.150 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:20.162 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and  ( create_id = ?  or   s.id in (?)
 )
15:03:20.165 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:03:20.165 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and  ( create_id = ?  or   s.id in (?)
 )) t]
15:03:22.096 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:03:22.096 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 ) t]
15:03:22.125 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:03:22.125 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT
p.id, 
p.name,
p.data_num,
p.effect_day,
p.selling_price,
p.create_time 
FROM dub_paid_packages p
where 1=1 order by p.create_time DESC  LIMIT ? OFFSET ? ]
15:03:23.625 [34mINFO [0;39m [http-nio-8770-exec-10] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:23.636 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:23.636 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:23.640 [34mINFO [0;39m [http-nio-8770-exec-9] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:23.640 [34mINFO [0;39m [http-nio-8770-exec-9] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:23.655 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:03:23.655 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
 o.id, 
 o.out_trade_no ,
  p.`name` packagesName,
  o.amount,
  u.nickname,
  u.phone,
  s.shop_name,
  d.`name` deviceName,
  d.sn deviceSn,
  o.`status`,
  o.create_time,
  o.time_expire,
  0 invoiceStatus 
FROM
  dub_order o
  LEFT JOIN dub_device d ON d.id = o.device_id
  LEFT JOIN dub_shop s ON s.id = o.shop_id
  LEFT JOIN dub_paid_packages p ON p.id = o.package_id
  LEFT JOIN dub_wechat_user u ON u.id = o.user_id  WHERE 1=1  and ( o.user_id = 0 or s.id in (22) ) ) t]
15:03:26.175 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:26.195 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:26.195 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:26.200 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:26.206 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:26.206 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:03:26.206 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 AND s.id in (22)) t]
15:03:29.032 [34mINFO [0;39m [http-nio-8770-exec-3] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:29.043 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:29.043 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:29.357 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
15:03:29.357 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:29.357 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
15:03:29.357 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
15:03:29.373 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:29.373 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
15:03:29.373 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:03:29.373 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
 AND s.id in (22)]
15:03:29.390 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
15:03:29.893 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:03:29.918 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:03:29.918 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:04:18.773 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:04:18.783 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:04:18.783 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:04:22.815 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:04:22.824 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:04:22.824 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:04:22.928 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
15:04:22.933 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:04:22.933 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
15:04:22.933 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
15:04:22.933 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
15:04:22.943 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:04:22.943 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
15:04:22.948 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
15:04:22.948 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
 AND s.id in (22)]
15:04:23.616 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
15:04:23.631 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:04:23.631 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
15:20:19.107 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0,"speechRate":0,"text":"*******************************","userId":0,"volume":100}
15:20:19.144 [1;31mERROR[0;39m [http-nio-8770-exec-1] c.g.f.e.h.GlobalExceptionHandler - The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
org.springframework.dao.InvalidDataAccessApiUsageException: The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:374)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:235)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy198.findById(Unknown Source)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:507)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.util.Assert.notNull(Assert.java:201)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById(SimpleJpaRepository.java:297)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:529)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:599)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:163)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:80)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 107 common frames omitted
15:20:27.515 [34mINFO [0;39m [http-nio-8770-exec-9] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0,"speechRate":0,"text":"*******************************","userId":0,"volume":100}
15:20:27.515 [1;31mERROR[0;39m [http-nio-8770-exec-9] c.g.f.e.h.GlobalExceptionHandler - The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
org.springframework.dao.InvalidDataAccessApiUsageException: The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:374)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:235)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy198.findById(Unknown Source)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:507)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.util.Assert.notNull(Assert.java:201)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById(SimpleJpaRepository.java:297)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:529)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:599)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:163)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:80)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 107 common frames omitted
15:20:43.717 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0,"speechRate":0,"text":"*******************************","userId":0,"volume":100}
15:21:04.416 [1;31mERROR[0;39m [http-nio-8770-exec-5] c.g.f.e.h.GlobalExceptionHandler - The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
org.springframework.dao.InvalidDataAccessApiUsageException: The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:374)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:235)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy198.findById(Unknown Source)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:507)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.util.Assert.notNull(Assert.java:201)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById(SimpleJpaRepository.java:297)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:529)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:599)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:163)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:80)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 107 common frames omitted
16:07:48.978 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:07:48.977 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
16:07:48.977 [34mINFO [0;39m [http-nio-8770-exec-3] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:07:48.977 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
16:07:48.982 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
16:07:48.988 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
16:07:49.063 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:07:49.073 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:07:49.074 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
 AND s.id in (22)]
16:07:49.078 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:07:49.079 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:07:49.085 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
16:07:50.328 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:07:50.341 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:07:50.342 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:08:31.632 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
16:08:31.632 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
16:08:31.632 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
16:08:31.634 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:08:31.634 [34mINFO [0;39m [http-nio-8770-exec-10] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:08:31.634 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
16:08:31.636 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
16:08:31.641 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:08:31.646 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:08:31.646 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:08:31.646 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
 AND s.id in (22)]
16:08:31.647 [34mINFO [0;39m [http-nio-8770-exec-10] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:08:32.704 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:08:32.720 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:08:32.720 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:10:48.767 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:10:48.789 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:10:48.789 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:10:48.813 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:10:48.819 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
16:10:48.820 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
16:10:48.823 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
16:10:48.823 [34mINFO [0;39m [http-nio-8770-exec-5] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:10:48.836 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
16:10:48.836 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:10:48.836 [39mDEBUG[0;39m [http-nio-8770-exec-5] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
 AND s.id in (22)]
16:10:48.843 [39mDEBUG[0;39m [http-nio-8770-exec-1] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
16:10:50.716 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:10:50.729 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:10:50.729 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:11:02.607 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"anchorId":2,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0,"speechRate":0,"text":"<break time=\"6.6s\"></break>2334423424","userId":0,"volume":100}
16:11:02.644 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.opus.service.WorkService - bgm=null
16:11:02.679 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.service.opus.service.WorkService - 转换后的text=<break time="6.6s"></break>2334423424
16:11:03.225 [34mINFO [0;39m [http-nio-8770-exec-5] c.g.s.o.c.c.SpeechSynthesizerRequest - accessToken = 927acd98e8884db1bc1ee345ea69e214
16:11:03.318 [1;31mERROR[0;39m [http-nio-8770-exec-5] c.g.s.o.c.c.SpeechSynthesizerRequest - \Users\verne\home\peiyin\py-service\file\MP3\452ca42a-7d2b-4cf6-a097-c72e049c9e5c.mp3 (系统找不到指定的路径。)
java.io.FileNotFoundException: \Users\verne\home\peiyin\py-service\file\MP3\452ca42a-7d2b-4cf6-a097-c72e049c9e5c.mp3 (系统找不到指定的路径。)
	at java.io.FileOutputStream.open0(Native Method)
	at java.io.FileOutputStream.open(FileOutputStream.java:270)
	at java.io.FileOutputStream.<init>(FileOutputStream.java:213)
	at java.io.FileOutputStream.<init>(FileOutputStream.java:162)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest$1.<init>(SpeechSynthesizerRequest.java:60)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest.getSynthesizerListener(SpeechSynthesizerRequest.java:59)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest.process(SpeechSynthesizerRequest.java:131)
	at com.gl.service.opus.compound.compound.AliDubbingStrategy.process(AliDubbingStrategy.java:36)
	at com.gl.service.opus.compound.compound.DubbingStrategyContext.process(DubbingStrategyContext.java:21)
	at com.gl.service.opus.compound.compound.DubbingFactory.process(DubbingFactory.java:59)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:539)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
16:11:03.945 [1;31mERROR[0;39m [http-nio-8770-exec-5] c.g.s.o.c.c.SpeechSynthesizerRequest - null
java.lang.NullPointerException: null
	at com.alibaba.nls.client.protocol.tts.SpeechSynthesizer.afterConnection(SpeechSynthesizer.java:78)
	at com.alibaba.nls.client.protocol.tts.SpeechSynthesizer.<init>(SpeechSynthesizer.java:61)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest.process(SpeechSynthesizerRequest.java:131)
	at com.gl.service.opus.compound.compound.AliDubbingStrategy.process(AliDubbingStrategy.java:36)
	at com.gl.service.opus.compound.compound.DubbingStrategyContext.process(DubbingStrategyContext.java:21)
	at com.gl.service.opus.compound.compound.DubbingFactory.process(DubbingFactory.java:59)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:539)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
16:11:03.949 [1;31mERROR[0;39m [http-nio-8770-exec-5] c.g.f.e.h.GlobalExceptionHandler - null
java.lang.NullPointerException: null
	at java.io.File.<init>(File.java:279)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:548)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
16:11:24.180 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"anchorId":2,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0,"speechRate":0,"text":"<break time=\"6.6s\"></break>2334423424","userId":0,"volume":100}
16:11:24.189 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.opus.service.WorkService - bgm=null
16:11:24.194 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.opus.service.WorkService - 转换后的text=<break time="6.6s"></break>2334423424
16:11:24.199 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.s.o.c.c.SpeechSynthesizerRequest - accessToken = 927acd98e8884db1bc1ee345ea69e214
16:11:24.235 [1;31mERROR[0;39m [http-nio-8770-exec-4] c.g.s.o.c.c.SpeechSynthesizerRequest - \Users\verne\home\peiyin\py-service\file\MP3\925251d3-138f-4fcc-a6ab-fa8127982509.mp3 (系统找不到指定的路径。)
java.io.FileNotFoundException: \Users\verne\home\peiyin\py-service\file\MP3\925251d3-138f-4fcc-a6ab-fa8127982509.mp3 (系统找不到指定的路径。)
	at java.io.FileOutputStream.open0(Native Method)
	at java.io.FileOutputStream.open(FileOutputStream.java:270)
	at java.io.FileOutputStream.<init>(FileOutputStream.java:213)
	at java.io.FileOutputStream.<init>(FileOutputStream.java:162)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest$1.<init>(SpeechSynthesizerRequest.java:60)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest.getSynthesizerListener(SpeechSynthesizerRequest.java:59)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest.process(SpeechSynthesizerRequest.java:131)
	at com.gl.service.opus.compound.compound.AliDubbingStrategy.process(AliDubbingStrategy.java:36)
	at com.gl.service.opus.compound.compound.DubbingStrategyContext.process(DubbingStrategyContext.java:21)
	at com.gl.service.opus.compound.compound.DubbingFactory.process(DubbingFactory.java:59)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:539)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
16:11:24.382 [1;31mERROR[0;39m [http-nio-8770-exec-4] c.g.s.o.c.c.SpeechSynthesizerRequest - null
java.lang.NullPointerException: null
	at com.alibaba.nls.client.protocol.tts.SpeechSynthesizer.afterConnection(SpeechSynthesizer.java:78)
	at com.alibaba.nls.client.protocol.tts.SpeechSynthesizer.<init>(SpeechSynthesizer.java:61)
	at com.gl.service.opus.compound.compound.SpeechSynthesizerRequest.process(SpeechSynthesizerRequest.java:131)
	at com.gl.service.opus.compound.compound.AliDubbingStrategy.process(AliDubbingStrategy.java:36)
	at com.gl.service.opus.compound.compound.DubbingStrategyContext.process(DubbingStrategyContext.java:21)
	at com.gl.service.opus.compound.compound.DubbingFactory.process(DubbingFactory.java:59)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:539)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
16:11:24.389 [1;31mERROR[0;39m [http-nio-8770-exec-4] c.g.f.e.h.GlobalExceptionHandler - null
java.lang.NullPointerException: null
	at java.io.File.<init>(File.java:279)
	at com.gl.service.opus.service.WorkService.audition(WorkService.java:548)
	at com.gl.service.opus.controller.WorkController.audition(WorkController.java:176)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
16:15:32.142 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
16:15:32.144 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
16:15:32.144 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
16:15:32.226 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
16:15:32.531 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
16:15:32.565 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
16:15:32.590 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
16:15:43.779 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 18504 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
16:15:43.791 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
16:15:44.244 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
16:15:47.473 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
16:15:47.473 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
16:15:47.878 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 397 ms. Found 54 JPA repository interfaces.
16:15:48.246 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
16:15:48.257 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
16:15:48.496 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@433ea2ac' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:15:48.508 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:15:48.522 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:15:48.532 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:15:48.533 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:15:48.864 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
16:15:48.874 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
16:15:48.875 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
16:15:48.875 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
16:15:49.015 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
16:15:49.015 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5057 ms
16:15:49.431 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
16:15:49.467 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
16:15:50.079 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
16:15:50.254 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
16:15:50.325 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
16:15:50.500 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
16:15:50.776 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
16:15:52.410 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
16:15:52.424 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
16:15:54.333 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
16:15:54.333 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
16:15:54.335 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
16:15:57.190 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
16:15:58.040 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
16:15:58.202 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7aadb5e8, org.springframework.security.web.context.SecurityContextPersistenceFilter@3e42cff9, org.springframework.security.web.header.HeaderWriterFilter@6b16aa6f, org.springframework.security.web.authentication.logout.LogoutFilter@4a81582c, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@6def0632, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4dc74211, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2242dd2b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f8060ac, org.springframework.security.web.session.SessionManagementFilter@1e072d67, org.springframework.security.web.access.ExceptionTranslationFilter@6679a082, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@35e55afa]
16:15:58.454 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
16:15:58.454 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
16:15:58.454 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
16:15:58.454 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
16:15:58.515 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
16:15:58.639 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 17.429 seconds (JVM running for 20.085)
16:15:58.652 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

16:15:59.266 [34mINFO [0;39m [RMI TCP Connection(17)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:15:59.266 [34mINFO [0;39m [RMI TCP Connection(17)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
16:15:59.270 [34mINFO [0;39m [RMI TCP Connection(17)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
16:16:44.280 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
16:16:44.280 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 0 subscriber(s).
16:16:44.280 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer - stopped bean '_org.springframework.integration.errorLogger'
16:16:44.294 [34mINFO [0;39m [SpringApplicationShutdownHook] sys-user - ====关闭后台任务任务线程池====
16:16:44.296 [31mWARN [0;39m [SpringApplicationShutdownHook] o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
16:16:44.373 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
16:16:44.377 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
16:16:44.385 [34mINFO [0;39m [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
16:16:53.767 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
16:16:53.799 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 24448 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
16:16:53.800 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
16:16:56.276 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
16:16:56.276 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
16:16:56.661 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 376 ms. Found 54 JPA repository interfaces.
16:16:57.024 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
16:16:57.033 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
16:16:57.264 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@1c297897' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:16:57.273 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:16:57.287 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:16:57.298 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:16:57.300 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
16:16:57.598 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
16:16:57.607 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
16:16:57.607 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
16:16:57.608 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
16:16:57.761 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
16:16:57.762 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3876 ms
16:16:58.109 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
16:16:58.139 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
16:16:58.558 [34mINFO [0;39m [main] c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
16:16:58.708 [34mINFO [0;39m [main] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
16:16:58.750 [34mINFO [0;39m [main] org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
16:16:58.864 [34mINFO [0;39m [main] o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
16:16:59.113 [34mINFO [0;39m [main] org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
16:17:00.051 [34mINFO [0;39m [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
16:17:00.060 [34mINFO [0;39m [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
16:17:01.214 [34mINFO [0;39m [MQTT Call: admin-service-clientId] com.gl.config.mqtt.MqttClientConfig - MQTT连接成功
16:17:01.236 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - MQTT连接成功
16:17:01.239 [34mINFO [0;39m [MQTT Call: admin-service-clientId-Sub] c.gl.config.mqtt.MqttSubscriptConfig - 订阅主题成功
16:17:03.750 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
16:17:04.436 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
16:17:04.558 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c3f0f2e, org.springframework.security.web.context.SecurityContextPersistenceFilter@77cac3b, org.springframework.security.web.header.HeaderWriterFilter@3776c917, org.springframework.security.web.authentication.logout.LogoutFilter@de4bee9, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@1b3a9ef4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35007b7a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6565691a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5e324000, org.springframework.security.web.session.SessionManagementFilter@7a9df9b2, org.springframework.security.web.access.ExceptionTranslationFilter@71390c8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2c513e9f]
16:17:04.783 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
16:17:04.784 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).
16:17:04.784 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'
16:17:04.784 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8770"]
16:17:04.802 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8770 (http) with context path ''
16:17:04.905 [34mINFO [0;39m [main] com.gl.ManagerApplication - Started ManagerApplication in 13.577 seconds (JVM running for 15.774)
16:17:04.911 [34mINFO [0;39m [main] com.gl.ManagerApplication -                                                   
(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     
   _____ __  ______________________________       
  / ___// / / / ____/ ____/ ____/ ___/ ___/       
  \__ \/ / / / /   / /   / __/  \__ \\__ \  
 ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        
/____/\____/\____/\____/_____//____/____/      

16:17:05.446 [34mINFO [0;39m [RMI TCP Connection(5)-************] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:17:05.446 [34mINFO [0;39m [RMI TCP Connection(5)-************] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
16:17:05.448 [34mINFO [0;39m [RMI TCP Connection(5)-************] o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
16:17:15.720 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"anchorId":2,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0,"speechRate":0,"text":"<break time=\"6.6s\"></break>2334423424","userId":0,"volume":100}
16:17:15.767 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.opus.service.WorkService - bgm=null
16:17:15.786 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.opus.service.WorkService - 转换后的text=<break time="6.6s"></break>2334423424
16:17:15.816 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.s.o.c.c.SpeechSynthesizerRequest - accessToken = 927acd98e8884db1bc1ee345ea69e214
16:17:16.411 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.s.o.c.c.SpeechSynthesizerRequest - tts start latency 8 ms
16:17:16.583 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.594 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.596 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.597 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.598 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.600 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.601 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.603 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.604 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.615 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.616 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.617 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.619 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.620 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.621 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.622 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.624 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.626 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.627 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.629 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.630 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.630 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.631 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.632 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.633 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.634 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.636 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.637 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.638 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.639 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.640 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.641 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.641 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.642 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.643 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.644 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.645 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.646 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.647 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.647 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.648 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.649 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.875 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.877 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.878 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.879 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.880 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.883 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.884 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.885 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}53accded7cdc42ea891b8f56d6bae6c9
16:17:16.886 [34mINFO [0;39m [nioEventLoopGroup-4-1] c.g.s.o.c.c.SpeechSynthesizerRequest - name: SynthesisCompleted, status: 20000000, output file :C:\Users\<USER>\Desktop\my\11\faacedee-0d6c-4a57-ba38-24e6e5e5896e.mp3
16:17:18.978 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.s.o.c.c.SpeechSynthesizerRequest - tts stop latency 2575 ms
16:17:18.978 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.s.o.c.c.SpeechSynthesizerRequest - url = C:\Users\<USER>\Desktop\my\11\b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3
16:17:22.616 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.audio - Reading file:pathC:\Users\<USER>\Desktop\my\11\compound\b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3:abs:C:\Users\<USER>\Desktop\my\11\compound\b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3
16:17:22.663 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.audio - No ids3v11 tag found
16:17:22.663 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.audio - No id3v1 tag found
16:17:22.663 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.audio - Attempting to read id3v2tags
16:17:22.663 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.id3 - ByteBuffer pos:0:limit67:cap67
16:17:22.663 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.id3 - b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3:Reading ID3v24 tag
16:17:22.663 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.id3 - b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3:Reading tag from file size set in header is57
16:17:22.720 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.id3 - Reading body forTENC:12
16:17:22.720 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.datatype - Read NumberFixedlength:3
16:17:22.720 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.datatype - Read SizeTerminatedString:Lavf52.1.0  size:11
16:17:22.728 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.id3 - Reading body forTSSE:15
16:17:22.729 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.datatype - Read NumberFixedlength:3
16:17:22.729 [34mINFO [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.datatype - Read SizeTerminatedString:Lavf60.16.100  size:14
16:17:22.729 [31mWARN [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.id3 - b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3:No space to find another frame:
16:17:22.731 [31mWARN [0;39m [http-nio-8770-exec-2] org.jaudiotagger.tag.id3 - b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3:Invalid Frame:b3bb2909-b6e2-47d6-a4c5-60bbc45de4e1.mp3:No space to find another frame
16:17:32.636 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"anchorId":8,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0,"speechRate":0,"text":"3123321321321","userId":0,"volume":100}
16:17:32.646 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.opus.service.WorkService - bgm=null
16:17:32.648 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.service.opus.service.WorkService - 转换后的text=3123321321321
16:17:32.657 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.s.o.c.c.SpeechSynthesizerRequest - accessToken = 927acd98e8884db1bc1ee345ea69e214
16:17:32.797 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.s.o.c.c.SpeechSynthesizerRequest - tts start latency 0 ms
16:17:32.938 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:32.939 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:32.941 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.039 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.040 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.041 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.069 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.070 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.072 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.131 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.133 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.135 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.216 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.219 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.221 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.222 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.224 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.224 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.247 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.248 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.249 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - MetaInfo event:{}c63facffebdc42aa8df2fb6726f531cf
16:17:33.253 [34mINFO [0;39m [nioEventLoopGroup-5-1] c.g.s.o.c.c.SpeechSynthesizerRequest - name: SynthesisCompleted, status: 20000000, output file :C:\Users\<USER>\Desktop\my\11\e7cedf3b-14a4-4fc5-9976-110988c18b7d.mp3
16:17:34.113 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.s.o.c.c.SpeechSynthesizerRequest - tts stop latency 1316 ms
16:17:34.114 [34mINFO [0;39m [http-nio-8770-exec-3] c.g.s.o.c.c.SpeechSynthesizerRequest - url = C:\Users\<USER>\Desktop\my\11\b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3
16:17:35.097 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.audio - Reading file:pathC:\Users\<USER>\Desktop\my\11\compound\b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3:abs:C:\Users\<USER>\Desktop\my\11\compound\b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3
16:17:35.099 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.audio - No ids3v11 tag found
16:17:35.099 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.audio - No id3v1 tag found
16:17:35.099 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.audio - Attempting to read id3v2tags
16:17:35.101 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.id3 - ByteBuffer pos:0:limit67:cap67
16:17:35.102 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.id3 - b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3:Reading ID3v24 tag
16:17:35.102 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.id3 - b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3:Reading tag from file size set in header is57
16:17:35.102 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.id3 - Reading body forTENC:12
16:17:35.106 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.datatype - Read NumberFixedlength:3
16:17:35.107 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.datatype - Read SizeTerminatedString:Lavf52.1.0  size:11
16:17:35.107 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.id3 - Reading body forTSSE:15
16:17:35.109 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.datatype - Read NumberFixedlength:3
16:17:35.109 [34mINFO [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.datatype - Read SizeTerminatedString:Lavf60.16.100  size:14
16:17:35.111 [31mWARN [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.id3 - b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3:No space to find another frame:
16:17:35.111 [31mWARN [0;39m [http-nio-8770-exec-3] org.jaudiotagger.tag.id3 - b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3:Invalid Frame:b9bf4010-e1a8-4e87-ad00-61c904310d8c.mp3:No space to find another frame
16:17:46.410 [34mINFO [0;39m [http-nio-8770-exec-4] c.g.service.opus.service.WorkService - Anchor dto={"afterDelay":0,"beforeDelay":0,"bgmCenterVolum":1.0,"isEmotion":0,"isHeighVoice":0,"pitchRate":0}
16:17:46.422 [1;31mERROR[0;39m [http-nio-8770-exec-4] c.g.f.e.h.GlobalExceptionHandler - The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
org.springframework.dao.InvalidDataAccessApiUsageException: The given id must not be null!; nested exception is java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:374)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:235)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy198.findById(Unknown Source)
	at com.gl.service.opus.service.WorkService.dialog(WorkService.java:681)
	at com.gl.service.opus.controller.WorkController.dialog(WorkController.java:197)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:228)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.gl.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:41)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:190)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:163)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1723)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The given id must not be null!
	at org.springframework.util.Assert.notNull(Assert.java:201)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findById(SimpleJpaRepository.java:297)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:529)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:599)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:163)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:80)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 107 common frames omitted
16:41:42.119 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:41:42.144 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:41:42.144 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:41:42.191 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,usage_scenario,voice_url,url from dub_anchor ]
16:41:42.191 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select * from dub_background_music_type]
16:41:42.191 [39mDEBUG[0;39m [http-nio-8770-exec-9] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name from dub_template_type where del_status != 1  ]
16:41:42.206 [39mDEBUG[0;39m [http-nio-8770-exec-2] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,nickname as nicknames,phone from dub_wechat_user ]
16:41:42.211 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing SQL query [select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ]
16:41:42.211 [34mINFO [0;39m [http-nio-8770-exec-7] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:41:42.261 [34mINFO [0;39m [http-nio-8770-exec-7] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:41:42.277 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:41:42.277 [39mDEBUG[0;39m [http-nio-8770-exec-7] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [SELECT COUNT(1) FROM dub_voice_work vw
LEFT JOIN dub_voice_packet vp 
ON vp.voice_work_id = vw.id
LEFT JOIN dub_anchor a 
ON a.id = vw.anchor_id
LEFT JOIN dub_background_music bm
ON bm.id = vw.background_music_id
LEFT JOIN dub_wechat_user wu 
ON wu.id = vw.user_id
LEFT JOIN dub_shop s on s.id = vw.shop_id 
WHERE vw.del_status != 1
 AND s.id in (22)]
16:43:46.539 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:46.572 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:46.572 [34mINFO [0;39m [http-nio-8770-exec-6] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:46.587 [34mINFO [0;39m [http-nio-8770-exec-6] c.g.s.device.service.DeviceService - 查询设备列表，sql:SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and  ( create_id = ?  or   s.id in (?)
 )
16:43:46.587 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:43:46.587 [39mDEBUG[0;39m [http-nio-8770-exec-6] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT d.id,d.name,d.sn,wu.nickname,wu.phone, (select count(1) from dub_voice_work dvw LEFT JOIN dub_device_voice ddv ON dvw.id = ddv.voice_work_id LEFT JOIN dub_device dd ON ddv.device_id = dd.id WHERE dd.id = d.id and dvw.del_status !=1 and ddv.del_status != 1 ) as workCount, d.status,d.bind_status,d.use_status,d.create_time,s.shop_name,s.id shopId  FROM dub_device d  LEFT JOIN dub_user_shop_ref sr ON sr.shop_id = d.shop_id and sr.role = 1 LEFT JOIN dub_wechat_user wu ON wu.id = sr.user_id LEFT JOIN dub_shop s on s.id=d.shop_id WHERE d.del_status != 1  and  ( create_id = ?  or   s.id in (?)
 )) t]
16:43:46.806 [34mINFO [0;39m [http-nio-8770-exec-2] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:46.806 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:43:46.806 [34mINFO [0;39m [http-nio-8770-exec-2] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:43:55.613 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:55.628 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:43:55.628 [34mINFO [0;39m [http-nio-8770-exec-8] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:43:55.628 [34mINFO [0;39m [http-nio-8770-exec-10] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:55.628 [34mINFO [0;39m [http-nio-8770-exec-10] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:55.628 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:43:55.628 [39mDEBUG[0;39m [http-nio-8770-exec-10] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 AND s.id in (22)) t]
16:43:58.639 [34mINFO [0;39m [http-nio-8770-exec-1] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:58.655 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:43:58.655 [34mINFO [0;39m [http-nio-8770-exec-1] c.g.service.shop.service.ShopService - 查询门店列表，list:[Shop(id=22, parentId=0, type=1, shopName=1, createUserId=0, createTime=2025-07-01 17:04:36.0, status=1)]
16:43:58.655 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:58.655 [34mINFO [0;39m [http-nio-8770-exec-4] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:43:58.671 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:43:58.672 [39mDEBUG[0;39m [http-nio-8770-exec-4] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
 o.id, 
 o.out_trade_no ,
  p.`name` packagesName,
  o.amount,
  u.nickname,
  u.phone,
  s.shop_name,
  d.`name` deviceName,
  d.sn deviceSn,
  o.`status`,
  o.create_time,
  o.time_expire,
  0 invoiceStatus 
FROM
  dub_order o
  LEFT JOIN dub_device d ON d.id = o.device_id
  LEFT JOIN dub_shop s ON s.id = o.shop_id
  LEFT JOIN dub_paid_packages p ON p.id = o.package_id
  LEFT JOIN dub_wechat_user u ON u.id = o.user_id  WHERE 1=1  and ( o.user_id = 0 or s.id in (22) ) ) t]
16:44:11.791 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:44:11.794 [34mINFO [0;39m [http-nio-8770-exec-8] com.gl.util.GetShopRefUtil - 微信用户，需要通过关联门店过滤数据 siteId = 0
16:44:11.795 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL query
16:44:11.795 [39mDEBUG[0;39m [http-nio-8770-exec-8] o.s.jdbc.core.JdbcTemplate - Executing prepared SQL statement [select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`sn`)) deviceNames,
 group_concat(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  and s.id in (?)
 GROUP BY b.id ) t]
