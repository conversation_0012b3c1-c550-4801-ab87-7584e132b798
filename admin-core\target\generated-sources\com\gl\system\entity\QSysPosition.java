package com.gl.system.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysPosition is a Querydsl query type for SysPosition
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QSysPosition extends EntityPathBase<SysPosition> {

    private static final long serialVersionUID = 1929576718L;

    public static final QSysPosition sysPosition = new QSysPosition("sysPosition");

    public final com.gl.framework.entity.QIdEntity _super = new com.gl.framework.entity.QIdEntity(this);

    public final StringPath code = createString("code");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> createUserId = createNumber("createUserId", Long.class);

    public final StringPath description = createString("description");

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final StringPath name = createString("name");

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public final NumberPath<Long> updateUserId = createNumber("updateUserId", Long.class);

    public QSysPosition(String variable) {
        super(SysPosition.class, forVariable(variable));
    }

    public QSysPosition(Path<? extends SysPosition> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysPosition(PathMetadata metadata) {
        super(SysPosition.class, metadata);
    }

}

