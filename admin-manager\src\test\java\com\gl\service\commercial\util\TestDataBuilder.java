package com.gl.service.commercial.util;

import com.gl.framework.web.response.Result;
import com.gl.service.commercial.vo.CommercialVo;
import com.gl.service.commercial.vo.ExcelCommercial;
import com.gl.service.commercial.vo.dto.CommercialDto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 测试数据构建工具类
 * 为单元测试提供标准化的测试数据构建方法
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
public class TestDataBuilder {

    /**
     * 创建标准的CommercialDto测试数据
     */
    public static CommercialDto createStandardCommercialDto() {
        CommercialDto dto = new CommercialDto();
        dto.setPageNumber(0);
        dto.setPageSize(10);
        dto.setShopName("测试店铺");
        dto.setShopId(1L);
        dto.setSearchCondition("测试用户");
        dto.setBeginTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");
        return dto;
    }

    /**
     * 创建空的CommercialDto测试数据
     */
    public static CommercialDto createEmptyCommercialDto() {
        return new CommercialDto();
    }

    /**
     * 创建包含完整参数的CommercialDto测试数据
     */
    public static CommercialDto createFullCommercialDto() {
        CommercialDto dto = new CommercialDto();
        dto.setPageNumber(1);
        dto.setPageSize(20);
        dto.setShopName("完整测试店铺名称");
        dto.setShopId(999L);
        dto.setSearchCondition("完整搜索条件");
        dto.setBeginTime("2024-01-01 00:00:00");
        dto.setEndTime("2024-12-31 23:59:59");
        return dto;
    }

    /**
     * 创建标准的CommercialVo列表测试数据
     */
    public static List<CommercialVo> createStandardCommercialVoList() {
        List<CommercialVo> list = new ArrayList<>();
        
        CommercialVo vo1 = new CommercialVo();
        vo1.setId(1L);
        vo1.setNickname("测试用户1");
        vo1.setPhone("13800138001");
        vo1.setGender(1);
        vo1.setAuthTime(new Date());
        vo1.setShopNames("测试店铺1");
        vo1.setDeviceNames("设备1");
        vo1.setDeviceNum(2);
        list.add(vo1);

        CommercialVo vo2 = new CommercialVo();
        vo2.setId(2L);
        vo2.setNickname("测试用户2");
        vo2.setPhone("13800138002");
        vo2.setGender(2);
        vo2.setAuthTime(new Date());
        vo2.setShopNames("测试店铺2");
        vo2.setDeviceNames("设备2");
        vo2.setDeviceNum(1);
        list.add(vo2);

        return list;
    }

    /**
     * 创建包含空值的CommercialVo列表测试数据
     */
    public static List<CommercialVo> createCommercialVoListWithNulls() {
        List<CommercialVo> list = new ArrayList<>();
        
        CommercialVo vo = new CommercialVo();
        vo.setId(1L);
        vo.setNickname(null);
        vo.setPhone(null);
        vo.setGender(1);
        vo.setAuthTime(new Date());
        vo.setShopNames(null);
        vo.setDeviceNames(null);
        vo.setDeviceNum(0);
        list.add(vo);

        return list;
    }

    /**
     * 创建成功的Result测试数据
     */
    public static Result createSuccessResult(List<CommercialVo> data) {
        Result result = Result.success();
        result.addData("total", (long) data.size());
        result.addData("result", data);
        return result;
    }

    /**
     * 创建空结果的Result测试数据
     */
    public static Result createEmptyResult() {
        Result result = Result.success();
        result.addData("total", 0L);
        result.addData("result", new ArrayList<>());
        return result;
    }

    /**
     * 创建失败的Result测试数据
     */
    public static Result createFailResult(String message) {
        return Result.fail(message);
    }

    /**
     * 创建ExcelCommercial测试数据
     */
    public static ExcelCommercial createExcelCommercial() {
        ExcelCommercial excel = new ExcelCommercial();
        excel.setNickname("测试用户");
        excel.setPhone("13800138000");
        excel.setGender("男");
        excel.setAuthTime("2024-01-01 12:00:00");
        excel.setShopNames("测试店铺");
        excel.setDeviceNames("测试设备");
        return excel;
    }

    /**
     * 创建Long类型的店铺ID列表
     */
    public static List<Long> createShopRefList() {
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(2L);
        list.add(3L);
        return list;
    }

    /**
     * 创建空的店铺ID列表
     */
    public static List<Long> createEmptyShopRefList() {
        return new ArrayList<>();
    }
}
