package com.gl.service.opus.entity;

import javax.persistence.*;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-05-30-16:11
 */
@Entity
@Table(name = "follow_anchor")
public class FollowAnchor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "user_id", nullable = true)
    private Long userId; // 小程序openid

    @Basic
    @Column(name = "anchor_id", nullable = true)
    private Long anchorId; // 小程序openid

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getAnchorId() {
        return anchorId;
    }

    public void setAnchorId(Long anchorId) {
        this.anchorId = anchorId;
    }
}
