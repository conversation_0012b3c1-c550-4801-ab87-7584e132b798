package com.gl.service.opus.compound.compound;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.commons.enums.PlatFormStatusEnum;
import com.gl.commons.enums.PlatFormTypeEnum;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.framework.common.util.StringUtils;
import com.gl.redis.RedisService;
import com.gl.service.opus.compound.entity.TtsAddRequest;
import com.gl.service.opus.entity.PlatformConfig;
import com.gl.service.opus.repository.PlatformConfigRepository;
import com.gl.service.opus.utils.FfmpegUtil;
import com.gl.service.opus.utils.FileUtil;
import com.gl.service.opus.utils.LineInsertUtils;
import com.gl.service.opus.utils.StopUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.UUID;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-09-13:49
 */
@Service
public class DyDubbingStrategy implements DubbingStrategy {
    @Resource
    private RestTemplate restTemplate;

    private static final Logger logger = LoggerFactory.getLogger(DyDubbingStrategy.class);
    @Autowired
    private RedisService redisService;

    @Autowired
    private PlatformConfigRepository platformConfigRepository;
    private final String ordinaryAdd = "https://openspeech.bytedance.com/api/v1/tts_async/submit";
    private final String emotionAdd = "https://openspeech.bytedance.com/api/v1/tts_async_with_emotion/submit";

    private final String ordinaryQuery = "https://openspeech.bytedance.com/api/v1/tts_async/query?appid=%s&task_id=%s";

    private final String emotionQuery = "https://openspeech.bytedance.com/api/v1/tts_async_with_emotion/query?appid=%s&task_id=%s";

    private String getAudio(String taskId, Integer isEmotion,String accessToken,String appId) {
        try {
            redisService.incrPyLockNum(taskId);
            String lockNum = redisService.getPyLockNum(taskId);
            if (Integer.parseInt(lockNum) > 70) {
                return "";
            }
            String host = "";
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer;" + accessToken);
            if (isEmotion == 1) {
                headers.set("Resource-Id", "volc.tts_async.emotion");
                host = String.format(emotionQuery, appId, taskId);
            } else {
                headers.set("Resource-Id", "volc.tts_async.default");
                host = String.format(ordinaryQuery, appId, taskId);
            }
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(host, HttpMethod.GET, httpEntity, String.class);
            JSONObject jsonObject = JSONObject.parseObject(response.getBody().toString());
            logger.info("抖音合成结果{}", response.getBody().toString());
            if (jsonObject.getString("audio_url") != null) {
                return jsonObject.getString("audio_url");
            } else {
                Thread.sleep(200);
                return getAudio(taskId, isEmotion,accessToken, appId);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {
        List<PlatformConfig> platformConfigs = platformConfigRepository.findByPlatformTypeAndStatus(PlatFormTypeEnum.DY_DUBBING.name(),
                PlatFormStatusEnum.ENABLED.ordinal());
        PlatformConfig platformConfig = platformConfigs.get(0);
        String appId = platformConfig.getAppId();
        String accessToken = platformConfig.getAccessToken();
        Integer isEmotion = dto.getIsEmotion();
        if (dto.getText().indexOf("<break") != -1) {
            dto.setText(StopUtils.breakRename(dto.getText()));
        }
        TtsAddRequest ttsAddRequest = new TtsAddRequest();
        ttsAddRequest.setAppId(appId);
        ttsAddRequest.setReqId(UUID.randomUUID().toString());
        //发音人
        ttsAddRequest.setVoiceType(dto.getVoice());
        if (dto.getVolume() != null) {
            Double volume = LineInsertUtils.mapRange(dto.getVolume(), 0,
                    100, 0.2, 2.9);
            //音量
            ttsAddRequest.setVolume(volume);
        }
        if (dto.getPitchRate() != null) {
            //语调，范围是-500~500，可选，默认是0
            Double pirchRate = LineInsertUtils.mapRange(dto.getPitchRate() - 230, -500,
                    500, 0.3, 2.9);
            ttsAddRequest.setPitch(pirchRate);
        }
        if (dto.getSpeechRate() != null) {
            //语速，范围是-500~500，默认是0
            Double speechRate = LineInsertUtils.mapRange(dto.getSpeechRate() - 200, -500,
                    500, 0.2, 2.9);
            ttsAddRequest.setSpeed(speechRate);
        }
        ttsAddRequest.setFormat("mp3");
        String tempFileName = dto.getUrl() + FileUtil.getFileNewName(".mp3");
        ttsAddRequest.setSampleRate(24000);
        ttsAddRequest.setText(dto.getText());
        ttsAddRequest.setLanguage("cn");
        ttsAddRequest.setEnableSubtitle(2);
        ttsAddRequest.setVoiceType(dto.getVoice());
        HttpHeaders headers = new HttpHeaders();
        ResponseEntity<String> response = null;
        if (isEmotion == 1) {
            ttsAddRequest.setStyle("surprise");
            headers.set("Authorization", "Bearer;" + accessToken);
            headers.set("Resource-Id", "volc.tts_async.emotion");
            HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(ttsAddRequest), headers);
            response = restTemplate.exchange(emotionAdd, HttpMethod.POST, httpEntity, String.class);
        } else {
            headers.set("Authorization", "Bearer;" + accessToken);
            headers.set("Resource-Id", "volc.tts_async.default");
            ttsAddRequest.setStyle(dto.getEmotion());
            HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(ttsAddRequest), headers);
            response = restTemplate.exchange(ordinaryAdd, HttpMethod.POST, httpEntity, String.class);
        }
        JSONObject jsonObject = JSONObject.parseObject(response.getBody().toString());
        Thread.sleep(200);
        String url = getAudio(jsonObject.getString("task_id"), isEmotion,accessToken, appId);
        File source = FileUtil.downloadFile(url, tempFileName);
        File file = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
        if (!StringUtils.isEmpty(dto.getBgm())) {
            String bgmFileName = dto.getUrl() + FileUtil.getFileNewName(".wav");
            File bgmFile = FileUtil.taiseng(dto.getBgm(), bgmFileName, dto.getBugRate());
            File outFile = new File(dto.getUrl() + FileUtil.getFileNewName(".wav"));
            FfmpegUtil.mixBgm(source, bgmFile, outFile, dto.getBeforeDelay(), dto.getAfterDelay(), dto.getBgmCenterVolum());
            if (dto.getIsHeighVoice() == 1) {
                FileUtil.coverToMp3Heigh(outFile, file);
            } else {
                FileUtil.coverToMp3(outFile, file);
            }
        } else {
            if (dto.getIsHeighVoice() == 1) {
                FileUtil.coverToMp3Heigh(source, file);
            } else {
                FileUtil.coverToMp3(source, file);
            }
        }
        return file.getAbsolutePath();
    }
}
