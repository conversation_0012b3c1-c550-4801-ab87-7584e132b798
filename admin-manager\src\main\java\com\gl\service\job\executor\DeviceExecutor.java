package com.gl.service.job.executor;

import com.gl.service.job.service.DeviceJobService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

/**
 * @author: duanjinze
 * @date: 2022/11/15 13:38
 * @version: 1.0
 */
public class DeviceExecutor {
    private static final Logger log = LoggerFactory.getLogger(DeviceExecutor.class);

    @Resource
    private DeviceJobService deviceJobService;

    /**
     * 设备更新状态
     * 每隔1分钟执行一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void followTask() {
        try {
            deviceJobService.work();
        } catch (Exception e) {
            log.error("{}设备更新状态异常", e.getMessage(), e);
        }
    }

}
