package com.gl.system.entity;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.gl.framework.entity.IdEntity;

/**
 * 用户与部门关系
 */
@Entity
@Table(name = "sys_user_dept")
public class SysUserDept extends IdEntity {
	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 部门ID
	 */
	private Long deptId;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

}
