package com.gl.system.repository;

import com.gl.system.entity.SysRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface SysRoleRepository extends JpaRepository<SysRole, Long>, JpaSpecificationExecutor<SysRole> {

	SysRole findByRoleKey(String roleKey);

	SysRole findByRoleName(String roleName);

	List<SysRole> findByIdIn(List<Long> ids);
}
