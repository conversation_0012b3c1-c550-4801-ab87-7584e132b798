package com.gl.framework.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目阿里云OSS配置
 */
@Component
@ConfigurationProperties(prefix = "aliyun-oss")
public class AliyunOssProperties {

	/**
	 * OSS 域名
	 */
	private String endpoint;

	/**
	 * OSS accessKeyId
	 */
	private String accessKeyId;

	/**
	 * OSS accessKeySecret
	 */
	private String accessKeySecret;

	/**
	 * OSS 私有bucket
	 */
	private String privateBucketName;

	/**
	 * OSS 私有bucket访问域名
	 */
	private String privateBucketHost;

	/**
	 * OSS 公有bucket
	 */
	private String publicBucketName;

	/**
	 * OSS 公有bucket访问域名
	 */
	private String publicBucketHost;

	/**
	 * 签名有效期（单位：天）
	 */
	private Integer expireTime;

	public String getEndpoint() {
		return endpoint;
	}

	public void setEndpoint(String endpoint) {
		this.endpoint = endpoint;
	}

	public String getAccessKeyId() {
		return accessKeyId;
	}

	public void setAccessKeyId(String accessKeyId) {
		this.accessKeyId = accessKeyId;
	}

	public String getAccessKeySecret() {
		return accessKeySecret;
	}

	public void setAccessKeySecret(String accessKeySecret) {
		this.accessKeySecret = accessKeySecret;
	}

	public String getPrivateBucketName() {
		return privateBucketName;
	}

	public void setPrivateBucketName(String privateBucketName) {
		this.privateBucketName = privateBucketName;
	}

	public String getPrivateBucketHost() {
		return privateBucketHost;
	}

	public void setPrivateBucketHost(String privateBucketHost) {
		this.privateBucketHost = privateBucketHost;
	}

	public String getPublicBucketName() {
		return publicBucketName;
	}

	public void setPublicBucketName(String publicBucketName) {
		this.publicBucketName = publicBucketName;
	}

	public String getPublicBucketHost() {
		return publicBucketHost;
	}

	public void setPublicBucketHost(String publicBucketHost) {
		this.publicBucketHost = publicBucketHost;
	}

	public Integer getExpireTime() {
		return expireTime;
	}

	public void setExpireTime(Integer expireTime) {
		this.expireTime = expireTime;
	}

}
