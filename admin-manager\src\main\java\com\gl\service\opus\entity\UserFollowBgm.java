package com.gl.service.opus.entity;

import com.gl.framework.entity.IdEntity;
import lombok.Data;

import javax.persistence.*;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-05-30-16:11
 */
@Data
@Entity
@Table(name = "user_follow_bgm")
public class UserFollowBgm {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "user_id", nullable = true)
    private Long userId; // 小程序openid

    @Basic
    @Column(name = "bgm_id", nullable = true)
    private Long bgmId; // 小程序openid
}
