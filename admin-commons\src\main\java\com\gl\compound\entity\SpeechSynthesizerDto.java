package com.gl.compound.entity;

import lombok.Data;

@Data
public class SpeechSynthesizerDto {
    /**
     * 主播id
     */
    private Long anchorId;
    /**
     * appkey
     */
    private String appKey;

    /**
     * AccessKey Id
     */
    private String accessKeyId;

    /**
     * secret
     */
    private String secret;

    /**
     * 配音人
     */
    private String voice;

    /**
     * 语调
     */
    private Integer pitchRate = 0;

    /**
     * 语速
     */
    private Integer speechRate;

    /**
     * 音量
     */
    private Integer volume;

    /**
     * 文本
     */
    private String text;

    /**
     * 背景音乐
     */
    private String bgm;

    /**
     * 背景音乐音量
     */
    private Integer bugRate;

    private String url;

    /**
     * 采样率
     */
    private Integer sampleRate;

    private String type;

    private Long userId;

    private Integer isEmotion = 0;

    private String emotion;

    private String voiceContent;

    private Integer beforeDelay = 0;

    private Integer afterDelay = 0;

    private Double bgmCenterVolum = 1.0;

    private Integer isHeighVoice = 0;

    private String promptPath;

}
