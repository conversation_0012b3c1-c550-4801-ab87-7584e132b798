package com.gl.service.shop.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date: 2025/3/2
 * @description: 店铺表
 * CREATE TABLE `dub_user_shop_ref` (
 *   `id` bigint NOT NULL AUTO_INCREMENT,
 *   `user_id` bigint DEFAULT NULL,
 *   `shop_id` bigint DEFAULT NULL,
 *   `role` int DEFAULT NULL COMMENT '角色1管理员，2店员',
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户与门店的关系';
 */
@Data
@Entity
@Table(name = "dub_user_shop_ref")
public class ShopUserRef {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Basic
    @Column(name = "user_id")
    private Long userId;

    @Basic
    @Column(name = "shop_id")
    private Long shopId;

    /**
     * 角色 1管理员，2店员
     */
    @Basic
    @Column(name = "role")
    private Integer role;
}
