package com.gl.service.packet.util;

import com.gl.service.device.vo.DeviceVo;
import com.gl.service.opus.entity.VoicePacket;
import com.gl.service.opus.entity.VoiceWork;
import com.gl.service.packet.vo.VoicePacketVo;
import com.gl.service.packet.vo.dto.VoicePacketDto;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 语音包测试数据构建工具类
 * 用于生成大量测试数据，支持数据库性能测试
 * 
 * @author: 测试开发
 * @date: 2025-07-15
 * @version: 1.0
 */
public class VoicePacketTestDataBuilder {

    private static final String[] VOICE_NAMES = {
        "欢迎语音", "提示音", "警告音", "通知音", "背景音乐", 
        "开机音", "关机音", "错误提示", "成功提示", "操作音效",
        "系统音", "铃声", "闹钟音", "消息音", "来电音",
        "按键音", "滴滴音", "嘟嘟音", "叮咚音", "咔嚓音"
    };

    private static final String[] USER_NICKNAMES = {
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十",
        "郑一", "王二", "冯三", "陈四", "褚五", "卫六", "蒋七", "沈八",
        "韩九", "杨十", "朱一", "秦二", "尤三", "许四", "何五", "吕六",
        "施七", "张八", "孔九", "曹十", "严一", "华二", "金三", "魏四"
    };

    private static final String[] SHOP_NAMES = {
        "总店", "分店1", "分店2", "分店3", "分店4", "分店5",
        "东区店", "西区店", "南区店", "北区店", "中心店", "旗舰店",
        "体验店", "专卖店", "直营店", "加盟店", "概念店", "形象店"
    };

    private static final String[] DEVICE_NAMES = {
        "智能音箱", "蓝牙音响", "无线耳机", "有线耳机", "麦克风",
        "扩音器", "录音笔", "播放器", "收音机", "音响设备",
        "语音设备", "通讯设备", "广播设备", "音频设备", "声音设备"
    };

    /**
     * 创建单个VoicePacketDto测试数据
     */
    public static VoicePacketDto createVoicePacketDto() {
        VoicePacketDto dto = new VoicePacketDto();
        dto.setId(generateRandomId());
        dto.setShopId(generateRandomShopId());
        dto.setSearchCondition(generateRandomSearchCondition());
        dto.setPageNumber(0);
        dto.setPageSize(10);
        return dto;
    }

    /**
     * 创建指定数量的VoicePacketDto列表
     */
    public static List<VoicePacketDto> createVoicePacketDtoList(int count) {
        List<VoicePacketDto> dtoList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            VoicePacketDto dto = createVoicePacketDto();
            dto.setId((long) (i + 1));
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 创建单个VoicePacketVo测试数据
     */
    public static VoicePacketVo createVoicePacketVo() {
        VoicePacketVo vo = new VoicePacketVo();
        vo.setId(generateRandomId());
        vo.setVoiceWorkId(generateRandomId());
        vo.setName(generateRandomVoiceName());
        vo.setVoiceTime(generateRandomVoiceTime());
        vo.setFileUrl(generateRandomFileUrl());
        vo.setNickname(generateRandomNickname());
        vo.setPhone(generateRandomPhone());
        vo.setCreateTime(generateRandomDate());
        vo.setShopName(generateRandomShopName());
        return vo;
    }

    /**
     * 创建指定数量的VoicePacketVo列表
     */
    public static List<VoicePacketVo> createVoicePacketVoList(int count) {
        List<VoicePacketVo> voList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            VoicePacketVo vo = createVoicePacketVo();
            vo.setId((long) (i + 1));
            vo.setName(generateRandomVoiceName() + (i + 1));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 创建单个DeviceVo测试数据
     */
    public static DeviceVo createDeviceVo() {
        DeviceVo vo = new DeviceVo();
        vo.setId(generateRandomId());
        vo.setName(generateRandomDeviceName());
        vo.setSn(generateRandomSn());
        vo.setUserId(generateRandomId());
        vo.setVolume(generateRandomVolume());
        vo.setStatus(generateRandomStatus());
        vo.setBindStatus(1); // 已绑定
        vo.setUseStatus(1); // 使用中
        vo.setDelStatus(0); // 未删除
        vo.setCreateId(generateRandomId());
        vo.setNickname(generateRandomNickname());
        vo.setPhone(generateRandomPhone());
        return vo;
    }

    /**
     * 创建指定数量的DeviceVo列表
     */
    public static List<DeviceVo> createDeviceVoList(int count) {
        List<DeviceVo> voList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            DeviceVo vo = createDeviceVo();
            vo.setId((long) (i + 1));
            vo.setName(generateRandomDeviceName() + (i + 1));
            vo.setSn("SN" + String.format("%06d", i + 1));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 创建单个VoicePacket实体测试数据
     */
    public static VoicePacket createVoicePacketEntity() {
        VoicePacket entity = new VoicePacket();
        entity.setId(generateRandomId());
        entity.setVoiceWorkId(generateRandomId());
        entity.setName(generateRandomVoiceName());
        entity.setVoiceTime(generateRandomVoiceTime());
        entity.setFileUrl(generateRandomFileUrl());
        entity.setShopId(generateRandomShopId());
        return entity;
    }

    /**
     * 创建指定数量的VoicePacket实体列表
     */
    public static List<VoicePacket> createVoicePacketEntityList(int count) {
        List<VoicePacket> entityList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            VoicePacket entity = createVoicePacketEntity();
            entity.setId((long) (i + 1));
            entity.setName(generateRandomVoiceName() + (i + 1));
            entityList.add(entity);
        }
        return entityList;
    }

    /**
     * 创建单个VoiceWork实体测试数据
     */
    public static VoiceWork createVoiceWorkEntity() {
        VoiceWork entity = new VoiceWork();
        entity.setId(generateRandomId());
        entity.setTitle(generateRandomVoiceName());
        entity.setContent("这是一段测试语音内容，用于测试语音包功能。");
        entity.setVoiceId(generateRandomId());
        entity.setAnchorId(generateRandomId());
        entity.setVoiceTime(generateRandomVoiceTime());
        entity.setSpeed(generateRandomSpeed());
        entity.setVolume(generateRandomVolume());
        entity.setPitch(generateRandomPitch());
        entity.setBackgroundMusicVolume(generateRandomVolume());
        entity.setBackgroundMusicId(generateRandomId());
        entity.setSampleRate(generateRandomSampleRate());
        entity.setUserId(generateRandomId());
        entity.setDelStatus(0); // 未删除
        entity.setCreateTime(generateRandomDate());
        entity.setShopId(generateRandomShopId());
        return entity;
    }

    /**
     * 创建指定数量的VoiceWork实体列表
     */
    public static List<VoiceWork> createVoiceWorkEntityList(int count) {
        List<VoiceWork> entityList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            VoiceWork entity = createVoiceWorkEntity();
            entity.setId((long) (i + 1));
            entity.setTitle(generateRandomVoiceName() + (i + 1));
            entityList.add(entity);
        }
        return entityList;
    }

    /**
     * 创建大量测试数据用于性能测试
     */
    public static Map<String, Object> createLargeTestDataSet(int voicePacketCount, int deviceCount) {
        Map<String, Object> dataSet = new HashMap<>();
        
        // 创建大量语音包数据
        List<VoicePacketVo> voicePackets = createVoicePacketVoList(voicePacketCount);
        dataSet.put("voicePackets", voicePackets);
        
        // 创建大量设备数据
        List<DeviceVo> devices = createDeviceVoList(deviceCount);
        dataSet.put("devices", devices);
        
        // 创建大量实体数据
        List<VoicePacket> voicePacketEntities = createVoicePacketEntityList(voicePacketCount);
        dataSet.put("voicePacketEntities", voicePacketEntities);
        
        List<VoiceWork> voiceWorkEntities = createVoiceWorkEntityList(voicePacketCount);
        dataSet.put("voiceWorkEntities", voiceWorkEntities);
        
        // 添加统计信息
        dataSet.put("totalVoicePackets", voicePacketCount);
        dataSet.put("totalDevices", deviceCount);
        dataSet.put("createdAt", new Date());
        
        return dataSet;
    }

    // ==================== 私有辅助方法 ====================

    private static Long generateRandomId() {
        return ThreadLocalRandom.current().nextLong(1, 100000);
    }

    private static Long generateRandomShopId() {
        return ThreadLocalRandom.current().nextLong(1, 1000);
    }

    private static String generateRandomVoiceName() {
        return VOICE_NAMES[ThreadLocalRandom.current().nextInt(VOICE_NAMES.length)];
    }

    private static String generateRandomNickname() {
        return USER_NICKNAMES[ThreadLocalRandom.current().nextInt(USER_NICKNAMES.length)];
    }

    private static String generateRandomShopName() {
        return SHOP_NAMES[ThreadLocalRandom.current().nextInt(SHOP_NAMES.length)];
    }

    private static String generateRandomDeviceName() {
        return DEVICE_NAMES[ThreadLocalRandom.current().nextInt(DEVICE_NAMES.length)];
    }

    private static String generateRandomSearchCondition() {
        String[] conditions = {"测试", "语音", "音频", "设备", "用户"};
        return conditions[ThreadLocalRandom.current().nextInt(conditions.length)];
    }

    private static Integer generateRandomVoiceTime() {
        return ThreadLocalRandom.current().nextInt(10, 300); // 10-300秒
    }

    private static String generateRandomFileUrl() {
        return "test/voice" + ThreadLocalRandom.current().nextInt(1, 10000) + ".mp3";
    }

    private static String generateRandomPhone() {
        return "138" + String.format("%08d", ThreadLocalRandom.current().nextInt(10000000, 99999999));
    }

    private static Date generateRandomDate() {
        long now = System.currentTimeMillis();
        long randomTime = now - ThreadLocalRandom.current().nextLong(0, 365L * 24 * 60 * 60 * 1000); // 过去一年内
        return new Date(randomTime);
    }

    private static String generateRandomSn() {
        return "SN" + String.format("%08d", ThreadLocalRandom.current().nextInt(10000000, 99999999));
    }

    private static Integer generateRandomVolume() {
        return ThreadLocalRandom.current().nextInt(0, 101); // 0-100
    }

    private static Integer generateRandomStatus() {
        return ThreadLocalRandom.current().nextInt(0, 2); // 0离线 1在线
    }

    private static Integer generateRandomSpeed() {
        return ThreadLocalRandom.current().nextInt(50, 201); // 50-200
    }

    private static Integer generateRandomPitch() {
        return ThreadLocalRandom.current().nextInt(50, 201); // 50-200
    }

    private static Integer generateRandomSampleRate() {
        int[] rates = {8000, 16000, 22050, 44100, 48000};
        return rates[ThreadLocalRandom.current().nextInt(rates.length)];
    }
}
