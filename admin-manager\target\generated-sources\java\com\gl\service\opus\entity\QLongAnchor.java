package com.gl.service.opus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QLongAnchor is a Querydsl query type for LongAnchor
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QLongAnchor extends EntityPathBase<LongAnchor> {

    private static final long serialVersionUID = -1336732734L;

    public static final QLongAnchor longAnchor = new QLongAnchor("longAnchor");

    public final NumberPath<Integer> enableSubtitle = createNumber("enableSubtitle", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath name = createString("name");

    public final NumberPath<Integer> rhoticAccent = createNumber("rhoticAccent", Integer.class);

    public final NumberPath<Integer> sampleRate = createNumber("sampleRate", Integer.class);

    public final NumberPath<Integer> supportVoiceType = createNumber("supportVoiceType", Integer.class);

    public final StringPath type = createString("type");

    public final StringPath typeName = createString("typeName");

    public final StringPath url = createString("url");

    public final StringPath usageScenario = createString("usageScenario");

    public final NumberPath<Integer> vip = createNumber("vip", Integer.class);

    public final StringPath voiceName = createString("voiceName");

    public final StringPath voiceUrl = createString("voiceUrl");

    public QLongAnchor(String variable) {
        super(LongAnchor.class, forVariable(variable));
    }

    public QLongAnchor(Path<? extends LongAnchor> path) {
        super(path.getType(), path.getMetadata());
    }

    public QLongAnchor(PathMetadata metadata) {
        super(LongAnchor.class, metadata);
    }

}

