package com.gl.system.repository;

import com.gl.system.entity.SysPositionUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SysPositionUserRepository extends JpaRepository<SysPositionUser, Long>, JpaSpecificationExecutor<SysPositionUser> {

    List<SysPositionUser> findByUserId(Long userId);

    void deleteByUserId(Long userIdSS);

    @Transactional
    @Modifying
    @Query(value = "delete from SysPositionUser p where p.userId in (?1)")
    void deleteBatch(List<Long> userIds);
}

