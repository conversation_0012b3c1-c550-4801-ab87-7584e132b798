package com.gl.framework.config.oss;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.aliyun.oss.OSSClient;

@Configuration
public class OssConfig {
	/**
	 * OSS配置
	 */
	@Value("${ali.oss.endpoint}")
	private String endpoint;
	@Value("${ali.oss.accessKeyId}")
	private String accessKeyId;
	@Value("${ali.oss.accessKeySecret}")
	private String accessKeySecret;
	
	/**
	 * 对象存储桶名称
	 */
	@Value("${ali.oss.bucket.name}")
	private String ossBucketName;
	/**
	 * 桶路径
	 */
	@Value("${ali.oss.bucket.url}")
	private String bucketUrl;
	
	/**
	 * 图片下载临时目录
	 */
	@Value("${ali.oss.downFile.tempdir}")
	private String downFileTempdir;

	/**
	 * 语音包下载临时目录
	 */
	@Value("${ali.oss.downFile.packetdir}")
	private String downFilePacketdir;

	@Value("${ali.oss.bucket.path}")
	private String fristFilePath;
	/**
	 * 客户端
	 * 
	 * @return
	 * <AUTHOR>
	 * @date 2021年3月23日
	 */
	@Bean
	public OSSClient obsClient() {
		return new OSSClient(endpoint, accessKeyId, accessKeySecret);
	}
	
	public String getEndpoint() {
		return endpoint;
	}

	public String getAccessKeyId() {
		return accessKeyId;
	}

	public String getAccessKeySecret() {
		return accessKeySecret;
	}

	public String getOssBucketName() {
		return ossBucketName;
	}

	public String getBucketUrl() {
		return bucketUrl;
	}

	public String getDownFileTempdir() {
		return downFileTempdir;
	}

	public String getDownFilePacketdir() {
		return downFilePacketdir;
	}

	public String getFristFilePath() {
		return fristFilePath;
	}

	public void setFristFilePath(String fristFilePath) {
		this.fristFilePath = fristFilePath;
	}
}