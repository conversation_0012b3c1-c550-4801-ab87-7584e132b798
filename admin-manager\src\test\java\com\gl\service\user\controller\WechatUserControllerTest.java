package com.gl.service.user.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.user.service.WechatUserService;
import com.gl.service.user.vo.SysAreaTreeVo;
import com.gl.service.user.vo.WechatUserVo;
import com.gl.service.user.vo.dto.WechatUserDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * WechatUserController单元测试类
 * 测试微信用户控制器的所有端点，包括正面和负面场景
 * 
 * @author: Test
 * @date: 2024/12/19
 * @version: 1.0
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = com.gl.ManagerApplication.class)
@AutoConfigureMockMvc
@DisplayName("微信用户控制器单元测试")
class WechatUserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WechatUserService wechatUserService;

    @Autowired
    private ObjectMapper objectMapper;

    private Result successResult;
    private Result errorResult;
    private WechatUserDto mockDto;
    private List<WechatUserVo> mockUserList;
    private List<SysAreaTreeVo> mockAreaList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockDto = new WechatUserDto();
        mockDto.setGender(1);
        mockDto.setAddress("110000");
        mockDto.setSearchCondition("测试用户");
        mockDto.setBeginTime("2023-01-01");
        mockDto.setEndTime("2023-12-31");
        mockDto.setPageNumber(0);
        mockDto.setPageSize(10);

        WechatUserVo mockUser = new WechatUserVo();
        mockUser.setId(1L);
        mockUser.setOpenid("test_openid");
        mockUser.setNickname("测试用户");
        mockUser.setAvatar("http://test.com/avatar.jpg");
        mockUser.setGender(1);
        mockUser.setPhone("13800138000");
        mockUser.setArea("北京市");
        mockUser.setAuthTime(new Date());
        mockUser.setShopName("测试店铺");

        mockUserList = Arrays.asList(mockUser);

        SysAreaTreeVo mockArea = new SysAreaTreeVo();
        mockArea.setId(110000L);
        mockArea.setName("北京市");
        mockArea.setParentId(0L);
        mockArea.setChildren(new ArrayList<>());

        mockAreaList = Arrays.asList(mockArea);

        // 初始化成功结果
        successResult = Result.success();
        Map<String, Object> data = new HashMap<>();
        data.put("total", 1L);
        data.put("result", mockUserList);
        successResult.setData(data);

        // 初始化错误结果
        errorResult = Result.fail("服务器内部错误");
    }

    @Test
    @DisplayName("获取微信用户列表 - 成功场景，有权限")
    @WithMockUser(authorities = "user:user:list")
    void testList_Success_WithPermission() throws Exception {
        // Given
        when(wechatUserService.list(any(WechatUserDto.class), eq(1))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/user")
                .param("gender", "1")
                .param("address", "110000")
                .param("searchCondition", "测试用户")
                .param("beginTime", "2023-01-01")
                .param("endTime", "2023-12-31")
                .param("pageNumber", "0")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.result").isArray())
                .andExpect(jsonPath("$.data.result[0].id").value(1))
                .andExpect(jsonPath("$.data.result[0].nickname").value("测试用户"));

        verify(wechatUserService).list(any(WechatUserDto.class), eq(1));
    }

    @Test
    @DisplayName("获取微信用户列表 - 成功场景，无查询参数")
    @WithMockUser(authorities = "user:user:list")
    void testList_Success_NoParameters() throws Exception {
        // Given
        when(wechatUserService.list(any(WechatUserDto.class), eq(1))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/user")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        verify(wechatUserService).list(any(WechatUserDto.class), eq(1));
    }

    @Test
    @DisplayName("获取微信用户列表 - 权限拒绝场景")
    @WithMockUser(authorities = "other:permission")
    void testList_AccessDenied_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(get("/user")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(wechatUserService, never()).list(any(), any());
    }

    @Test
    @DisplayName("获取微信用户列表 - 未认证场景")
    void testList_Unauthorized_NotAuthenticated() throws Exception {
        // When & Then
        mockMvc.perform(get("/user")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        verify(wechatUserService, never()).list(any(), any());
    }

    @Test
    @DisplayName("获取微信用户列表 - 服务异常场景")
    @WithMockUser(authorities = "user:user:list")
    void testList_ServiceException() throws Exception {
        // Given
        when(wechatUserService.list(any(WechatUserDto.class), eq(1)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        mockMvc.perform(get("/user")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        verify(wechatUserService).list(any(WechatUserDto.class), eq(1));
    }

    @Test
    @DisplayName("导出微信用户列表 - 成功场景，有权限")
    @WithMockUser(authorities = "user:user:export")
    void testExportList_Success_WithPermission() throws Exception {
        // Given
        doNothing().when(wechatUserService).exportList(any(WechatUserDto.class), any(HttpServletResponse.class));

        // When & Then
        mockMvc.perform(post("/user/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockDto)))
                .andDo(print())
                .andExpect(status().isOk());

        verify(wechatUserService).exportList(any(WechatUserDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("导出微信用户列表 - 权限拒绝场景")
    @WithMockUser(authorities = "other:permission")
    void testExportList_AccessDenied_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(post("/user/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockDto)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(wechatUserService, never()).exportList(any(), any());
    }

    @Test
    @DisplayName("导出微信用户列表 - 未认证场景")
    void testExportList_Unauthorized_NotAuthenticated() throws Exception {
        // When & Then
        mockMvc.perform(post("/user/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockDto)))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        verify(wechatUserService, never()).exportList(any(), any());
    }

    @Test
    @DisplayName("导出微信用户列表 - IO异常场景")
    @WithMockUser(authorities = "user:user:export")
    void testExportList_IOException() throws Exception {
        // Given
        doThrow(new IOException("文件写入失败")).when(wechatUserService)
                .exportList(any(WechatUserDto.class), any(HttpServletResponse.class));

        // When & Then
        mockMvc.perform(post("/user/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockDto)))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        verify(wechatUserService).exportList(any(WechatUserDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("导出微信用户列表 - 请求体为空场景")
    @WithMockUser(authorities = "user:user:export")
    void testExportList_EmptyRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/user/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andDo(print())
                .andExpect(status().isOk());

        verify(wechatUserService).exportList(any(WechatUserDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("导出微信用户列表 - 无效JSON格式场景")
    @WithMockUser(authorities = "user:user:export")
    void testExportList_InvalidJsonFormat() throws Exception {
        // When & Then
        mockMvc.perform(post("/user/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(wechatUserService, never()).exportList(any(), any());
    }

    @Test
    @DisplayName("获取区域列表 - 成功场景，无搜索条件")
    @WithMockUser
    void testAreaList_Success_NoSearchCondition() throws Exception {
        // Given
        when(wechatUserService.areaList(null)).thenReturn(mockAreaList);

        // When & Then
        mockMvc.perform(get("/user/area")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(110000))
                .andExpect(jsonPath("$[0].name").value("北京市"))
                .andExpect(jsonPath("$[0].parentId").value(0))
                .andExpect(jsonPath("$[0].children").isArray());

        verify(wechatUserService).areaList(null);
    }

    @Test
    @DisplayName("获取区域列表 - 成功场景，有搜索条件")
    @WithMockUser
    void testAreaList_Success_WithSearchCondition() throws Exception {
        // Given
        String searchName = "北京";
        when(wechatUserService.areaList(searchName)).thenReturn(mockAreaList);

        // When & Then
        mockMvc.perform(get("/user/area")
                .param("name", searchName)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("北京市"));

        verify(wechatUserService).areaList(searchName);
    }

    @Test
    @DisplayName("获取区域列表 - 成功场景，空字符串搜索条件")
    @WithMockUser
    void testAreaList_Success_EmptySearchCondition() throws Exception {
        // Given
        when(wechatUserService.areaList("")).thenReturn(mockAreaList);

        // When & Then
        mockMvc.perform(get("/user/area")
                .param("name", "")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());

        verify(wechatUserService).areaList("");
    }

    @Test
    @DisplayName("获取区域列表 - 服务异常场景")
    @WithMockUser
    void testAreaList_ServiceException() throws Exception {
        // Given
        when(wechatUserService.areaList(any())).thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        mockMvc.perform(get("/user/area")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        verify(wechatUserService).areaList(any());
    }

    @Test
    @DisplayName("获取区域列表 - 返回空列表场景")
    @WithMockUser
    void testAreaList_EmptyResult() throws Exception {
        // Given
        when(wechatUserService.areaList(any())).thenReturn(Collections.emptyList());

        // When & Then
        mockMvc.perform(get("/user/area")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());

        verify(wechatUserService).areaList(any());
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 成功场景，已绑定")
    @WithMockUser
    void testCheckPhone_Success_PhoneBound() throws Exception {
        // Given
        Result phoneResult = Result.success(false); // false表示已绑定
        when(wechatUserService.checkPhone()).thenReturn(phoneResult);

        // When & Then
        mockMvc.perform(get("/user/checkPhone")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data").value(false));

        verify(wechatUserService).checkPhone();
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 成功场景，未绑定")
    @WithMockUser
    void testCheckPhone_Success_PhoneNotBound() throws Exception {
        // Given
        Result phoneResult = Result.success(true); // true表示未绑定
        when(wechatUserService.checkPhone()).thenReturn(phoneResult);

        // When & Then
        mockMvc.perform(get("/user/checkPhone")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data").value(true));

        verify(wechatUserService).checkPhone();
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 服务异常场景")
    @WithMockUser
    void testCheckPhone_ServiceException() throws Exception {
        // Given
        when(wechatUserService.checkPhone()).thenThrow(new RuntimeException("获取用户信息失败"));

        // When & Then
        mockMvc.perform(get("/user/checkPhone")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        verify(wechatUserService).checkPhone();
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 未认证场景")
    void testCheckPhone_Unauthorized_NotAuthenticated() throws Exception {
        // When & Then
        mockMvc.perform(get("/user/checkPhone")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        verify(wechatUserService, never()).checkPhone();
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 返回错误结果场景")
    @WithMockUser
    void testCheckPhone_ErrorResult() throws Exception {
        // Given
        Result errorResult = Result.fail("用户不存在");
        when(wechatUserService.checkPhone()).thenReturn(errorResult);

        // When & Then
        mockMvc.perform(get("/user/checkPhone")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("用户不存在"));

        verify(wechatUserService).checkPhone();
    }
}
