# 安装包管理模块单元测试

本目录包含了 InstallationPackageService 和 InstallationPackageController 的全面单元测试。

## 测试文件结构

```
src/test/java/com/gl/service/installationPackage/
├── service/
│   └── InstallationPackageServiceTest.java    # Service层单元测试
├── controller/
│   └── InstallationPackageControllerTest.java # Controller层单元测试
├── TestSuite.java                             # 测试套件
└── README.md                                  # 本文件
```

## 测试覆盖范围

### InstallationPackageServiceTest

- **测试方法数量**: 22 个测试用例
- **覆盖的公共方法**:
  - `list()` - 6 个测试用例
  - `addOrUpdate()` - 10 个测试用例
  - `delete()` - 6 个测试用例
- **测试场景**:
  - ✅ 正面场景（成功路径）
  - ✅ 负面场景（错误处理）
  - ✅ 输入验证
  - ✅ 边缘案例
  - ✅ 异常处理
  - ✅ 数据库操作模拟

### InstallationPackageControllerTest

- **测试方法数量**: 24 个测试用例
- **覆盖的公共方法**:
  - `list()` - 6 个测试用例
  - `addOrUpdate()` - 9 个测试用例
  - `delete()` - 9 个测试用例
- **测试场景**:
  - ✅ HTTP 请求处理
  - ✅ 权限验证
  - ✅ CSRF 保护
  - ✅ JSON 序列化/反序列化
  - ✅ 异常处理
  - ✅ 状态码验证

## 技术栈

- **测试框架**: JUnit 5
- **模拟框架**: Mockito
- **Web 测试**: Spring Boot Test + MockMvc
- **安全测试**: Spring Security Test

## 运行测试

### 运行所有测试

```bash
# Maven
mvn test -Dtest=com.gl.service.installationPackage.**

# Gradle
./gradlew test --tests "com.gl.service.installationPackage.*"
```

### 运行特定测试类

```bash
# Service层测试
mvn test -Dtest=InstallationPackageServiceTest

# Controller层测试
mvn test -Dtest=InstallationPackageControllerTest

# 运行测试套件
mvn test -Dtest=TestSuite
```

### 生成测试报告

```bash
# Maven Surefire报告
mvn surefire-report:report

# JaCoCo代码覆盖率报告
mvn jacoco:report
```

## 测试数据

测试使用模拟数据，不依赖真实数据库：

- 模拟的 InstallationPackage 实体
- 模拟的用户认证信息
- 模拟的数据库查询结果

## 测试最佳实践

1. **描述性命名**: 每个测试方法都有清晰的@DisplayName 注解
2. **Given-When-Then 结构**: 测试代码结构清晰
3. **独立性**: 每个测试用例相互独立
4. **全面断言**: 验证所有重要的返回值和副作用
5. **异常测试**: 覆盖各种异常情况
6. **边缘案例**: 测试空值、特殊字符等边缘情况

## 注意事项

1. 测试需要 Spring Boot 测试环境
2. Controller 测试需要 Spring Security 配置
3. 某些测试使用了 MockedStatic，需要 Mockito 3.4+版本
4. 测试中使用了@SuppressWarnings("unchecked")来处理泛型警告

## 维护建议

1. 当业务逻辑发生变化时，及时更新对应的测试用例
2. 新增功能时，同时编写相应的单元测试
3. 定期运行测试确保代码质量
4. 关注测试覆盖率报告，保持高覆盖率
