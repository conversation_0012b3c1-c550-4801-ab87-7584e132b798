package com.gl.service.opus.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @author: duanjinze
 * @date: 2022/11/10 15:06
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoiceWorkVo extends UserVo {
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 作品文字
     */
    private String content;

    /**
     * 语音包id
     */
    private Long voiceId;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 作品时长(秒)
     */
    private Integer voiceTime;

    /**
     * 语速
     */
    private Integer speed;

    /**
     * 语调
     */
    private Integer volume;


    /**
     * 音量
     */
    private Integer pitch = 0;


    /**
     * 背景音乐音量
     */
    private Integer backgroundMusicVolume;

    /**
     * 背景音乐id
     */
    private Long backgroundMusicId;

    /**
     * 音频采样率
     */
    private Integer sampleRate;

    /**
     * 所属用户id
     */
    private Long userId;

    /**
     * 0未删除 1已删除
     */
    private Integer delStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    //=================//
    /**
     * 语音路径
     */
    private String fileUrl;
    /**
     * 主播名称
     */
    private String anchorName;
    /**
     * 背景音乐
     */
    private String musicName;

    private String shopName;

    private Long shopId;

}
