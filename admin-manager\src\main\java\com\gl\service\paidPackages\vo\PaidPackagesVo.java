package com.gl.service.paidPackages.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaidPackagesVo extends UserVo {

    private Long id;
    private String name;

    private Integer dataNum;
    private Integer effectDay;

    private BigDecimal sellingPrice;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

}
