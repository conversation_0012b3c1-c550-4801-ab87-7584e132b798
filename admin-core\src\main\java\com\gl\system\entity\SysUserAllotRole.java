package com.gl.system.entity;

import com.gl.framework.entity.IdEntity;
import lombok.Data;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 用户可分配角色表实体
 */

@Entity
@Data
@Table(name = "sys_user_allot_role")
public class SysUserAllotRole extends IdEntity {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;

}
