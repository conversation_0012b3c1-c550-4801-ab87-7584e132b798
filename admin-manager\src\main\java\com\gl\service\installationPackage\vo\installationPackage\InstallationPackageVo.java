package com.gl.service.installationPackage.vo.installationPackage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class InstallationPackageVo extends UserVo {

    private Long id;
    private String versionName;
    private String remark;
    private Integer updateDeviceNum;
    private String packageUrl;

    private String deviceIds;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

}
