package com.gl.service.installationPackage.repository;

import com.gl.service.installationPackage.entity.InstallationPackageLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface InstallationPackageLogRepository extends JpaRepository<InstallationPackageLog,Long>, JpaSpecificationExecutor<InstallationPackageLog> {


    @Transactional
    @Modifying
    @Query(value = "delete from dub_installation_package_log  where package_id = ?1 ",nativeQuery = true)
    Integer deleteByPackageId(Long packageId);

}
