package com.gl.system.controller;

import com.gl.aspectj.annotation.Log;
import com.gl.aspectj.enums.BusinessType;
import com.gl.framework.interceptor.annotation.RepeatSubmit;
import com.gl.framework.web.response.Result;
import com.gl.system.service.SysAreaService;
import com.gl.system.vo.SysAreaVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行政区划管理控制器
 */
@RestController
@RequestMapping("/system/area")
public class SysAreaController {

    @Autowired
    private SysAreaService areaService;

    /**
     * 获取列表
     *
     * @param vo
     * @return
     */
    @GetMapping("/list")
    public List<SysAreaVo> list(SysAreaVo vo) {
        return areaService.list(vo);
    }

    /**
     * 获取列表
     *
     * @param parentId
     * @return
     */
    @GetMapping("/list/{parentId}")
    public List<SysAreaVo> listByParentId(@PathVariable long parentId) {
        return areaService.list(parentId);
    }

    /**
     * 获取列表
     *
     * @param keyword
     * @return
     */
    @GetMapping("/keyword")
    public List<SysAreaVo> listByKeyword(@RequestParam("parentId") Long parentId, @RequestParam("keyword") String keyword) {
        return areaService.list(parentId, keyword);
    }

    /**
     * 获取详细信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @PreAuthorize("@ps.hasPermi('system:area:query')")
    public SysAreaVo query(@PathVariable Long id) {
        return areaService.findById(id);
    }

    /**
     * 新增
     *
     * @param vo
     * @return
     */
    @RepeatSubmit
    @PostMapping
    @PreAuthorize("@ps.hasPermi('system:area:add')")
    @Log(title = "区域管理", businessType = BusinessType.INSERT, businessTypeName = "新增")
    public Result add(@Validated @RequestBody SysAreaVo vo) {
        areaService.save(vo);
        return Result.success();
    }

    /**
     * 修改菜单
     *
     * @param vo
     * @return
     */
    @PutMapping
    @PreAuthorize("@ps.hasPermi('system:area:edit')")
    @Log(title = "区域管理", businessType = BusinessType.UPDATE, businessTypeName = "修改")
    public Result edit(@Validated @RequestBody SysAreaVo vo) {
        areaService.update(vo);
        return Result.success();
    }

    /**
     * 删除菜单
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("@ps.hasPermi('system:area:remove')")
    @Log(title = "区域管理", businessType = BusinessType.DELETE, businessTypeName = "删除")
    public Result delete(@PathVariable Long id) {
        areaService.delete(id);
        return Result.success();
    }
}
