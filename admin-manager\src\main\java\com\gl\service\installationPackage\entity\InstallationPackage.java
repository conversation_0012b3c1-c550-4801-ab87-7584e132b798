package com.gl.service.installationPackage.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


@Data
@Entity
@Table(name = "dub_installation_package")
public class InstallationPackage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Basic
    @Column(name = "version_name")
    private String versionName;

    @Basic
    @Column(name = "remark")
    private String remark;

    @Basic
    @Column(name = "package_url")
    private String packageUrl;


    @Basic
    @Column(name = "create_time")
    private Date createTime;

    @Basic
    @Column(name = "create_user_id")
    private Long createUserId;

}
