package com.gl.service.opus.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-11-9:57
 */
public class StopUtils {
    public static String breakRename(String text) {
        // 正则表达式匹配<break time='...ms'/>
        Pattern pattern = Pattern.compile("<break time='(\\d+)ms'/>");
        Matcher matcher = pattern.matcher(text);

        StringBuffer sb = new StringBuffer(); // 使用StringBuffer来构建结果字符串

        while (matcher.find()) {
            // 提取毫秒数
            String msStr = matcher.group(1);
            // 转换为秒并保留一位小数
            double seconds = Double.parseDouble(msStr) / 1000.0;
            String formattedSeconds = String.format("%.1f", seconds);

            // 替换匹配到的<break time='...ms'/>为<break time='...s'/><break/>
            matcher.appendReplacement(sb, "<break time=\"" + formattedSeconds + "s\"></break>");
        }

        // 将剩余的部分（最后一部分没有匹配到的）添加到结果中
        matcher.appendTail(sb);
        sb.insert(0, "<speak>");
        sb.append("</speak>");
        return sb.toString();
    }

    public static String clearBreak(String text) {
        Pattern pattern = Pattern.compile("<break time='\\d+ms'/>");
        Matcher matcher = pattern.matcher(text);
        StringBuffer sb = new StringBuffer();
        text = matcher.replaceAll("");
        // 如果需要在文本外围添加<speak>和</speak>标签
        sb.append(text);
        return sb.toString();
    }

    public static int countBreak(String text) {
        // 编译正则表达式
        Pattern pattern = Pattern.compile("\\[停(\\d{3,5})ms\\]");

        // 创建matcher对象
        Matcher matcher = pattern.matcher(text);

        // 初始化计数器
        int count = 0;

        // 查找匹配项并计数
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    public static Integer getBreakTime(String text) {
        // 使用正则表达式匹配数字部分
        Pattern pattern = Pattern.compile("<break time='(\\d+)ms'/>");
        Matcher matcher = pattern.matcher(text);

        // 如果找到匹配项
        if (matcher.find()) {
            // 提取第一个捕获组（括号中的内容），即数字部分
            String timeValue = matcher.group(1);
            // 将字符串转换为整数并返回
            return Integer.parseInt(timeValue);
        }
        // 如果没有找到匹配项，返回null或者可以根据需求返回默认值
        return null;
    }

    public static List<String> parseTextWithBreaks(String input) {
        // 存储所有元素（文本和break标签）的列表
        List<String> elements = new ArrayList<>();

        // 定义匹配<break>标签的正则表达式
        Pattern breakPattern = Pattern.compile("<break time='\\d+ms'/>");
        Matcher matcher = breakPattern.matcher(input);

        // 用于跟踪当前处理位置
        int lastEnd = 0;

        // 查找所有的break标签
        while (matcher.find()) {
            // 获取break标签的起始位置和结束位置
            int start = matcher.start();
            int end = matcher.end();
            // 添加break标签前的文本（如果有）
            if (start > lastEnd) {
                String text = input.substring(lastEnd, start).trim();
                if (!text.isEmpty()) {
                    elements.add(text);
                }
            }
            // 添加break标签
            elements.add(matcher.group());
            lastEnd = end;
        }
        // 添加最后剩余的文本（如果有）
        if (lastEnd < input.length()) {
            String remainingText = input.substring(lastEnd).trim();
            if (!remainingText.isEmpty()) {
                elements.add(remainingText);
            }
        }
        return elements;
    }

    public static String splitWords(String input) {
        return input.trim().replaceAll("[,，、]+", "，").replaceAll("[ \\n]+", "，");
    }

    public static List<String> splitText(String text) {
        if (StringUtils.isEmpty(text)) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        // 按句号分割，但保留句号
        String[] sentences = text.split("(?<=\\，)");

        StringBuilder currentSegment = new StringBuilder();

        for (String sentence : sentences) {
            // 如果当前句子长度小于50
            if (sentence.length() < 50) {
                // 如果累积的文本加上当前句子仍小于50，继续累积
                if (currentSegment.length() + sentence.length() < 50) {
                    currentSegment.append(sentence);
                } else {
                    // 如果累积文本不为空，先添加到结果中
                    if (currentSegment.length() > 0) {
                        result.add(currentSegment.toString());
                        currentSegment = new StringBuilder();
                    }
                    currentSegment.append(sentence);
                }
            } else {
                // 如果累积文本不为空，先添加到结果中
                if (currentSegment.length() > 0) {
                    result.add(currentSegment.toString());
                    currentSegment = new StringBuilder();
                }
                // 长句子直接添加到结果中
                result.add(sentence);
            }
        }
        // 添加最后剩余的文本
        if (currentSegment.length() > 0) {
            result.add(currentSegment.toString());
        }
        return result;
    }
}
