package com.gl.service.shop.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2025/3/11
 * @description:
 */
@Data
public class ShopUpdatePerson {

    @NotNull(message = "关联用户不能为空")
    private Long userId;

    @NotNull(message = "门店不能为空")
    private Long shopId;


    // 1 设置管理员 2 解绑店员 3 添加店员
    @NotNull(message = "状态不能为空")
    private Long status;
}
