package com.gl.service.opus.repository;


import com.gl.service.opus.entity.AiStyle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-08-08-11:07
 */
public interface AiStyleRepository extends JpaRepository<AiStyle,Long>, JpaSpecificationExecutor<AiStyle> {
    List<AiStyle> findByDelStatus(int i);
}
