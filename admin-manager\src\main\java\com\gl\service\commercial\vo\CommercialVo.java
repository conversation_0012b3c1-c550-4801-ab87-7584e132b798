package com.gl.service.commercial.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gl.service.opus.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class CommercialVo extends UserVo {

    private Long id;
    private String avatar;
    private String nickname;
    private String phone;
    private Integer gender;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date authTime;
    private String shopNames;
    private String deviceNames;
    private Integer deviceNum;
}
