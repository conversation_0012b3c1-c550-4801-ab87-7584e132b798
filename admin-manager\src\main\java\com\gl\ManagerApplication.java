package com.gl;

import com.gl.service.websocket.config.NettyConfig;
import com.gl.service.websocket.server.WebSocketNettyServer;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.Resource;
import javax.persistence.EntityManager;

@EnableAsync
@EnableScheduling
@SpringBootApplication
public class ManagerApplication implements CommandLineRunner {
	private static final Logger log = LoggerFactory.getLogger(ManagerApplication.class);


	@Resource
	private WebSocketNettyServer webSocketNettyServer;

	@Resource
	private NettyConfig nettyConfig;

	public static void main(String[] args) {
		SpringApplication.run(ManagerApplication.class, args);
		log.info("                                                  \n" +
				 "(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ                     \n" +
				 "   _____ __  ______________________________       \n" +
				 "  / ___// / / / ____/ ____/ ____/ ___/ ___/       \n" +
				 "  \\__ \\/ / / / /   / /   / __/  \\__ \\\\__ \\  \n" +
				 " ___/ / /_/ / /___/ /___/ /___ ___/ /__/ /        \n" +
				 "/____/\\____/\\____/\\____/_____//____/____/      \n");
	}

	//让Spring管理JPAQueryFactory
	@Bean
	public JPAQueryFactory jpaQueryFactory(EntityManager entityManager) {
		return new JPAQueryFactory(entityManager);
	}

	@Override
	public void run(String... args) throws Exception {
		new Thread(new Runnable() {
			@Override
			public void run() {
				webSocketNettyServer.start(nettyConfig.getPort());
			}
		}).start();
	}
}
