package com.gl.framework.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 */
@Component
@ConfigurationProperties(prefix = "project")
public class ProjectProperties {
	/**
	 * 项目名称
	 */
	private String name;

	/**
	 * 版本
	 */
	private String version;

	/**
	 * 验证码类型: math 数组计算; char 字符验证
	 */
	private String captchaType;

	/**
	 * 获取地址开关
	 */
	private static boolean addressEnabled;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getCaptchaType() {
		return captchaType;
	}

	public void setCaptchaType(String captchaType) {
		this.captchaType = captchaType;
	}

	public static boolean isAddressEnabled() {
		return addressEnabled;
	}

	public void setAddressEnabled(boolean addressEnabled) {
		ProjectProperties.addressEnabled = addressEnabled;
	}

}
