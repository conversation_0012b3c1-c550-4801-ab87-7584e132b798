package com.gl.config.threadPool;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Version 1.0
 */
@Configuration
@EnableAsync
public class ThreadPoolCustomVoiceTaskConfig {
    @Bean
    @Qualifier("customVoiceTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor(){
        ThreadPoolTaskExecutor threadPool=new ThreadPoolTaskExecutor();
        threadPool.setCorePoolSize(8);
        threadPool.setQueueCapacity(2000);
        threadPool.setMaxPoolSize(32);
        threadPool.setKeepAliveSeconds(30);
        threadPool.setThreadNamePrefix("customVoice：");
        threadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return threadPool;
    }
}