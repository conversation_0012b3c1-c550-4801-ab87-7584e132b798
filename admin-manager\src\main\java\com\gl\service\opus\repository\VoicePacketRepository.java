package com.gl.service.opus.repository;

import com.gl.service.opus.entity.VoicePacket;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

public interface VoicePacketRepository extends JpaRepository<VoicePacket,Long>, JpaSpecificationExecutor<VoicePacket> {

    VoicePacket findByVoiceWorkId(Long voiceWorkId);

    @Transactional
    @Modifying
    Integer deleteByVoiceWorkId(Long voiceWorkId);
}
