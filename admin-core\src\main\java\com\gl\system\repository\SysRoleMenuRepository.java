package com.gl.system.repository;

import com.gl.system.entity.SysRoleMenu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SysRoleMenuRepository extends JpaRepository<SysRoleMenu, Long>, JpaSpecificationExecutor<SysRoleMenu> {

	/**
	 * 查询菜单是否存在角色
	 *
	 * @param menuId
	 * @return
	 */
	int countByMenuId(Long menuId);

	@Transactional
	void deleteByRoleId(Long roleId);

	@Transactional
	void deleteByRoleIdIn(List<Long> roleIds);

	@Transactional
	void deleteByMenuId(Long menuId);

	List<SysRoleMenu> findByRoleIdIn(List<Long> roleIds);

}
